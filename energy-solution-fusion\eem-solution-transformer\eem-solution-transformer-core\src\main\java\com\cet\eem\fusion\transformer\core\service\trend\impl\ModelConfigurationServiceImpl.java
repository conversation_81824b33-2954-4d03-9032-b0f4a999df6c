package com.cet.eem.fusion.transformer.core.service.trend.impl;

import com.cet.eem.auth.service.NodeManageWithAuthService;
import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.bll.common.model.domain.subject.energysaving.DeviceChain;
import com.cet.eem.bll.common.model.domain.subject.energysaving.DeviceChainDetail;
import com.cet.eem.bll.common.model.domain.subject.energysaving.DeviceOperationRule;
import com.cet.eem.bll.common.model.ext.modelentity.EemQueryCondition;
import com.cet.eem.bll.common.model.ext.subject.energysaving.DeviceChainWithSubLayer;
import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.bll.energysaving.dao.weather.DeviceChainDao;
import com.cet.eem.bll.energysaving.dao.weather.DeviceOperationRuleDao;
import com.cet.eem.bll.energysaving.dao.weather.RefrigeratingSystemDao;
import com.cet.eem.bll.energysaving.model.config.*;
import com.cet.eem.bll.energysaving.model.config.system.RefrigeratingSystem;
import com.cet.eem.bll.energysaving.model.weather.DeviceOperationType;
import com.cet.eem.bll.energysaving.model.weather.PumpVo;
import com.cet.eem.bll.energysaving.service.trend.ModelConfigurationService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.model.base.ConditionBlock;
import com.cet.eem.model.base.ConditionBlockCompose;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.base.SingleModelConditionDTO;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import com.cet.electric.modelservice.common.entity.IdTextPair;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : AIPredictServiceImpl
 * @Description : ai预测service
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-16 14:50
 */
@Service
public class ModelConfigurationServiceImpl implements ModelConfigurationService {
    @Autowired
    RefrigeratingSystemDao refrigeratingSystemDao;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    DeviceOperationRuleDao deviceOperationRuleDao;
    public final static Integer FIRST = 1;
    public final static Integer SECOND = 2;
    public final static Integer THIRD = 3;
    public final static Integer FOUR = 4;
    public final static String COLD_WATER = "冷水机组";
    public final static String FREEZING_WATER_PUMP = "冷冻水泵";
    public final static String COLD_WATER_PUMP = "冷却水泵";
    public final static String COLD_TOWER = "冷却塔风机";
    private final static String TYPE_PUMP = "functiontype";
    private final static String DEVICE_CLASS = "deviceclass";
    private final static String CHANGER = "plateheatexchanger";
    @Value("${cet.eem.coldOptimization.com.cet.eem.emailsend.model-config.temp:6.5}")
    private Double temp;
    @Autowired
    DeviceChainDao deviceChainDao;
    @Autowired
    NodeDao nodeDao;
    @Autowired
    protected NodeManageWithAuthService nodeManageBffService;

    @Override
    public List<RefrigeratingSystemVo> queryAiUseSystem(Long projectId,Long userId) {
        List<Map<String, Object>> maps = nodeManageBffService.queryNodeTree(queryCondition(projectId), userId);
        List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.querySystem(projectId);
        refrigeratingSystems = filterSystem(maps, refrigeratingSystems);
        List<BaseVo> collect = refrigeratingSystems.stream().map(refrigeratingSystem -> new BaseVo(refrigeratingSystem.getRoomId(), NodeLabelDef.ROOM)).collect(Collectors.toList());
        List<BaseVo> baseVos = nodeDao.queryNodes(collect, BaseVo.class);
        List<RefrigeratingSystemVo> result = new ArrayList<>();
        for (RefrigeratingSystem refrigeratingSystem : refrigeratingSystems) {
            RefrigeratingSystemVo item = new RefrigeratingSystemVo();
            result.add(item);
            BeanUtils.copyProperties(refrigeratingSystem, item);
            List<BaseVo> nodes = baseVos.stream().filter(baseVo -> Objects.equals(baseVo.getId(), refrigeratingSystem.getRoomId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(nodes)) {
                item.setName(nodes.get(0).getName());
            }
        }

        return result.stream().filter(refrigeratingSystemVo -> Objects.nonNull(refrigeratingSystemVo.getName())).collect(Collectors.toList());
    }

    private List<RefrigeratingSystem> filterSystem(List<Map<String, Object>> maps, List<RefrigeratingSystem> refrigeratingSystems) {
        List<BaseVo> baseVos = JsonTransferUtils.transferList(maps, BaseVo.class);
        List<BaseVo> rooms = new ArrayList<>();
        for (BaseVo baseVo : baseVos) {
            if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                rooms.addAll(baseVo.getChildren());
            }
        }
        if (CollectionUtils.isNotEmpty(rooms)) {
            Set<Long> ids = rooms.stream().map(BaseVo::getId).collect(Collectors.toSet());
            return refrigeratingSystems.stream().filter(refrigeratingSystem -> ids.contains(refrigeratingSystem.getRoomId()))
                    .collect(Collectors.toList());
        }
        return refrigeratingSystems;
    }

    /**
     * 查询空调机房
     *
     * @return
     */
    private EemQueryCondition queryCondition(Long id) {
        EemQueryCondition eemQueryCondition = new EemQueryCondition();
        eemQueryCondition.setRootLabel(NodeLabelDef.PROJECT);
        eemQueryCondition.setRootID(id);
        eemQueryCondition.setTreeReturnEnable(true);
        SingleModelConditionDTO singleModelConditionDTO = new SingleModelConditionDTO();
        singleModelConditionDTO.setModelLabel(NodeLabelDef.ROOM);
        ConditionBlockCompose compose = new ConditionBlockCompose();
        ConditionBlock conditionBlock = new ConditionBlock(ColumnDef.ROOM_TYPE, ConditionBlock.OPERATOR_EQ, 3);
        compose.setExpressions(Collections.singletonList(conditionBlock));
        singleModelConditionDTO.setFilter(compose);
        eemQueryCondition.setSubLayerConditions(Collections.singletonList(singleModelConditionDTO));
        return eemQueryCondition;
    }

    @Override
    public List<RefrigeratingSystemVo> queryAiUseSystemByRoomId(Long id) {
        List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.querySystemByRoomId(id,GlobalInfoUtils.getProjectId());
        List<BaseVo> baseVos = nodeDao.queryNodes(Collections.singletonList(new BaseVo(id, NodeLabelDef.ROOM)), BaseVo.class);
        List<RefrigeratingSystemVo> result = new ArrayList<>();
        for (RefrigeratingSystem refrigeratingSystem : refrigeratingSystems) {
            RefrigeratingSystemVo item = new RefrigeratingSystemVo();
            result.add(item);
            BeanUtils.copyProperties(refrigeratingSystem, item);
            List<BaseVo> nodes = baseVos.stream().filter(baseVo -> Objects.equals(baseVo.getId(), refrigeratingSystem.getRoomId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(nodes)) {
                item.setName(nodes.get(0).getName());
            }
        }

        return result;
    }

    @Override
    public void writeOrUpdateAiUseSystem(List<Long> ids) {

        List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.querySystemByIds(ids,GlobalInfoUtils.getProjectId());
        List<RefrigeratingSystem> totalSys = refrigeratingSystemDao.querySystem(GlobalInfoUtils.getProjectId());
        List<RefrigeratingSystem> updateList = new ArrayList<>();
        List<RefrigeratingSystem> insertList = new ArrayList<>();
        for (Long id : ids) {
            RefrigeratingSystem item = new RefrigeratingSystem();
            List<RefrigeratingSystem> update = refrigeratingSystems.stream().filter(refrigeratingSystem -> Objects.equals(refrigeratingSystem.getRoomId(), id)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(update)) {
                BeanUtils.copyProperties(update.get(0), item);
                item.setUseAi(true);
                updateList.add(item);
            } else {
                item.setProjectId(GlobalInfoUtils.getProjectId());
                item.setRoomId(id);
                item.setPlateHeaTexChangerMinTemp(temp);
                item.setUseAi(true);
                insertList.add(item);
            }
        }
        setAiUseFalse(totalSys, ids, updateList);
        modelServiceUtils.writeData(updateList);
        modelServiceUtils.writeData(insertList);
    }

    @Override
    public void setOrUpdateTemp(Long id, Double temp,Double outsideTempLimit,Double coolingLoadDemandLimit) {
        List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.querySystemByIds(Collections.singletonList(id),GlobalInfoUtils.getProjectId());
        if (CollectionUtils.isEmpty(refrigeratingSystems)) {
            return;
        }
        RefrigeratingSystem refrigeratingSystem = refrigeratingSystems.get(0);
        refrigeratingSystem.setPlateHeaTexChangerMinTemp(temp);
        refrigeratingSystem.setOutsideTempLimit(outsideTempLimit);
        refrigeratingSystem.setCoolingLoadDemandLimit(coolingLoadDemandLimit);
        modelServiceUtils.writeData(Collections.singletonList(refrigeratingSystem));
    }

    @Override
    public void writeOrUpdate(ModelConfigurationParam modelConfigurationParam) {
        List<DeviceOperationRule> deviceOperationRules = deviceOperationRuleDao.queryRule(modelConfigurationParam.getRoomId(), modelConfigurationParam.getType(),GlobalInfoUtils.getProjectId());
        if (CollectionUtils.isEmpty(deviceOperationRules)) {
            List<DeviceOperationRule> insert = insertRule(modelConfigurationParam);
            modelServiceUtils.writeData(insert);
        } else {
            List<DeviceOperationRule> update = new ArrayList<>();
            List<DeviceOrTimeParam> deviceList = modelConfigurationParam.getDeviceList();
            List<DeviceOrTimeParam> timeList = modelConfigurationParam.getTimeList();
            Map<Integer, List<DeviceOrTimeParam>> timeSortMap = timeList.stream().collect(Collectors.groupingBy(DeviceOrTimeParam::getSort));
            Map<Integer, List<DeviceOrTimeParam>> deviceSortMap = deviceList.stream().collect(Collectors.groupingBy(DeviceOrTimeParam::getSort));
            for (DeviceOperationRule deviceOperationRule : deviceOperationRules) {
                Integer sort = deviceOperationRule.getSort();
                List<DeviceOrTimeParam> deviceOrTimeParams = deviceSortMap.get(sort);
                deviceOperationRule.setObjectLabel(deviceOrTimeParams.get(0).getObjectLabel());
                if (!Objects.equals(sort, FIRST)) {
                    deviceOperationRule.setPreMinRunningTime(CommonUtils.calcLong(timeSortMap.get(sort - FIRST).get(0).getTime(), TimeUtil.MINUTE / TimeUtil.SECOND, EnumOperationType.MULTIPLICATION.getId()));
                }
                update.add(deviceOperationRule);
            }
            modelServiceUtils.writeData(update);
        }
    }

    @Override
    public Map<String, Object> insertDeviceChain(DeviceChainParam deviceChainParam) {
        checkDeviceChainName(deviceChainParam.getChainName(), deviceChainParam.getRoomId(), deviceChainParam.getChainId());
        List<NodeWithSort> nodes = deviceChainParam.getNodes();
        //新增连锁，先新建连锁，再写入细节
        if (Objects.isNull(deviceChainParam.getChainId())) {
            DeviceChain deviceChain = new DeviceChain(deviceChainParam.getChainName(), deviceChainParam.getRoomId(), GlobalInfoUtils.getProjectId());
            List<DeviceChain> deviceChains = modelServiceUtils.writeData(deviceChain, DeviceChain.class);
            List<DeviceChainDetail> deviceChainDetails = transParamToDetail(nodes);
            deviceChainDao.insertChild(deviceChains.get(0).getId(), deviceChainDetails);
            return deviceChainDao.selectRelatedTreeById(DeviceChainWithSubLayer.class, deviceChains.get(0).getId());
        } else {
            //编辑连锁还要考虑会删掉设备的情况，比如之前连锁写入的是4个设备，下次编辑变成3个，要把多余的删掉。
            DeviceChainWithSubLayer deviceChainWithSubLayer = deviceChainDao.selectRelatedById(DeviceChainWithSubLayer.class, deviceChainParam.getChainId());
            if (Objects.isNull(deviceChainWithSubLayer)) {
                return Collections.emptyMap();
            }
            List<DeviceChainDetail> deviceChainDetails = updateDetail(nodes, deviceChainWithSubLayer);
            deviceChainDao.insertChild(deviceChainWithSubLayer.getId(), deviceChainDetails);
            if (CollectionUtils.isNotEmpty(deviceChainDetails) && CollectionUtils.isNotEmpty(deviceChainWithSubLayer.getDeviceChainDetails()) && deviceChainDetails.size() < deviceChainWithSubLayer.getDeviceChainDetails().size()) {
                deleteRedundantDeviceChainDetail(deviceChainWithSubLayer, deviceChainDetails);
            }
            if (CollectionUtils.isEmpty(deviceChainParam.getNodes())) {
                //删除掉所有设备
                deviceChainDao.deleteChild(deviceChainWithSubLayer.getId(), deviceChainWithSubLayer.getDeviceChainDetails());
            }
            DeviceChain deviceChain = new DeviceChain();
            deviceChain.setId(deviceChainWithSubLayer.getId());
            deviceChain.setName(deviceChainParam.getChainName());
            deviceChain.setRoomId(deviceChainWithSubLayer.getRoomId());
            deviceChain.setProjectId(deviceChainWithSubLayer.getProjectId());
            modelServiceUtils.writeData(Collections.singletonList(deviceChain));
            return deviceChainDao.selectRelatedTreeById(DeviceChainWithSubLayer.class, deviceChainWithSubLayer.getId());
        }
    }

    private void checkDeviceChainName(String name, Long roomId, Long chainId) {
        DeviceChain deviceChains = deviceChainDao.queryDeivceChain(roomId, name, chainId,GlobalInfoUtils.getProjectId());
        Assert.isNull(deviceChains, "存在同名的连锁！");
    }

    /**
     * 删掉多余的连锁详情
     *
     * @param deviceChainWithSubLayer
     * @param deviceChainDetails
     */
    private void deleteRedundantDeviceChainDetail(DeviceChainWithSubLayer deviceChainWithSubLayer, List<DeviceChainDetail> deviceChainDetails) {
        if (CollectionUtils.isEmpty(deviceChainDetails)) {
            deviceChainDao.deleteChild(deviceChainWithSubLayer.getId(), deviceChainWithSubLayer.getDeviceChainDetails());
        }
        List<DeviceChainDetail> details = deviceChainWithSubLayer.getDeviceChainDetails();
        List<DeviceChainDetail> delete = new ArrayList<>();
        for (DeviceChainDetail item : details) {
            List<DeviceChainDetail> collect = deviceChainDetails.stream().filter(deviceChainDetail -> Objects.equals(item.getSort(), deviceChainDetail.getSort())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                delete.add(item);
            }
        }
        deviceChainDao.deleteChild(deviceChainWithSubLayer.getId(), delete);
    }

    @Override
    public List<DeviceChainParam> queryAllDeviceChain(Long roomId,Long projectId) {
        List<DeviceChain> deviceChains = deviceChainDao.queryDeviceChain(roomId,projectId);
        if (CollectionUtils.isEmpty(deviceChains)) {
            return Collections.emptyList();
        }
        List<Long> ids = deviceChains.stream().map(DeviceChain::getId).collect(Collectors.toList());
        LambdaQueryWrapper<DeviceChain> wrapper = LambdaQueryWrapper.of(DeviceChain.class);
        wrapper.in(DeviceChain::getId, ids)
                .eq(DeviceChain::getProjectId, projectId);
        List<DeviceChainWithSubLayer> deviceChainWithSubLayers = deviceChainDao.selectRelatedList(DeviceChainWithSubLayer.class, wrapper);

        return transToVo(deviceChainWithSubLayers);
    }

    private List<DeviceChainParam> transToVo(List<DeviceChainWithSubLayer> deviceChainWithSubLayers) {
        List<DeviceChainParam> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(deviceChainWithSubLayers)) {
            return result;
        }
        List<BaseVo> childs = deviceChainWithSubLayers.stream().filter(deviceChainWithSubLayer -> CollectionUtils.isNotEmpty(deviceChainWithSubLayer.getDeviceChainDetails())).flatMap(deviceChainWithSubLayer -> deviceChainWithSubLayer.getDeviceChainDetails().stream()).map(deviceChainDetail -> new BaseVo(deviceChainDetail.getObjectId(), deviceChainDetail.getObjectLabel())).collect(Collectors.toList());
        List<BaseVo> baseVos = nodeDao.queryNodes(childs, BaseVo.class);
        for (DeviceChainWithSubLayer item : deviceChainWithSubLayers) {
            DeviceChainParam deviceChainParam = new DeviceChainParam();
            result.add(deviceChainParam);
            deviceChainParam.setChainId(item.getId());
            deviceChainParam.setRoomId(item.getRoomId());
            deviceChainParam.setChainName(item.getName());
            if (CollectionUtils.isEmpty(item.getDeviceChainDetails())) {
                continue;
            }
            List<NodeWithSort> nodes = new ArrayList<>();
            for (DeviceChainDetail detail : item.getDeviceChainDetails()) {
                List<BaseVo> collect = baseVos.stream().filter(baseVo -> Objects.equals(detail.getObjectId(), baseVo.getId()) && Objects.equals(detail.getObjectLabel(), baseVo.getModelLabel())).collect(Collectors.toList());
                NodeWithSort node = new NodeWithSort();
                node.setId(detail.getObjectId());
                node.setModelLabel(detail.getObjectLabel());
                node.setSort(detail.getSort());
                if (CollectionUtils.isNotEmpty(collect)) {
                    node.setName(collect.get(0).getName());
                }
                nodes.add(node);
            }
            deviceChainParam.setNodes(nodes);

        }
        return result;
    }

    @Override
    public List<PumpVo> queryChainDevice(Long rooId) {
        //查询冷水主机，两个类型的管道，板式换热器，冷却塔
        SingleModelConditionDTO singleModelConditionDTO = new SingleModelConditionDTO(NodeLabelDef.PUMP);
        List<ConditionBlock> filters = new ArrayList<>();
        filters.add(new ConditionBlock(TYPE_PUMP, ConditionBlock.OPERATOR_IN, Arrays.asList(PumpFunctionType.COOLING_PUMP, PumpFunctionType.REFRIGERATING_PUMP)));
        singleModelConditionDTO.setFilter(new ConditionBlockCompose(filters));
        SingleModelConditionDTO singleModelConditionDTO1 = new SingleModelConditionDTO(NodeLabelDef.COLD_WATER_MAINENGINE);
        SingleModelConditionDTO singleModelConditionDTO2 = new SingleModelConditionDTO(NodeLabelDef.COOLING_TOWER);
        SingleModelConditionDTO singleModelConditionDTO3 = new SingleModelConditionDTO(CHANGER);
        QueryCondition condition = new QueryConditionBuilder<>(NodeLabelDef.ROOM, rooId)
                .leftJoinCondition(Arrays.asList(singleModelConditionDTO, singleModelConditionDTO1, singleModelConditionDTO2, singleModelConditionDTO3))
                .queryAsTree()
                .build();
        List<PumpVo> query = modelServiceUtils.query(condition, PumpVo.class);
        List<PumpVo> result = new ArrayList<>();
        for (PumpVo baseVo : query) {
            if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                result.addAll(baseVo.getChildren());
            }
        }
        return result;


    }

    @Override
    public List<ModelConfigVo> queryDeviceStartAndStop(Long roomId) {
        List<ModelConfigVo> result = new ArrayList<>();
        List<DeviceOperationRule> deviceOperationRules = deviceOperationRuleDao.queryRule(roomId, null,
                GlobalInfoUtils.getProjectId());
//        List<IdTextPair> idTextPairs = dataService.queryEnumeration(DEVICE_CLASS);
        Map<Integer, IdTextPair> enumeration = modelServiceUtils.getEnumeration(DEVICE_CLASS);
        Collection<IdTextPair> idTextPairs = enumeration.values();
        Map<Integer, List<DeviceOperationRule>> collect = deviceOperationRules.stream().collect(Collectors.groupingBy(DeviceOperationRule::getDeviceOperationType));
        for (Map.Entry<Integer, List<DeviceOperationRule>> entry : collect.entrySet()) {
            Integer key = entry.getKey();
            List<DeviceOperationRule> value = entry.getValue();
            ModelConfigVo modelConfigVo = new ModelConfigVo();
            modelConfigVo.setRoomId(roomId);
            modelConfigVo.setType(key);
            List<DeviceOrTimeVo> deviceList = new ArrayList<>();
            List<DeviceOrTimeVo> timeList = new ArrayList<>();
            for (DeviceOperationRule rule : value) {
                if (Objects.nonNull(rule.getPreMinRunningTime())) {
                    DeviceOrTimeVo deviceOrTimeVo = new DeviceOrTimeVo();
                    deviceOrTimeVo.setTime(rule.getPreMinRunningTime());
                    deviceOrTimeVo.setSort(rule.getSort() - 1);
                    timeList.add(deviceOrTimeVo);
                }
                DeviceOrTimeVo deviceOrTimeVo = new DeviceOrTimeVo();
                deviceOrTimeVo.setObjectLabel(rule.getObjectLabel());
                deviceOrTimeVo.setObjectLabelName(getObjectName(new ArrayList<>(idTextPairs), rule.getObjectLabel()));
                deviceOrTimeVo.setSort(rule.getSort());
                deviceList.add(deviceOrTimeVo);

            }
            modelConfigVo.setDeviceList(deviceList);
            modelConfigVo.setTimeList(timeList);
            result.add(modelConfigVo);
        }
        return result;
    }

    @Override
    public void deleteChain(List<Long> chainIds) {
        List<DeviceChainWithSubLayer> deviceChainWithSubLayers = deviceChainDao.queryDeviceChainWithDetail(chainIds,GlobalInfoUtils.getProjectId());
        for (DeviceChainWithSubLayer deviceChainWithSubLayer : deviceChainWithSubLayers) {
            deviceChainDao.deleteChild(deviceChainWithSubLayer.getId(), deviceChainWithSubLayer.getDeviceChainDetails());
        }
        deviceChainDao.deleteBatchIds(chainIds);

    }

    @Override
    public List<DeviceChainParam> queryDeviceChainWithDetail(Long chainId, Long projectID) {
        List<DeviceChainWithSubLayer> deviceChainWithSubLayers = deviceChainDao.queryDeviceChainWithDetail(Collections.singletonList(chainId), projectID);
        List<DeviceChainParam> deviceChainParams = new ArrayList<>();
        for (DeviceChainWithSubLayer chain : deviceChainWithSubLayers) {
            DeviceChainParam param = new DeviceChainParam();
            param.setRoomId(chain.getRoomId());
            param.setChainId(chain.getId());
            param.setChainName(chain.getName());
            deviceChainParams.add(param);
            if (CollectionUtils.isEmpty(chain.getDeviceChainDetails())) {
                continue;
            }
            List<NodeWithSort> nodes = new ArrayList<>();
            param.setNodes(nodes);
            for (DeviceChainDetail detail : chain.getDeviceChainDetails()) {
                NodeWithSort withSort = new NodeWithSort();
                withSort.setSort(detail.getSort());
                withSort.setModelLabel(detail.getObjectLabel());
                withSort.setId(detail.getObjectId());
                nodes.add(withSort);
            }
        }
        return deviceChainParams;
    }

    private DeviceOperationRule createRule(Integer sort, DeviceWithTimeParam firstStart, StartAndStopParam startAndStopParam, Integer type) {
        return new DeviceOperationRule(sort, GlobalInfoUtils.getProjectId(), firstStart.getObjectLabel()
                , type, firstStart.getFunctionType(), CommonUtils.calcLong(firstStart.getTime(), TimeUtil.MINUTE / TimeUtil.SECOND,
                EnumOperationType.MULTIPLICATION.getId()), startAndStopParam.getRoomId());
    }

    private List<DeviceOperationRule> insertOperationRule(StartAndStopParam startAndStopParam) {
        List<DeviceOperationRule> insert = new ArrayList<>();
        insert.add(createRule(FIRST, startAndStopParam.getFirstStart(), startAndStopParam, DeviceOperationType.START));
        insert.add(createRule(SECOND, startAndStopParam.getSecondStart(), startAndStopParam, DeviceOperationType.START));
        insert.add(createRule(THIRD, startAndStopParam.getThirdStart(), startAndStopParam, DeviceOperationType.START));
        insert.add(createRule(FOUR, startAndStopParam.getLastStart(), startAndStopParam, DeviceOperationType.START));
        insert.add(createRule(FIRST, startAndStopParam.getFirstStop(), startAndStopParam, DeviceOperationType.STOP));
        insert.add(createRule(SECOND, startAndStopParam.getSecondStop(), startAndStopParam, DeviceOperationType.STOP));
        insert.add(createRule(THIRD, startAndStopParam.getThirdStop(), startAndStopParam, DeviceOperationType.STOP));
        insert.add(createRule(FOUR, startAndStopParam.getLastStop(), startAndStopParam, DeviceOperationType.STOP));


        return insert;
    }

    @Override
    public void writeStartAndStopOperationRule(StartAndStopParam startAndStopParam) {
        List<DeviceOperationRule> deviceOperationRules = deviceOperationRuleDao.queryRule(startAndStopParam.getRoomId(), null,GlobalInfoUtils.getProjectId());
        List<DeviceOperationRule> insert = insertOperationRule(startAndStopParam);
        if (CollectionUtils.isNotEmpty(deviceOperationRules)) {
            for (DeviceOperationRule rule : insert) {
                List<DeviceOperationRule> same = deviceOperationRules.stream().filter(it -> Objects.equals(it.getSort(), rule.getSort())
                        && Objects.equals(it.getDeviceOperationType(), rule.getDeviceOperationType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(same)) {
                    rule.setId(same.get(0).getId());
                }
            }
        }
        modelServiceUtils.writeData(insert);
    }

    private String setDeviceName(Integer functionType, String objectLabel) {
        if (Objects.equals(objectLabel, NodeLabelDef.COLD_WATER_MAINENGINE)) {
            return COLD_WATER;
        } else if (Objects.equals(objectLabel, NodeLabelDef.COOLING_TOWER)) {
            return COLD_TOWER;
        } else if (Objects.equals(objectLabel, NodeLabelDef.PUMP)) {
            if (Objects.equals(functionType, PumpFunctionType.REFRIGERATING_PUMP)) {
                return FREEZING_WATER_PUMP;
            } else if (Objects.equals(functionType, PumpFunctionType.COOLING_PUMP)) {
                return COLD_WATER_PUMP;
            }
        }
        return null;
    }

    @Override
    public StartAndStopVo queryStartAndStopOperationRule(Long roomId) {
        StartAndStopVo startAndStopVo = new StartAndStopVo();
        List<DeviceOperationRule> deviceOperationRules = deviceOperationRuleDao.queryRule(roomId, null,
                GlobalInfoUtils.getProjectId());
        if (CollectionUtils.isEmpty(deviceOperationRules)) {
            return startAndStopVo;
        }
        Map<Integer, List<DeviceOperationRule>> collect = deviceOperationRules.stream().collect(Collectors.groupingBy(DeviceOperationRule::getDeviceOperationType));
        for (Map.Entry<Integer, List<DeviceOperationRule>> entry : collect.entrySet()) {

            List<DeviceOperationRule> value = entry.getValue().stream().sorted(Comparator.comparing(DeviceOperationRule::getSort)).collect(Collectors.toList());
            if (Objects.equals(entry.getKey(), DeviceOperationType.START)) {
                startAndStopVo.setFirstStart(createDeviceOrTimeVo(FIRST, value));
                startAndStopVo.setSecondStart(createDeviceOrTimeVo(SECOND, value));
                startAndStopVo.setThirdStart(createDeviceOrTimeVo(THIRD, value));
                startAndStopVo.setLastStart(createDeviceOrTimeVo(FOUR, value));
            } else {
                startAndStopVo.setFirstStop(createDeviceOrTimeVo(FIRST, value));
                startAndStopVo.setSecondStop(createDeviceOrTimeVo(SECOND, value));
                startAndStopVo.setThirdStop(createDeviceOrTimeVo(THIRD, value));
                startAndStopVo.setLastStop(createDeviceOrTimeVo(FOUR, value));
            }
        }
        return startAndStopVo;
    }

    private DeviceOrTimeVo createDeviceOrTimeVo(Integer sort, List<DeviceOperationRule> value) {
        if (CollectionUtils.isNotEmpty(value) && value.size() >= sort) {
            DeviceOperationRule rule = value.get(sort - 1);
            String s = setDeviceName(rule.getFunctionType(), rule.getObjectLabel());
            return new DeviceOrTimeVo(s, rule.getObjectLabel(), CommonUtils.calcLong(rule.getPreMinRunningTime(), TimeUtil.MINUTE / TimeUtil.SECOND,
                    EnumOperationType.DIVISION.getId()), rule.getFunctionType());
        }
        return new DeviceOrTimeVo();
    }

    private String getObjectName(List<IdTextPair> enumItems, String objectLabel) {
        List<IdTextPair> collect = enumItems.stream().filter(enumItem -> Objects.equals(enumItem.getPropertyLabel(), objectLabel)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            return collect.get(0).getText();
        }
        return null;
    }

    private List<DeviceChainDetail> updateDetail(List<NodeWithSort> nodes, DeviceChainWithSubLayer deviceChainWithSubLayer) {
        if (Objects.isNull(deviceChainWithSubLayer)) {
            return Collections.emptyList();
        }
        List<DeviceChainDetail> deviceChainDetails;
        if (CollectionUtils.isEmpty(deviceChainWithSubLayer.getDeviceChainDetails())) {
            deviceChainDetails = new ArrayList<>();
        } else {
            deviceChainDetails = deviceChainWithSubLayer.getDeviceChainDetails();
        }

        List<DeviceChainDetail> result = new ArrayList<>();
        for (NodeWithSort node : nodes) {
            List<DeviceChainDetail> same = deviceChainDetails.stream().filter(deviceChainDetail -> Objects.equals(deviceChainDetail.getSort(), node.getSort())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(same)) {
                DeviceChainDetail detail = same.get(0);
                detail.setObjectLabel(node.getModelLabel());
                detail.setObjectId(node.getId());
                result.add(detail);
            } else {
                DeviceChainDetail detail = new DeviceChainDetail(node.getId(), node.getModelLabel(), node.getSort());
                result.add(detail);
            }
        }
        return result;
    }

    private List<DeviceChainDetail> transParamToDetail(List<NodeWithSort> nodes) {
        List<DeviceChainDetail> result = new ArrayList<>();
        for (NodeWithSort node : nodes) {
            DeviceChainDetail detail = new DeviceChainDetail();
            result.add(detail);
            detail.setObjectLabel(node.getModelLabel());
            detail.setObjectId(node.getId());
            detail.setSort(node.getSort());
        }
        return result;
    }

    private List<DeviceOperationRule> insertRule(ModelConfigurationParam modelConfigurationParam) {
        List<DeviceOperationRule> result = new ArrayList<>();
        List<DeviceOrTimeParam> deviceList = modelConfigurationParam.getDeviceList();
        List<DeviceOrTimeParam> timeList = modelConfigurationParam.getTimeList();
        Map<Integer, List<DeviceOrTimeParam>> timeSortMap = timeList.stream().collect(Collectors.groupingBy(DeviceOrTimeParam::getSort));
        for (DeviceOrTimeParam item : deviceList) {
            DeviceOperationRule deviceOperationRule = new DeviceOperationRule();
            Integer sort = item.getSort();
            if (!Objects.equals(FIRST, sort)) {
                deviceOperationRule.setPreMinRunningTime(CommonUtils.calcLong(timeSortMap.get(sort - FIRST).get(0).getTime(), TimeUtil.MINUTE / TimeUtil.SECOND, EnumOperationType.MULTIPLICATION.getId()));
            }
            deviceOperationRule.setSort(sort);
            deviceOperationRule.setDeviceOperationType(modelConfigurationParam.getType());
            deviceOperationRule.setObjectLabel(item.getObjectLabel());
            deviceOperationRule.setRoomId(modelConfigurationParam.getRoomId());
            deviceOperationRule.setProjectId(GlobalInfoUtils.getProjectId());
            result.add(deviceOperationRule);
        }
        return result;
    }

    /**
     * 已经存在但是调用此接口没有传入的id对应的系统不需要启用预测
     *
     * @param refrigeratingSystems
     * @param ids
     * @param updateList
     */
    private void setAiUseFalse(List<RefrigeratingSystem> refrigeratingSystems, List<Long> ids, List<RefrigeratingSystem> updateList) {
        List<Long> collect = refrigeratingSystems.stream().map(RefrigeratingSystem::getRoomId).distinct().collect(Collectors.toList());
        collect.removeAll(ids);
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }

        for (Long id : collect) {
            RefrigeratingSystem item = new RefrigeratingSystem();
            List<RefrigeratingSystem> update = refrigeratingSystems.stream().filter(refrigeratingSystem -> Objects.equals(refrigeratingSystem.getRoomId(), id)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(update)) {
                BeanUtils.copyProperties(update.get(0), item);
                item.setUseAi(false);
                updateList.add(item);
            }
        }

    }
}