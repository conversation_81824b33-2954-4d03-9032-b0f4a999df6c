package com.cet.eem.fusion.transformer.core.service.aioptimization;

import com.cet.eem.bll.energysaving.model.aioptimization.WorkSection;
import com.cet.eem.bll.energysaving.model.aioptimization.plc.ControlMode;
import com.cet.eem.bll.energysaving.model.config.MesShift;
import com.cet.eem.bll.energysaving.model.config.ParameterConfig;
import com.cet.eem.bll.energysaving.model.config.system.ConditionParam;
import com.cet.eem.bll.energysaving.model.config.system.ParamConditionConfigVo;
import com.cet.eem.bll.energysaving.model.config.system.ParameterConfigVo;
import com.cet.eem.bll.energysaving.model.weather.PumpVo;
import com.cet.eem.common.model.BaseVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.ValidationException;
import java.io.IOException;
import java.util.List;

/**
 * @ClassName : ParameterConfigService
 * @Description : 参数配置
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-20 19:32
 */
public interface ParameterConfigService {
    /**
     * 查询工作区间
     *
     * @param baseVo
     * @return
     */
    List<WorkSection> queryWorkSection(BaseVo baseVo);

    /**
     * 写入
     *
     * @param workSections
     */
    void writeWorkSection(Long objectId, String objectLabel, List<WorkSection> workSections);

    /**
     * 删除
     *
     * @param ids
     */
    void deleteWorkSection(List<Long> ids);

    /**
     * 根据节点信息删除
     *
     * @param nodes
     */
    void deleteWorkSections(List<BaseVo> nodes);

    /**
     * 导出
     *
     * @param baseVo
     * @param response
     */
    void exportOperatingEfficiencyCurve(PumpVo baseVo, HttpServletResponse response);

    /**
     * 导入运行效率曲线
     *
     * @param id
     * @param label
     * @param file
     * @throws IOException
     */
    void importOperatingEfficiencyCurve(Long id, String label, MultipartFile file) throws IOException, ValidationException;

    /**
     * 删除设备时要删除内容
     *
     * @param baseVo
     */
    void deleteOperatingEfficiencyCurve(List<BaseVo> baseVo);

    /**
     * 写入冷机运行约束条件
     *
     * @param config
     * @return
     */
    ParameterConfigVo writeMachineOperationConstraints(ParameterConfigVo config);

    /**
     * 写入系统供水配置
     *
     * @param config
     * @return
     */
    ParameterConfig writeSystemSupplyWaterConfig(ParameterConfig config);

    /**
     * 写入加减机条件
     *
     * @param params
     */
    void writeAddAndSubConditionConfig(List<ConditionParam> params);

    /**
     * 参数配置查询页面
     *
     * @param systemId
     * @return
     */
    ParamConditionConfigVo queryParamConditionConfig(Long systemId);

    /**
     * 查询mes配置
     * @param baseVo
     * @return
     */
    List<MesShift> queryMesShift(BaseVo baseVo);

    /**
     * 写入mes班次配置
     * @param mesShifts
     * @return
     */
    List<MesShift> writeMesShift(List<MesShift> mesShifts, Long projectId);

    /**
     * 查询控制方案
     * @param systemId
     * @param projectId
     * @return
     */
    ControlMode queryControlMode(Long systemId, Long projectId);

    /**
     * 编辑控制方案
     * @param controlModes
     * @return
     */
    ControlMode writeControlMode(ControlMode controlModes);
}
