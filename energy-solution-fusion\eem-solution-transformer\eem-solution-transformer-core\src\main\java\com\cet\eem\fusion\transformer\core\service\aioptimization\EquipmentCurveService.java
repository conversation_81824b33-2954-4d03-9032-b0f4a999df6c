package com.cet.eem.fusion.transformer.core.service.aioptimization;

import com.cet.eem.bll.energysaving.model.equipmentoperation.TrendListVo;

import java.util.List;

/**
 * @ClassName : EquipmentCurveService
 * @Description : 设备运行曲线
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-30 14:54
 */
public interface EquipmentCurveService {
    /**
     * 查询冷机运行效率
     * @param chainId
     * @return
     */
    List<TrendListVo> queryMainsOperatingEfficiency(Long chainId, Long projectId);

    /**
     * 冷机运行功率
     * @param chainId
     * @return
     */
    List<TrendListVo> queryMainsOperatingPower(Long chainId, Long projectId);

    /**
     * 冷冻泵匹配功率
     * @param chainId
     * @return
     */
    List<TrendListVo> queryMatchingPowerOfRefrigerationPump(Long chainId, Long projectId);
    /**
     * 冷却泵匹配功率
     * @param chainId
     * @return
     */
    List<TrendListVo> queryMatchingPowerOfCoolingPump(Long chainId, Long projectId);
    /**
     * 冷却塔匹配功率
     * @param chainId
     * @return
     */
    List<TrendListVo> queryMatchingPowerOfCoolTower(Long chainId, Long projectId);
    /**
     * 冷冻泵频率与功率关系
     * @param chainId
     * @return
     */
    List<TrendListVo> queryFrequencyAndPowerOfRefrigerationPump(Long chainId, Long projectId);
    /**
     * 冷却泵匹配功率
     * @param chainId
     * @return
     */
    List<TrendListVo> queryFrequencyAndPowerOfCoolPump(Long chainId, Long projectId);
    /**
     * 冷却塔频率与功率关系
     * @param chainId
     * @return
     */
    List<TrendListVo> queryFrequencyAndPowerOfCoolTower(Long chainId, Long projectId);
    /**
     * 冷却供、回水温度与制冷站功率关系
     * @param chainId
     * @return
     */
    List<TrendListVo> queryTempAndPowerOfSupplyAndReturnWater(Long chainId, Long projectId);
}
