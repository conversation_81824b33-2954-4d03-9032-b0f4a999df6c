# IntelliJ Static Analysis Tool 测试脚本
Write-Host "========================================" -ForegroundColor Green
Write-Host "IntelliJ Static Analysis Tool 测试脚本" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host ""
Write-Host "检查JAR文件是否存在..." -ForegroundColor Yellow

$jarFile = "target\intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar"
if (-not (Test-Path $jarFile)) {
    Write-Host "错误: 找不到JAR文件，请先运行构建脚本" -ForegroundColor Red
    Write-Host ""
    Write-Host "正在尝试构建项目..." -ForegroundColor Yellow
    
    & .\build.ps1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "构建失败，无法继续测试" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
}

Write-Host ""
Write-Host "检查示例文件是否存在..." -ForegroundColor Yellow

$exampleFile = "examples\AiOptimizationServiceImpl.java"
if (-not (Test-Path $exampleFile)) {
    Write-Host "错误: 找不到示例文件 $exampleFile" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""
Write-Host "开始分析示例文件..." -ForegroundColor Yellow
Write-Host "命令: java -jar $jarFile $exampleFile ." -ForegroundColor Cyan
Write-Host ""

# 执行分析
& java -jar $jarFile $exampleFile "."

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "测试完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "如果看到分析结果，说明工具运行正常。" -ForegroundColor Green
Write-Host "如果遇到错误，请检查:" -ForegroundColor Yellow
Write-Host "1. JDK版本是否为1.8或更高" -ForegroundColor White
Write-Host "2. 是否有足够的内存 (可以尝试添加 -Xmx2g 参数)" -ForegroundColor White
Write-Host "3. 文件路径是否正确" -ForegroundColor White
Write-Host ""
Read-Host "按任意键退出"
