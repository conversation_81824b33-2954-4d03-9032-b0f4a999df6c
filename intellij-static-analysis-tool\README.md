# IntelliJ Static Analysis Tool

基于IntelliJ Platform SDK的Java静态分析工具，能够通过调用IntelliJ IDEA的SDK来获取指定Java文件的所有静态扫描问题。

## 功能特性

- 基于IntelliJ Platform SDK实现
- 支持分析单个Java文件
- 自动检测和报告各种Java代码问题
- 支持多种严重程度的问题分类（错误、警告、信息）
- 提供详细的问题位置信息（行号、列号）
- 支持快速修复建议
- 完整的日志记录

## 技术栈

- **JDK**: 1.8
- **构建工具**: Maven
- **核心依赖**: IntelliJ Platform SDK
- **日志框架**: SLF4J + Logback

## 项目结构

```
intellij-static-analysis-tool/
├── pom.xml                                    # Maven配置文件
├── README.md                                  # 项目说明文档
├── src/
│   ├── main/
│   │   ├── java/com/example/intellij/analysis/
│   │   │   ├── StaticAnalysisMain.java       # 主入口类
│   │   │   ├── JavaFileAnalyzer.java         # 核心分析器
│   │   │   ├── AnalysisResult.java           # 分析结果数据类
│   │   │   └── InspectionProblem.java        # 问题描述数据类
│   │   └── resources/
│   │       └── logback.xml                   # 日志配置文件
│   └── test/
│       └── java/                             # 测试代码目录
└── examples/
    └── AiOptimizationServiceImpl.java        # 示例Java文件
```

## 构建和运行

### 1. 构建项目

```bash
cd intellij-static-analysis-tool
mvn clean compile
```

### 2. 打包项目

```bash
mvn clean package
```

这将生成两个JAR文件：
- `target/intellij-static-analysis-tool-1.0.0.jar` - 普通JAR
- `target/intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar` - 包含所有依赖的可执行JAR

### 3. 运行分析

```bash
# 使用包含依赖的JAR文件
java -jar target/intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar <文件路径> [项目根路径]

# 示例
java -jar target/intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar examples/AiOptimizationServiceImpl.java .
```

### 参数说明

- `<文件路径>`: 要分析的Java文件的完整路径
- `[项目根路径]`: 可选，项目的根目录路径，默认为文件所在目录

## 使用示例

### 分析单个文件

```bash
java -jar target/intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar /path/to/AiOptimizationServiceImpl.java /path/to/project
```

### 输出示例

```
=== 静态分析结果 ===
文件: /path/to/AiOptimizationServiceImpl.java
分析时间: 1250ms
发现问题数量: 3

=== 问题详情 ===
1. [WARNING] 未使用的导入语句
   位置: 第5行，第1列
   建议修复: 删除未使用的导入

2. [ERROR] 变量可能未初始化
   位置: 第25行，第12列
   建议修复: 初始化变量

3. [INFO] 方法可以声明为静态
   位置: 第45行，第5列
   建议修复: 添加static修饰符
```

## 支持的检查类型

工具会自动运行IntelliJ IDEA中启用的所有Java检查，包括但不限于：

- **代码质量检查**
  - 未使用的变量、方法、导入
  - 重复代码检测
  - 复杂度分析

- **潜在错误检查**
  - 空指针异常风险
  - 资源泄漏检测
  - 线程安全问题

- **代码风格检查**
  - 命名规范
  - 代码格式
  - 注释规范

- **性能优化建议**
  - 字符串拼接优化
  - 集合使用优化
  - 算法复杂度建议

## 配置说明

### 日志配置

日志配置文件位于 `src/main/resources/logback.xml`，支持：
- 控制台输出
- 文件输出（自动滚动）
- 不同组件的日志级别控制

### Maven配置

`pom.xml` 中的关键配置：
- JDK 1.8 编译目标
- IntelliJ Platform SDK依赖
- 可执行JAR打包配置

## 故障排除

### 常见问题

1. **内存不足错误**
   ```bash
   java -Xmx2g -jar target/intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar <文件路径>
   ```

2. **找不到项目文件**
   - 确保提供正确的文件路径
   - 确保项目根路径包含必要的项目文件（如.idea目录或pom.xml）

3. **权限问题**
   - 确保对文件和目录有读取权限
   - 确保日志目录可写

### 调试模式

启用详细日志输出：
```bash
java -Dlogback.configurationFile=src/main/resources/logback.xml -jar target/intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar <文件路径>
```

## 扩展开发

### 添加自定义检查

1. 继承 `LocalInspectionTool` 类
2. 实现检查逻辑
3. 在 `JavaFileAnalyzer` 中注册新的检查

### 自定义输出格式

修改 `StaticAnalysisMain.printAnalysisResult()` 方法来自定义输出格式，支持：
- JSON格式输出
- XML格式输出
- CSV格式输出

## 许可证

本项目基于MIT许可证开源。

## 贡献

欢迎提交Issue和Pull Request来改进这个工具。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
