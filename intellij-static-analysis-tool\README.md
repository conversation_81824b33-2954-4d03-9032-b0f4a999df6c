# IntelliJ Static Analysis Tool

基于IntelliJ Platform SDK的Java静态分析工具，能够通过调用IntelliJ IDEA的SDK来获取指定Java文件的所有静态扫描问题。

## 功能特性

- 基于IntelliJ Platform SDK实现
- 支持分析单个Java文件
- 自动检测和报告各种Java代码问题
- 支持多种严重程度的问题分类（错误、警告、信息）
- 提供详细的问题位置信息（行号、列号）
- 支持快速修复建议
- 完整的日志记录

## 技术栈

- **JDK**: 1.8
- **构建工具**: Maven
- **核心依赖**: IntelliJ Platform SDK
- **日志框架**: SLF4J + Logback

## 安装和构建

### 前置要求

1. **JDK 1.8或更高版本**
2. **Maven 3.6+**
3. **IntelliJ IDEA Community Edition** (推荐2022.3.3版本)

### 1. 设置IntelliJ Platform SDK依赖

由于IntelliJ Platform SDK的JAR文件不在Maven Central仓库中，需要先设置本地依赖：

```powershell
# 自动查找并配置IntelliJ IDEA依赖
.\setup-intellij-sdk.ps1

# 或者手动指定IntelliJ IDEA安装路径
.\setup-intellij-sdk.ps1 -IntellijPath "C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2022.3.3"
```

### 2. 构建项目

```powershell
# 使用PowerShell脚本（推荐）
.\build.ps1

# 或者直接使用Maven
mvn clean package
```

### 3. 运行分析

```powershell
# 使用测试脚本
.\test.ps1

# 或者直接运行
java -jar target/intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar examples/AiOptimizationServiceImpl.java .
```

### 参数说明

- `<文件路径>`: 要分析的Java文件的完整路径
- `[项目根路径]`: 可选，项目的根目录路径，默认为文件所在目录

## 使用示例

### 分析单个文件

```bash
java -jar target/intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar /path/to/AiOptimizationServiceImpl.java /path/to/project
```

### 输出示例

```
=== 静态分析结果 ===
文件: /path/to/AiOptimizationServiceImpl.java
分析时间: 1250ms
发现问题数量: 3

=== 问题详情 ===
1. [WARNING] 未使用的导入语句
   位置: 第5行，第1列
   建议修复: 删除未使用的导入

2. [ERROR] 变量可能未初始化
   位置: 第25行，第12列
   建议修复: 初始化变量

3. [INFO] 方法可以声明为静态
   位置: 第45行，第5列
   建议修复: 添加static修饰符
```

## 支持的检查类型

工具会自动运行IntelliJ IDEA中启用的所有Java检查，包括但不限于：

- **代码质量检查**
  - 未使用的变量、方法、导入
  - 重复代码检测
  - 复杂度分析

- **潜在错误检查**
  - 空指针异常风险
  - 资源泄漏检测
  - 线程安全问题

- **代码风格检查**
  - 命名规范
  - 代码格式
  - 注释规范

- **性能优化建议**
  - 字符串拼接优化
  - 集合使用优化
  - 算法复杂度建议

## 故障排除

### 常见问题

1. **找不到IntelliJ Platform SDK依赖**
   ```powershell
   # 重新运行安装脚本
   .\setup-intellij-sdk.ps1
   ```

2. **内存不足错误**
   ```bash
   java -Xmx2g -jar target/intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar <文件路径>
   ```

3. **找不到项目文件**
   - 确保提供正确的文件路径
   - 确保项目根路径包含必要的项目文件

### 手动配置依赖

如果自动安装脚本失败，可以手动配置：

1. 找到IntelliJ IDEA安装目录下的`lib`文件夹
2. 将以下JAR文件添加到项目依赖中：
   - `idea.jar`
   - `openapi.jar` 
   - `util.jar`
   - `extensions.jar`
   - `annotations.jar`

## 项目结构

```
intellij-static-analysis-tool/
├── pom.xml                                    # Maven配置文件
├── README.md                                  # 项目说明文档
├── setup-intellij-sdk.ps1                    # SDK安装脚本
├── build.ps1                                 # 构建脚本
├── test.ps1                                  # 测试脚本
├── src/
│   ├── main/
│   │   ├── java/com/example/intellij/analysis/
│   │   │   ├── StaticAnalysisMain.java       # 主入口类
│   │   │   ├── JavaFileAnalyzer.java         # 核心分析器
│   │   │   ├── AnalysisResult.java           # 分析结果数据类
│   │   │   └── InspectionProblem.java        # 问题描述数据类
│   │   └── resources/
│   │       └── logback.xml                   # 日志配置文件
│   └── test/
│       └── java/                             # 测试代码目录
└── examples/
    └── AiOptimizationServiceImpl.java        # 示例Java文件
```

## 许可证

本项目基于MIT许可证开源。
