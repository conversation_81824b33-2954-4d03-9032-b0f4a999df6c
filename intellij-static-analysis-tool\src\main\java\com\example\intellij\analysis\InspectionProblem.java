package com.example.intellij.analysis;

/**
 * 检查问题数据类
 * 表示静态分析过程中发现的一个具体问题
 */
public class InspectionProblem {
    
    private final String inspectionName;
    private final String description;
    private final String severity;
    private final int line;
    private final int column;
    private final String quickFix;
    
    /**
     * 构造函数
     * 
     * @param inspectionName 检查工具名称
     * @param description 问题描述
     * @param severity 严重程度 (ERROR, WARNING, WEAK_WARNING, INFO)
     * @param line 行号
     * @param column 列号
     * @param quickFix 快速修复建议
     */
    public InspectionProblem(String inspectionName, String description, String severity, 
                           int line, int column, String quickFix) {
        this.inspectionName = inspectionName;
        this.description = description;
        this.severity = severity;
        this.line = line;
        this.column = column;
        this.quickFix = quickFix;
    }
    
    /**
     * 获取检查工具名称
     * 
     * @return 检查工具名称
     */
    public String getInspectionName() {
        return inspectionName;
    }
    
    /**
     * 获取问题描述
     * 
     * @return 问题描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 获取严重程度
     * 
     * @return 严重程度
     */
    public String getSeverity() {
        return severity;
    }
    
    /**
     * 获取行号
     * 
     * @return 行号
     */
    public int getLine() {
        return line;
    }
    
    /**
     * 获取列号
     * 
     * @return 列号
     */
    public int getColumn() {
        return column;
    }
    
    /**
     * 获取快速修复建议
     * 
     * @return 快速修复建议
     */
    public String getQuickFix() {
        return quickFix;
    }
    
    /**
     * 检查是否为错误级别
     * 
     * @return 如果是错误级别返回true
     */
    public boolean isError() {
        return "ERROR".equals(severity);
    }
    
    /**
     * 检查是否为警告级别
     * 
     * @return 如果是警告级别返回true
     */
    public boolean isWarning() {
        return "WARNING".equals(severity) || "WEAK_WARNING".equals(severity);
    }
    
    /**
     * 检查是否为信息级别
     * 
     * @return 如果是信息级别返回true
     */
    public boolean isInfo() {
        return "INFO".equals(severity);
    }
    
    /**
     * 获取严重程度的显示名称
     * 
     * @return 严重程度的中文显示名称
     */
    public String getSeverityDisplayName() {
        switch (severity) {
            case "ERROR":
                return "错误";
            case "WARNING":
                return "警告";
            case "WEAK_WARNING":
                return "轻微警告";
            case "INFO":
                return "信息";
            default:
                return severity;
        }
    }
    
    /**
     * 获取位置信息的字符串表示
     * 
     * @return 位置信息字符串
     */
    public String getLocationString() {
        return String.format("第%d行，第%d列", line, column);
    }
    
    @Override
    public String toString() {
        return String.format("InspectionProblem{inspection='%s', severity='%s', line=%d, column=%d, description='%s'}",
                inspectionName, severity, line, column, description);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        InspectionProblem that = (InspectionProblem) obj;
        
        return line == that.line &&
               column == that.column &&
               inspectionName.equals(that.inspectionName) &&
               description.equals(that.description) &&
               severity.equals(that.severity);
    }
    
    @Override
    public int hashCode() {
        int result = inspectionName.hashCode();
        result = 31 * result + description.hashCode();
        result = 31 * result + severity.hashCode();
        result = 31 * result + line;
        result = 31 * result + column;
        return result;
    }
}
