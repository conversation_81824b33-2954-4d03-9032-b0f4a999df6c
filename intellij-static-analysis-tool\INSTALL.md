# 安装指南

## 快速开始

### 1. 安装IntelliJ Platform SDK依赖

```powershell
# 运行安装脚本（会自动查找IntelliJ IDEA）
.\setup-intellij-sdk.ps1

# 如果自动查找失败，手动指定路径
.\setup-intellij-sdk.ps1 -IntellijPath "C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2022.3.3"
```

### 2. 构建项目

```powershell
.\build.ps1
```

### 3. 测试运行

```powershell
.\test.ps1
```

## 详细说明

### 依赖问题解决

IntelliJ Platform SDK的JAR文件不在Maven Central仓库中，需要从本地IntelliJ IDEA安装中获取。

**方法1：使用安装脚本（推荐）**

安装脚本会：
1. 自动查找IntelliJ IDEA安装目录
2. 扫描lib目录中的JAR文件
3. 自动更新pom.xml文件，添加系统依赖

**方法2：手动配置**

如果脚本失败，可以手动操作：

1. 找到IntelliJ IDEA安装目录，例如：
   ```
   C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2022.3.3\lib\
   ```

2. 在pom.xml中添加系统依赖：
   ```xml
   <dependency>
       <groupId>com.jetbrains.intellij</groupId>
       <artifactId>idea</artifactId>
       <version>local</version>
       <scope>system</scope>
       <systemPath>C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2022.3.3\lib\idea.jar</systemPath>
   </dependency>
   ```

### 必需的JAR文件

以下是项目需要的主要JAR文件：

- `idea.jar` - 核心API
- `openapi.jar` - 开放API
- `util.jar` - 工具类
- `extensions.jar` - 扩展API
- `annotations.jar` - 注解支持

### 版本兼容性

- **推荐版本**: IntelliJ IDEA Community Edition 2022.3.3
- **JDK版本**: 1.8或更高
- **Maven版本**: 3.6+

### 常见错误

**错误1**: `ClassNotFoundException`
- **原因**: 缺少必要的JAR文件
- **解决**: 重新运行`.\setup-intellij-sdk.ps1`

**错误2**: `NoSuchMethodError`
- **原因**: IntelliJ IDEA版本不兼容
- **解决**: 使用推荐的2022.3.3版本

**错误3**: 找不到IntelliJ IDEA
- **原因**: 非标准安装路径
- **解决**: 手动指定路径参数

## 验证安装

运行以下命令验证安装是否成功：

```powershell
# 编译测试
mvn compile

# 如果编译成功，运行测试
.\test.ps1
```

如果看到类似以下输出，说明安装成功：

```
=== 静态分析结果 ===
文件: examples\AiOptimizationServiceImpl.java
分析时间: 1250ms
发现问题数量: X
```
