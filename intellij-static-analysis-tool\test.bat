@echo off
echo ========================================
echo IntelliJ Static Analysis Tool 测试脚本
echo ========================================

echo.
echo 检查JAR文件是否存在...
if not exist "target\intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar" (
    echo 错误: 找不到JAR文件，请先运行 build.bat 构建项目
    echo.
    echo 正在尝试构建项目...
    call build.bat
    if %errorlevel% neq 0 (
        echo 构建失败，无法继续测试
        pause
        exit /b 1
    )
)

echo.
echo 检查示例文件是否存在...
if not exist "examples\AiOptimizationServiceImpl.java" (
    echo 错误: 找不到示例文件 examples\AiOptimizationServiceImpl.java
    pause
    exit /b 1
)

echo.
echo 开始分析示例文件...
echo 命令: java -jar target\intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar examples\AiOptimizationServiceImpl.java .
echo.

java -jar target\intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar examples\AiOptimizationServiceImpl.java .

echo.
echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 如果看到分析结果，说明工具运行正常。
echo 如果遇到错误，请检查:
echo 1. JDK版本是否为1.8或更高
echo 2. 是否有足够的内存 (可以尝试添加 -Xmx2g 参数)
echo 3. 文件路径是否正确
echo.
pause
