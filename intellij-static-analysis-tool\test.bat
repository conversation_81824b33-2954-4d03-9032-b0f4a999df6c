@echo off
chcp 65001 >nul
echo ========================================
echo IntelliJ Static Analysis Tool Test Script
echo ========================================

echo.
echo Checking if JAR file exists...
if not exist "target\intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar" (
    echo Error: JAR file not found, please run build.bat first
    echo.
    echo Trying to build project...
    call build.bat
    if %errorlevel% neq 0 (
        echo Build failed, cannot continue testing
        pause
        exit /b 1
    )
)

echo.
echo Checking if example file exists...
if not exist "examples\AiOptimizationServiceImpl.java" (
    echo Error: Example file not found: examples\AiOptimizationServiceImpl.java
    pause
    exit /b 1
)

echo.
echo Starting analysis of example file...
echo Command: java -jar target\intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar examples\AiOptimizationServiceImpl.java .
echo.

java -jar target\intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar examples\AiOptimizationServiceImpl.java .

echo.
echo ========================================
echo Test completed!
echo ========================================
echo.
echo If you see analysis results above, the tool is working correctly.
echo If you encounter errors, please check:
echo 1. JDK version is 1.8 or higher
echo 2. Sufficient memory available (try adding -Xmx2g parameter)
echo 3. File paths are correct
echo.
pause
