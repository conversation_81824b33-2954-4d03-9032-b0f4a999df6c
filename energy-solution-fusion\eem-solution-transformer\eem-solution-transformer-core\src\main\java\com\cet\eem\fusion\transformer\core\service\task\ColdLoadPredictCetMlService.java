package com.cet.eem.fusion.transformer.core.service.task;

/**
 * @ClassName : ColdLoadPredictCetMlService
 * @Description : cet版算法
 * <AUTHOR> jiang<PERSON>xuan
 * @Date: 2023-06-08 16:05
 */
public interface ColdLoadPredictCetMlService {
    /**
     * 转存末端数据
     */
    void saveColdPredictData();

    /**
     * 转存冷机控制相关数据
     */
    void saveColdMainControlPredictData();

    /**
     * cop拟合
     */
    void saveCopFittingPredictData();

    /**
     * 泵拟合
     */
    void savePumpFitPredictData();
}