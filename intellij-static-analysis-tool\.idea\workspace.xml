<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="27031896-162d-4871-a3b3-2afdbcac1371" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\work\software\maven\apache-maven-3.5.4" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="E:\work\software\maven\apache-maven-3.5.4\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 6
}]]></component>
  <component name="ProjectId" id="30wz3LHuHEkaxfGpgYEmAej1KeX" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "last_opened_file_path": "E:/work/project/ai-solution-eem-service/intellij-static-analysis-tool",
    "settings.editor.selected.configurable": "reference.projectsettings.compiler.annotationProcessors"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="StaticAnalysisMain" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.example.intellij.analysis.StaticAnalysisMain" />
      <module name="intellij-static-analysis-tool" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.intellij.analysis.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.StaticAnalysisMain" />
      </list>
    </recent_temporary>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="27031896-162d-4871-a3b3-2afdbcac1371" name="Changes" comment="" />
      <created>1754553222862</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754553222862</updated>
      <workItem from="1754553223940" duration="85000" />
    </task>
    <servers />
  </component>
</project>