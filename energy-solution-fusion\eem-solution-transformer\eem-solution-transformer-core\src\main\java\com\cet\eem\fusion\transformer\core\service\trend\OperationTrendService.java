package com.cet.eem.fusion.transformer.core.service.trend;

import com.cet.eem.bll.common.model.domain.subject.energysaving.ColdActual;
import com.cet.eem.bll.common.model.domain.subject.energysaving.ColdPredict;
import com.cet.eem.bll.energysaving.model.weather.*;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
import com.cet.eem.weather.model.Weather;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName : OperationTrendService
 * @Description : 运行趋势
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-15 16:58
 */
public interface OperationTrendService {
    /**
     * 冷冻水供回水温度趋势
     *
     * @param queryParam
     * @return
     * @throws Exception
     */
    List<OperationTrendVo> queryCoolingWaterTrend(QueryParam queryParam) throws Exception;

    /**
     * 冷却水供回水温度趋势
     *
     * @param queryParam
     * @return
     * @throws Exception
     */
    List<OperationTrendVo> queryCoolingTowerTrend(QueryParam queryParam) throws Exception;

    /**
     * 系统功率趋势
     *
     * @param queryParam
     * @return
     * @throws Exception
     */
    List<OperationTrendVo> querySystemCoolingWaterTrend(QueryParam queryParam, Long proejectId) throws Exception;

    /**
     * 查询实时值来了，和前面查定时任务逻辑相似
     *
     * @param queryParam
     * @return
     * @throws Exception
     */
    OperationTrendVo querySystemCoolingWaterSingData(QueryParam queryParam);

    /**
     * 查询冷负荷，传参是管道列表
     *
     * @param baseVos
     * @return
     */
    Double querySystemCold(List<BaseVo> baseVos);

    /**
     * 设备组能耗
     *
     * @param queryParam
     * @return
     */
    EquipmentGroupConsumption queryEquipmentGroupConsumption(QueryParam queryParam);

    /**
     * 温度预测趋势
     *
     * @param queryParam
     * @return
     * @throws Exception
     */
    List<AIPredictWeatherVo> queryAiPredictForTemp(QueryParam queryParam, Long projectId) throws Exception;

    /**
     * 湿度预测趋势
     *
     * @param queryParam
     * @return
     * @throws Exception
     */
    List<AIPredictWeatherVo> queryAiPredictForHum(QueryParam queryParam, Long projectId) throws Exception;

    /**
     * 总冷负荷需求和温度
     *
     * @param queryParam
     * @return
     * @throws Exception
     */
    List<AIPredictWeatherWithEnergyVo> queryAiPredictWithEnergyForTemp(QueryParam queryParam, Long projectId) throws Exception;

    /**
     * 总冷负荷需求和湿度
     *
     * @param queryParam
     * @return
     * @throws Exception
     */
    List<AIPredictWeatherWithEnergyVo> queryAiPredictWithEnergyForHum(QueryParam queryParam, Long projectId) throws Exception;

    /**
     * 系统总功率预测曲线
     *
     * @param queryParam
     * @return
     * @throws Exception
     */
    List<AIPredictWeatherVo> queryAiPredictWithEnergy(QueryParam queryParam) throws Exception;

    /**
     * 总冷负荷需求预测曲线
     *
     * @param queryParam
     * @return
     * @throws Exception
     */
    List<AIPredictWeatherVo> queryAiPredictWithEnergyCop(QueryParam queryParam, Long projectId) throws Exception;

    /**
     * 预测开关机时间
     *
     * @param queryParam
     * @return
     */
    List<AiPredictWithTime> queryAiPredictWithTime(QueryParam queryParam) throws Exception;

    /**
     * 查询末端制冷趋势
     *
     * @param queryParam
     * @return
     */
    List<AIPredictWeatherVo> queryEndColdTrend(QueryParam queryParam, Long projectId) throws InstantiationException, IllegalAccessException;

    /**
     * 查询冷冻水管损失
     *
     * @param queryParam
     * @return
     */
    List<AIPredictWeatherVo> queryFreezingPipeline(QueryParam queryParam, Long projectId) throws IllegalAccessException, InstantiationException;

    /**
     * 总冷负荷需求预测
     *
     * @param queryParam
     * @return
     */
    List<AIPredictWeatherVo> queryTotalColdLoadingTrend(QueryParam queryParam, Long projectId) throws InstantiationException, IllegalAccessException;

    Double getRealTimeColdLoad(QueryParam queryParam, List<BaseVo> pipeLines);

    List<BaseVo> getMainEngineAfterPipe(QueryParam queryParam);

    /**
     * @param queryParam
     * @param label
     * @return
     */
    List<BaseVo> queryColdWaterMainEngine(QueryParam queryParam, String label);

    /**
     * 创建查询物理量条件
     * @param deviceNodes
     * @param st
     * @param et
     * @param cycle
     * @param quantitySearchVo
     * @return
     */
    QuantityDataBatchSearchVo createQuantityDataBatchSearchVo(List<BaseVo> deviceNodes, LocalDateTime st,
                                                              LocalDateTime et, Integer cycle, QuantitySearchVo quantitySearchVo);

    /**
     * 获得第一个不为null的数据列表
     * @param dataVoList
     * @return
     */
    List<DatalogValue> getNonValueDataLog(List<TrendDataVo> dataVoList);

    /**
     * 创建查询物理量条件
     * @param deviceNodes
     * @param query
     * @param quantitySearchVo
     * @return
     */
    QuantityDataBatchSearchVo createQuantityDataSearchVo(List<BaseVo> deviceNodes, QueryParam query, List<QuantitySearchVo> quantitySearchVo);

    /**
     * 求和
     * @param endActual
     * @param totalActual
     * @return
     */
    Double addTwoValue(ColdPredict endActual, ColdPredict totalActual);

    /**
     * 处理冷负荷数据
     * @param value
     * @return
     */
    Double handleDoubleData(Double value);

    /**
     * 处理管损数据
     * @param value
     * @return
     */
    Double handleLossData(Double value);

    /**
     * 查询爬虫未来数据
     * @param st
     * @param et
     * @param cycle
     * @param projectId
     * @return
     */
    List<Weather> queryWeather(LocalDateTime st, LocalDateTime et, Integer cycle, Long projectId);
    List<ColdActual> queryEndCold(QueryParam queryFact, Long projectId) throws IllegalAccessException, InstantiationException;
}
