package com.example.intellij.analysis;

import java.util.List;

/**
 * 静态分析结果数据类
 * 包含分析的文件路径、发现的问题列表和分析耗时
 */
public class AnalysisResult {
    
    private final String filePath;
    private final List<InspectionProblem> problems;
    private final long analysisTime;
    
    /**
     * 构造函数
     * 
     * @param filePath 分析的文件路径
     * @param problems 发现的问题列表
     * @param analysisTime 分析耗时（毫秒）
     */
    public AnalysisResult(String filePath, List<InspectionProblem> problems, long analysisTime) {
        this.filePath = filePath;
        this.problems = problems;
        this.analysisTime = analysisTime;
    }
    
    /**
     * 获取分析的文件路径
     * 
     * @return 文件路径
     */
    public String getFilePath() {
        return filePath;
    }
    
    /**
     * 获取发现的问题列表
     * 
     * @return 问题列表
     */
    public List<InspectionProblem> getProblems() {
        return problems;
    }
    
    /**
     * 获取分析耗时
     * 
     * @return 分析耗时（毫秒）
     */
    public long getAnalysisTime() {
        return analysisTime;
    }
    
    /**
     * 获取问题总数
     * 
     * @return 问题总数
     */
    public int getProblemCount() {
        return problems.size();
    }
    
    /**
     * 按严重程度获取问题数量
     * 
     * @param severity 严重程度
     * @return 指定严重程度的问题数量
     */
    public long getProblemCountBySeverity(String severity) {
        return problems.stream()
                .filter(problem -> severity.equals(problem.getSeverity()))
                .count();
    }
    
    /**
     * 获取错误数量
     * 
     * @return 错误数量
     */
    public long getErrorCount() {
        return getProblemCountBySeverity("ERROR");
    }
    
    /**
     * 获取警告数量
     * 
     * @return 警告数量
     */
    public long getWarningCount() {
        return getProblemCountBySeverity("WARNING") + getProblemCountBySeverity("WEAK_WARNING");
    }
    
    /**
     * 获取信息数量
     * 
     * @return 信息数量
     */
    public long getInfoCount() {
        return getProblemCountBySeverity("INFO");
    }
    
    /**
     * 检查是否有错误
     * 
     * @return 如果有错误返回true，否则返回false
     */
    public boolean hasErrors() {
        return getErrorCount() > 0;
    }
    
    /**
     * 检查是否有警告
     * 
     * @return 如果有警告返回true，否则返回false
     */
    public boolean hasWarnings() {
        return getWarningCount() > 0;
    }
    
    /**
     * 检查是否有任何问题
     * 
     * @return 如果有任何问题返回true，否则返回false
     */
    public boolean hasProblems() {
        return !problems.isEmpty();
    }
    
    @Override
    public String toString() {
        return String.format("AnalysisResult{filePath='%s', problemCount=%d, analysisTime=%dms, errors=%d, warnings=%d, info=%d}",
                filePath, getProblemCount(), analysisTime, getErrorCount(), getWarningCount(), getInfoCount());
    }
}
