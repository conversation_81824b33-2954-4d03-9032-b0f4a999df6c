<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.example.intellij</groupId>
    <artifactId>intellij-static-analysis-tool</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <name>IntelliJ Static Analysis Tool</name>
    <description>A tool to analyze Java files using IntelliJ Platform SDK</description>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <intellij.version>2022.3.3</intellij.version>
        <kotlin.version>1.7.22</kotlin.version>
    </properties>

    <repositories>
        <repository>
            <id>central</id>
            <name>Maven Central</name>
            <url>https://repo1.maven.org/maven2</url>
        </repository>
        <repository>
            <id>jetbrains-releases</id>
            <name>JetBrains Releases</name>
            <url>https://www.jetbrains.com/intellij-repository/releases</url>
        </repository>
        <repository>
            <id>jetbrains-snapshots</id>
            <name>JetBrains Snapshots</name>
            <url>https://www.jetbrains.com/intellij-repository/snapshots</url>
        </repository>
    </repositories>

    <dependencies>
        <!-- IntelliJ Platform SDK 核心依赖 -->
        <!-- 注意：这些依赖可能需要手动安装到本地Maven仓库 -->

        <!-- 方法1：使用本地安装的IntelliJ IDEA -->
        <!-- 需要将IntelliJ IDEA安装目录下的JAR文件添加到项目中 -->

        <!-- 临时解决方案：使用系统路径依赖 -->
        <!-- 用户需要根据实际IntelliJ IDEA安装路径调整 -->

        <!-- Logging -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.36</version>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.2.12</version>
        </dependency>

        <!-- 工具类 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
        </dependency>

        <!-- Test Dependencies -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- Maven Compiler Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

            <!-- IntelliJ Platform Plugin -->
            <plugin>
                <groupId>org.jetbrains.intellij</groupId>
                <artifactId>intellij-maven-plugin</artifactId>
                <version>1.13.3</version>
                <configuration>
                    <version>${intellij.version}</version>
                    <type>IC</type>
                    <downloadSources>true</downloadSources>
                    <updateSinceUntilBuild>false</updateSinceUntilBuild>
                </configuration>
                <executions>
                    <execution>
                        <id>prepare-sandbox</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>prepare-sandbox</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Maven Assembly Plugin for creating executable JAR -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.6.0</version>
                <configuration>
                    <descriptorRefs>
                        <descriptorRef>jar-with-dependencies</descriptorRef>
                    </descriptorRefs>
                    <archive>
                        <manifest>
                            <mainClass>com.example.intellij.analysis.StaticAnalysisMain</mainClass>
                        </manifest>
                    </archive>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
