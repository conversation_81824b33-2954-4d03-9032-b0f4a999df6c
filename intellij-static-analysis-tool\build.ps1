# IntelliJ Static Analysis Tool 构建脚本
Write-Host "========================================" -ForegroundColor Green
Write-Host "IntelliJ Static Analysis Tool 构建脚本" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host ""
Write-Host "检查Maven是否可用..." -ForegroundColor Yellow

# 检查Maven是否可用
try {
    $mavenVersion = & mvn --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Maven not found"
    }
    Write-Host "Maven版本信息:" -ForegroundColor Green
    Write-Host $mavenVersion
} catch {
    Write-Host "错误: 未找到Maven，请确保Maven已安装并添加到PATH环境变量中" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""
Write-Host "开始清理项目..." -ForegroundColor Yellow
& mvn clean
if ($LASTEXITCODE -ne 0) {
    Write-Host "错误: 清理项目失败" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""
Write-Host "开始编译项目..." -ForegroundColor Yellow
& mvn compile
if ($LASTEXITCODE -ne 0) {
    Write-Host "错误: 编译项目失败" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""
Write-Host "开始打包项目..." -ForegroundColor Yellow
& mvn package
if ($LASTEXITCODE -ne 0) {
    Write-Host "错误: 打包项目失败" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "构建完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "生成的文件:" -ForegroundColor Green
Write-Host "- target\intellij-static-analysis-tool-1.0.0.jar" -ForegroundColor White
Write-Host "- target\intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar" -ForegroundColor White
Write-Host ""
Write-Host "使用方法:" -ForegroundColor Yellow
Write-Host "java -jar target\intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar <文件路径> [项目根路径]" -ForegroundColor Cyan
Write-Host ""
Write-Host "示例:" -ForegroundColor Yellow
Write-Host "java -jar target\intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar examples\AiOptimizationServiceImpl.java ." -ForegroundColor Cyan
Write-Host ""
Read-Host "按任意键退出"
