package com.cet.eem.fusion.demo.core;

import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.JavaParser;
import com.github.javaparser.ParseResult;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

public class ListJarClasses {
    private static Set<String> read(String jarPath) throws IOException {
        Set<String> classSet = new HashSet<>();
        File jarFile = new File(jarPath);
        JarFile jar = new JarFile(jarFile);
        Enumeration<JarEntry> entries = jar.entries();
        while (entries.hasMoreElements()) {
            JarEntry entry = entries.nextElement();
            String name = entry.getName();
            if (name.endsWith(".class") && !name.contains("$")) { // 过滤出所有的类文件
                classSet.add(name);
            }
        }
        jar.close();
        return classSet;
    }

    public static void main(String[] args) throws IOException {
        //识别所有的jar包

        //根据jar包识别所有的类。
//        List<String> jarPathList = Arrays.asList(
////                "E:/work/project/maven_repo/com/cet/electric/device-data-service-core/2.6.0/device-data-service-core-2.6.0.jar",
//                "E:/work/project/maven_repo/com/cet/electric/eem-solution-common/4.0.0-SNAPSHOT/eem-solution-common-4.0.0-SNAPSHOT.jar");
//        Set<String> classSetTotal = new HashSet<>();
//        jarPathList.forEach(jarPath -> {
//            try {
//                classSetTotal.addAll(read(jarPath));
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
//        });
//        classSetTotal.forEach(System.out::println);

        //提取相关代码的import内容
        try {
            // 读取AIEnergyConsumptionAndCopServiceImpl文件
            String filePath = "E:/work/project/ai-solution-eem-service/energy-solution-fusion/eem-solution-transformer/eem-solution-transformer-core/src/main/java/com/cet/eem/fusion/transformer/core/service/aioptimization/impl/AIEnergyConsumptionAndCopServiceImpl.java";
            Path path = Paths.get(filePath);

            if (Files.exists(path)) {
                String code = new String(Files.readAllBytes(path), "UTF-8");

                // 使用JavaParser实例而不是静态方法
                JavaParser javaParser = new JavaParser();
                ParseResult<CompilationUnit> parseResult = javaParser.parse(code);

                if (parseResult.isSuccessful() && parseResult.getResult().isPresent()) {
                    CompilationUnit cu = parseResult.getResult().get();

//                    // 输出类信息
//                    cu.getTypes().forEach(type -> {
//                        System.out.println("找到类: " + type.getNameAsString());
//                        System.out.println("类的完整信息:");
//                        System.out.println(type.toString());
//                    });

                    // 输出import信息
                    System.out.println("\n=== Import 语句 ===");
                    cu.getImports().forEach(importDecl -> {
                        System.out.println(importDecl.toString());
                    });

                } else {
                    System.err.println("解析Java文件失败");
                    parseResult.getProblems().forEach(problem -> {
                        System.err.println("解析问题: " + problem.getMessage());
                    });
                }
            } else {
                System.err.println("找不到AIEnergyConsumptionAndCopServiceImpl文件: " + filePath);
            }

        } catch (IOException e) {
            System.err.println("读取文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}