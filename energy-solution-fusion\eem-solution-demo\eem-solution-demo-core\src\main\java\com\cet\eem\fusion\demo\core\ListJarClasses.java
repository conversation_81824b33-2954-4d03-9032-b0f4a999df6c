package com.cet.eem.fusion.demo.core;

import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.JavaParser;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

public class ListJarClasses {
    private static Set<String> read(String jarPath) throws IOException {
        Set<String> classSet = new HashSet<>();
        File jarFile = new File(jarPath);
        JarFile jar = new JarFile(jarFile);
        Enumeration<JarEntry> entries = jar.entries();
        while (entries.hasMoreElements()) {
            JarEntry entry = entries.nextElement();
            String name = entry.getName();
            if (name.endsWith(".class") && !name.contains("$")) { // 过滤出所有的类文件
                classSet.add(name);
            }
        }
        jar.close();
        return classSet;
    }

    public static void main(String[] args) throws IOException {
        //识别所有的jar包

        //根据jar包识别所有的类。
//        List<String> jarPathList = Arrays.asList(
////                "E:/work/project/maven_repo/com/cet/electric/device-data-service-core/2.6.0/device-data-service-core-2.6.0.jar",
//                "E:/work/project/maven_repo/com/cet/electric/eem-solution-common/4.0.0-SNAPSHOT/eem-solution-common-4.0.0-SNAPSHOT.jar");
//        Set<String> classSetTotal = new HashSet<>();
//        jarPathList.forEach(jarPath -> {
//            try {
//                classSetTotal.addAll(read(jarPath));
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
//        });
//        classSetTotal.forEach(System.out::println);

        //提取相关代码的import内容
        String code = "class A { void m() { System.out.println(\"Hello\"); } }";
        CompilationUnit cu = JavaParser.parse(code);
        System.out.println(cu.getClassByName("A").get());
    }
}