package com.cet.eem.fusion.transformer.core.service.predict.impl;

import com.cet.eem.bll.common.dao.node.EnergySupplyDao;
import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.bll.common.dao.pipenetwork.PipeNetworkConnectionModelDao;
import com.cet.eem.bll.common.def.quantity.FrequencyDef;
import com.cet.eem.bll.common.def.quantity.PhasorDef;
import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;
import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;
import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;
import com.cet.eem.bll.common.model.domain.subject.energysaving.ColdActual;
import com.cet.eem.bll.common.model.node.relations.EnergySupplyToPo;
import com.cet.eem.bll.energysaving.dao.aioptimization.EquipmentCurveDao;
import com.cet.eem.bll.energysaving.dao.weather.RefrigeratingSystemDao;
import com.cet.eem.bll.energysaving.handle.CurveFitting;
import com.cet.eem.bll.energysaving.model.cetml.*;
import com.cet.eem.bll.energysaving.model.config.DeviceChainParam;
import com.cet.eem.bll.energysaving.model.config.NodeWithSort;
import com.cet.eem.bll.energysaving.model.config.PumpFunctionType;
import com.cet.eem.bll.energysaving.model.config.system.RefrigeratingSystem;
import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.bll.energysaving.model.def.QuantityDef;
import com.cet.eem.bll.energysaving.model.weather.PumpVo;
import com.cet.eem.bll.energysaving.model.weather.QueryParam;
import com.cet.eem.bll.energysaving.service.predict.CetMlPredictService;
import com.cet.eem.bll.energysaving.service.trend.ModelConfigurationService;
import com.cet.eem.bll.energysaving.service.trend.OperationTrendService;
import com.cet.eem.common.constant.EnergyTypeDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName : CetMlPredictServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-06-08 17:10
 */
@Service
@Slf4j
public class CetMlPredictServiceImpl implements CetMlPredictService {
    @Autowired
    OperationTrendService operationTrendService;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    EnergySupplyDao energySupplyDao;
    @Value("${cet.eem.task.energy-saving.room-code: 1,2,3}")
    private String roomCode;
    @Autowired
    QuantityManageService quantityManageService;
    @Autowired
    RefrigeratingSystemDao refrigeratingSystemDao;
    @Autowired
    CurveFitting curveFitting;
    @Autowired
    EquipmentCurveDao equipmentCurveDao;
    @Autowired
    ModelConfigurationService modelConfigurationService;
    @Autowired
    NodeDao nodeDao;
    @Autowired
    PipeNetworkConnectionModelDao connectionModelDao;
    //gj转kw
    public static final Double UNIT = 0.0036D;

    /**
     * [
     * {
     * "project_id": null,
     * "predict_data": [
     * {
     * "logtime": null,
     * "actual_humidity": null,
     * "actual_temp": null,
     * "actual_power": null,
     * "actual_cooling_load": null
     * }
     * ]
     * }
     * ]
     * actual_temp: 实际测量温度,Double，摄氏度
     * actual_humidity: 实际测量湿度,Double
     * actual_power: 车间用电功率（焊装+涂装+总装）,Double，kW
     * actual_cooling_load: 实际冷负荷测量值,KW,Double,kW`
     *
     * @param time
     * @param roomId
     * @param projectId
     * @return
     */
    @Override
    public List<ColdLoadQueryParam> getColdPredictQueryParam(LocalDateTime time, Long roomId, Long projectId) throws Exception {
        //1.温度湿度通过查询气象监测仪的定时记录
        //2.冷负荷通过末端冷负荷*1.01
        //3.车间用电功率，通过3个厂房关联的一段线的功率
        QueryParam queryParam = new QueryParam(roomId, NodeLabelDef.ROOM, time, TimeUtil.addDateTimeByCycle(time, AggregationCycle.FIFTEEN_MINUTES, 1), AggregationCycle.FIFTEEN_MINUTES);
//        List<AIPredictWeatherWithEnergyVo> temp = operationTrendService.queryAiPredictWithEnergyForTemp(queryParam, projectId);
//        List<AIPredictWeatherWithEnergyVo> hum = operationTrendService.queryAiPredictWithEnergyForHum(queryParam, projectId);
        List<BaseVo> monitor = queryNodesByMonitor(projectId);
        //冷机功率
        Map<Integer, List<TrendDataVo>> temp = quantityManageService.queryDataLogBatch(
                operationTrendService.createQuantityDataBatchSearchVo(monitor, time, TimeUtil.addDateTimeByCycle(time, AggregationCycle.FIFTEEN_MINUTES, 1),
                        AggregationCycle.FIFTEEN_MINUTES, QuantityDef.getAiPredictForTempQuantitySetting()));
        //冷机供水温度
        Map<Integer, List<TrendDataVo>> hum = quantityManageService.queryDataLogBatch(
                operationTrendService.createQuantityDataBatchSearchVo(monitor, time, TimeUtil.addDateTimeByCycle(time, AggregationCycle.FIFTEEN_MINUTES, 1), AggregationCycle.FIFTEEN_MINUTES,
                        QuantityDef.getAiPredictForHumQuantitySetting()));
        //冷机冷负荷
        List<ColdActual> actuals = operationTrendService.queryEndCold(new QueryParam(roomId, NodeLabelDef.ROOM, time,
                TimeUtil.addDateTimeByCycle(time, AggregationCycle.FIFTEEN_MINUTES, 1), AggregationCycle.FIFTEEN_MINUTES), projectId);

        List<BaseVo> building = getBuilding();
        List<EnergySupplyToPo> energySupplyToPos = energySupplyDao.queryEnergySupplyTo(building, System.currentTimeMillis(), EnergySupplyToPo.class);
        List<BaseVo> nodes = energySupplyToPos.stream().filter(energySupplyToPo -> Objects.equals(NodeLabelDef.LINE_SEGMENT_WITH_SWITCH, energySupplyToPo.getObjectlabel()))
                .map(it -> new BaseVo(it.getObjectid(), it.getObjectlabel())).collect(Collectors.toList());
        Double powerSum = getPowerSum(nodes, queryParam);
        return assembleColdLoadQueryParam(temp, hum, time, powerSum, projectId, actuals);
    }

    private List<BaseVo> queryNodesByMonitor(Long projectId) {
        QueryCondition condition = new QueryConditionBuilder<>(NodeLabelDef.PROJECT, projectId)
                .leftJoin(ColdOptimizationLabelDef.METEORO_LOGICAL_MONITOR).queryAsTree().build();
        List<BaseVo> query = modelServiceUtils.query(condition, BaseVo.class);
        List<BaseVo> result = new ArrayList<>();
        for (BaseVo baseVo : query) {
            if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                result.addAll(baseVo.getChildren());
            }
        }
        return result;
    }

    private List<ColdLoadQueryParam> assembleColdLoadQueryParam(Map<Integer, List<TrendDataVo>> temp, Map<Integer, List<TrendDataVo>> hum,
                                                                LocalDateTime time, Double powerSum, Long projectId, List<ColdActual> actuals) {
        ColdLoadQueryParam param = new ColdLoadQueryParam();
        param.setProjectId(projectId);
        ColdLoadParam coldLoadParam = new ColdLoadParam();
        param.setPredictData(Collections.singletonList(coldLoadParam));
        Double tempValue = null;
        Double humValue = null;
        Double cold = null;
        if (!temp.isEmpty()) {
            List<TrendDataVo> tempData = temp.get(QuantityDef.getAiPredictForTempQuantitySetting().getId());
            if (CollectionUtils.isNotEmpty(tempData) && CollectionUtils.isNotEmpty(tempData.get(0).getDataList())) {
                tempValue = tempData.get(0).getDataList().get(0).getValue();
            }
        }
        if (!hum.isEmpty()) {
            List<TrendDataVo> humData = hum.get(QuantityDef.getAiPredictForHumQuantitySetting().getId());
            if (CollectionUtils.isNotEmpty(humData) && CollectionUtils.isNotEmpty(humData.get(0).getDataList())) {
                humValue = humData.get(0).getDataList().get(0).getValue();
            }
        }
        if (CollectionUtils.isNotEmpty(actuals)) {
            cold = actuals.stream().filter(datalogValue -> Objects.nonNull(datalogValue.getValue()) && Objects.equals(datalogValue.getLogTime(), time))
                    .mapToDouble(ColdActual::getValue).sum();
            cold = Math.abs(cold) * 1.01;
        }

        coldLoadParam.setActualCoolingLoad(cold);
        coldLoadParam.setActualHumidity(humValue);
        coldLoadParam.setActualPower(powerSum);
        coldLoadParam.setActualTemp(tempValue);
        coldLoadParam.setLogTime(TimeUtil.localDateTime2timestamp(time));
        return Collections.singletonList(param);
    }

    private List<ColdLoadQueryParam> assembleColdLoadQueryParamBatch(Map<Integer, List<TrendDataVo>> temp, Map<Integer, List<TrendDataVo>> hum,
                                                                     LocalDateTime time, LocalDateTime endTime, Map<Long, List<DatalogValue>> powerBatch, Long projectId, List<ColdActual> actuals) {
        ColdLoadQueryParam param = new ColdLoadQueryParam();
        param.setProjectId(projectId);
        List<LocalDateTime> timeRange = TimeUtil.getTimeRange(time, endTime, AggregationCycle.FIFTEEN_MINUTES);
        Map<Long, DatalogValue> tempMap = new HashMap<>();
        Map<Long, DatalogValue> humMap = new HashMap<>();
        Map<LocalDateTime, List<ColdActual>> coldMap = new HashMap<>();
        if (Objects.nonNull(temp)) {
            List<TrendDataVo> tempData = temp.get(QuantityDef.getAiPredictForTempQuantitySetting().getId());
            if (CollectionUtils.isNotEmpty(tempData) && CollectionUtils.isNotEmpty(tempData.get(0).getDataList())) {
                List<DatalogValue> dataList = tempData.get(0).getDataList();
                tempMap = dataList.stream().collect(Collectors.toMap(DatalogValue::getTime, Function.identity()));
            }
        }
        if (Objects.nonNull(hum)) {
            List<TrendDataVo> humData = hum.get(QuantityDef.getAiPredictForHumQuantitySetting().getId());
            if (CollectionUtils.isNotEmpty(humData) && CollectionUtils.isNotEmpty(humData.get(0).getDataList())) {
                List<DatalogValue> dataList = humData.get(0).getDataList();
                humMap = dataList.stream().collect(Collectors.toMap(DatalogValue::getTime, Function.identity()));
            }
        }
        if (CollectionUtils.isNotEmpty(actuals)) {
            coldMap = actuals.stream().collect(Collectors.groupingBy(ColdActual::getLogTime));

        }
        List<ColdLoadParam> coldLoadParams = new ArrayList<>();
        for (LocalDateTime current : timeRange) {
            Double tempValue = null;
            Double humValue = null;
            Double cold = null;
            Double power = null;
            DatalogValue tempSingle = tempMap.get(TimeUtil.localDateTime2timestamp(current));
            if (Objects.nonNull(tempSingle)) {
                tempValue = tempSingle.getValue();
            }
            DatalogValue humSingle = humMap.get(TimeUtil.localDateTime2timestamp(current));
            if (Objects.nonNull(humSingle)) {
                humValue = humSingle.getValue();
            }
            List<ColdActual> actuals1 = coldMap.get((current));
            if (CollectionUtils.isNotEmpty(actuals1)) {
                cold = actuals1.stream().filter(coldActual -> Objects.nonNull(coldActual.getValue()))
                        .mapToDouble(ColdActual::getValue).sum();
                cold = Math.abs(cold) * 1.01;
            }
            if (Objects.nonNull(powerBatch)) {
                List<DatalogValue> datalogValue = powerBatch.get(TimeUtil.localDateTime2timestamp(current));
                if (CollectionUtils.isNotEmpty(datalogValue)) {
                    power = datalogValue.stream().filter(DatalogValue1 -> Objects.nonNull(DatalogValue1.getValue()))
                            .mapToDouble(DatalogValue::getValue).sum();
                }
            }
            ColdLoadParam coldLoadParam = new ColdLoadParam();
            coldLoadParam.setActualCoolingLoad(cold);
            coldLoadParam.setActualHumidity(humValue);
            coldLoadParam.setActualPower(power);
            coldLoadParam.setActualTemp(tempValue);
            coldLoadParam.setLogTime(TimeUtil.localDateTime2timestamp(current));
            coldLoadParams.add(coldLoadParam);
        }
        param.setPredictData(coldLoadParams);
        return Collections.singletonList(param);
    }

    /**
     * 车间用电功率（焊装+涂装+总装）,Double，kW
     *
     * @param nodes
     * @param queryParam
     * @return
     */
    private Double getPowerSum(List<BaseVo> nodes, QueryParam queryParam) {
        QuantityDataBatchSearchVo quantityDataSearchVo = operationTrendService.createQuantityDataSearchVo(nodes, queryParam,
                Collections.singletonList(getNodePowerQuantitySetting()));
        Map<Integer, List<TrendDataVo>> dataLogResult = quantityManageService.queryDataLogBatch(
                quantityDataSearchVo);
        if (Objects.isNull(dataLogResult)) {
            return null;
        }

        // 取出设备数据
        List<TrendDataVo> dataVoList = dataLogResult.get(getNodePowerQuantitySetting().getId());
        if (CollectionUtils.isEmpty(dataVoList)) {
            return null;
        }
        Double sum = dataVoList.stream().filter(trendDataVo -> CollectionUtils.isNotEmpty(trendDataVo.getDataList()))
                .flatMap(trendDataVo -> trendDataVo.getDataList().stream())
                .filter(datalogValue -> Objects.equals(TimeUtil.localDateTime2timestamp(queryParam.getStartTime()), datalogValue.getTime())
                        && Objects.nonNull(datalogValue.getValue())).mapToDouble(DatalogValue::getValue).sum();

        return sum;
    }

    /**
     * 车间用电功率（焊装+涂装+总装）,Double，kW
     *
     * @param nodes
     * @param queryParam
     * @return
     */
    private Map<Long, List<DatalogValue>> getPowerSumBatch(List<BaseVo> nodes, QueryParam queryParam) {
        QuantityDataBatchSearchVo quantityDataSearchVo = operationTrendService.createQuantityDataSearchVo(nodes, queryParam,
                Collections.singletonList(getNodePowerQuantitySetting()));
        Map<Integer, List<TrendDataVo>> dataLogResult = quantityManageService.queryDataLogBatch(
                quantityDataSearchVo);
        if (Objects.isNull(dataLogResult)) {
            return null;
        }

        // 取出设备数据
        List<TrendDataVo> dataVoList = dataLogResult.get(getNodePowerQuantitySetting().getId());
        if (CollectionUtils.isEmpty(dataVoList)) {
            return null;
        }
        return dataVoList.stream().filter(trendDataVo -> CollectionUtils.isNotEmpty(trendDataVo.getDataList()))
                .flatMap(trendDataVo -> trendDataVo.getDataList().stream())
                .collect(Collectors.groupingBy(DatalogValue::getTime));

    }

    /**
     * 功率
     *
     * @return
     */
    private QuantitySearchVo getNodePowerQuantitySetting() {
        return new QuantitySearchVo(2000004,
                QuantityCategoryDef.POWER,
                QuantityTypeDef.POWER,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.ELECTRIC);
    }

    private List<BaseVo> getBuilding() {
        List<String> code = new ArrayList<>();
        String[] split = roomCode.split(",");
        for (String item : split) {
            code.add(item);
        }
        if (CollectionUtils.isEmpty(code)) {
            return Collections.emptyList();
        }
        QueryConditionBuilder builder = new QueryConditionBuilder(NodeLabelDef.BUILDING);
        builder.in(ColumnDef.CODE, code);
        List<BaseVo> query = modelServiceUtils.query(builder.build(), BaseVo.class);
        if (CollectionUtils.isEmpty(query)) {
            return Collections.emptyList();
        }
        return query;
    }

    /**
     * [
     * {
     * "project_id": null,
     * "predict_data": [
     * {
     * "logtime": null,
     * "actual_humidity": null,
     * "actual_temp": null,
     * "actual_power": null,
     * "actual_cooling_load": null,
     * "fit_data":[
     * {
     * "object_id": null,
     * "rated_refrigerating": null,
     * "water_return_temp": null,
     * "pump_power": null
     * }
     * ]
     * }
     * ],
     * "switch_limit":{
     * "temp_limit":null,
     * "cooling_load_demand_limit": null
     * }
     * }
     * ]
     * ```
     * <p>
     * 请求参数说明
     * <p>
     * ```yaml
     * <p>
     * object_id: 冷机id,Long，（正常状态的冷机设备）
     * rated_refrigerating: 冷机水组额定制冷量,Double, kW
     * rated_power: 冷机额定功率,Double, kW
     * actual_temp: 实际测量温度,Double, 摄氏度
     * actual_humidity: 实际测量湿度,Double
     * actual_power: 车间用电功率（焊装+涂装+总装）,Double, kW
     * actual_cooling_load: 实际总冷负荷测量值,Double, kW
     * temp_limit: 板换开启温度限制, 摄氏度
     * cooling_load_demand_limit: 板换开启冷机制冷需求量限制, kW
     *
     * @param time
     * @param roomId
     * @param projectId
     * @return
     */
    @Override
    public List<ColdMachineControlParam> getColdMainControlQueryParams(LocalDateTime time, Long roomId, Long projectId) throws Exception {
        List<ColdLoadQueryParam> coldPredictQueryParam = getColdPredictQueryParam(time, roomId, projectId);
        if (CollectionUtils.isEmpty(coldPredictQueryParam)) {
            return Collections.emptyList();
        }
        List<DeviceChainParam> deviceChainParams = modelConfigurationService.queryAllDeviceChain(roomId, projectId);
        if (CollectionUtils.isEmpty(deviceChainParams)) {
            return Collections.emptyList();
        }
        List<NodeWithSort> nodeWithSorts = deviceChainParams.stream().filter(deviceChainParam -> CollectionUtils.isNotEmpty(deviceChainParam.getNodes()))
                .flatMap(deviceChainParam -> deviceChainParam.getNodes().stream())
                .collect(Collectors.toList());
        List<BaseVo> baseVos = nodeWithSorts.stream().filter(nodeWithSort -> Objects.equals(nodeWithSort.getModelLabel(), NodeLabelDef.PUMP))
                .map(nodeWithSort -> new BaseVo(nodeWithSort.getId(), nodeWithSort.getModelLabel())).distinct().collect(Collectors.toList());
        List<BaseVo> mains = nodeWithSorts.stream().filter(nodeWithSort -> Objects.equals(nodeWithSort.getModelLabel(), NodeLabelDef.COLD_WATER_MAINENGINE))
                .map(nodeWithSort -> new BaseVo(nodeWithSort.getId(), nodeWithSort.getModelLabel())).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(baseVos)) {
            return Collections.emptyList();
        }
        List<PumpVo> pumpVos = nodeDao.queryNodes(baseVos, PumpVo.class);
        //过滤出冷冻泵
        List<PumpVo> freezingPump = pumpVos.stream().filter(pumpVo -> Objects.equals(pumpVo.getFunctionType(), PumpFunctionType.REFRIGERATING_PUMP))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(freezingPump)) {
            return Collections.emptyList();
        }
        List<EnergySupplyToPo> energySupplyToPos = energySupplyDao.queryEnergySupplyTo(baseVos, System.currentTimeMillis(), EnergySupplyToPo.class);
        List<EnergySupplyToPo> supplyToPos = energySupplyToPos.stream().filter(energySupplyToPo -> Objects.equals(NodeLabelDef.LINE_SEGMENT_WITH_SWITCH, energySupplyToPo.getObjectlabel()))
                .collect(Collectors.toList());
        List<BaseVo> nodes = supplyToPos.stream().map(energySupplyToPo -> new BaseVo(energySupplyToPo.getObjectid(), energySupplyToPo.getObjectlabel())).distinct().collect(Collectors.toList());
        //补充查询冷冻水供水水温度和冷冻泵功率
        //冷冻水供水水温度
        Map<Integer, List<TrendDataVo>> temp = quantityManageService.queryDataLogBatch(
                operationTrendService.createQuantityDataBatchSearchVo(mains, time, TimeUtil.addDateTimeByCycle(time, AggregationCycle.FIFTEEN_MINUTES, 1),
                        AggregationCycle.FIFTEEN_MINUTES, QuantityDef.getFreezingWaterForSupplyQuantitySetting()));
        //冷冻泵功率
        Map<Integer, List<TrendDataVo>> power = quantityManageService.queryDataLogBatch(
                operationTrendService.createQuantityDataBatchSearchVo(nodes, time, TimeUtil.addDateTimeByCycle(time, AggregationCycle.FIFTEEN_MINUTES, 1), AggregationCycle.FIFTEEN_MINUTES,
                        QuantityDef.getMainPowerQuantitySetting()));

        ColdMachineControlParam param = new ColdMachineControlParam();
        List<ColdMachineData> coldMachineData = getColdMachineData(roomId, temp, power, createColdWithPump(deviceChainParams, freezingPump), energySupplyToPos);
        List<ColdLoadParam> predictData = coldPredictQueryParam.get(0).getPredictData();
        if (CollectionUtils.isNotEmpty(predictData)) {
            ColdLoadParam coldLoadParam = predictData.get(0);
            coldLoadParam.setColdMachineData(coldMachineData);
            param.setColdLoadParams(Collections.singletonList(coldLoadParam));
        } else {
            param.setColdLoadParams(predictData);
        }
        List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.querySystemByRoomId(roomId, projectId);
        SwitchLimit switchLimit = new SwitchLimit(refrigeratingSystems.get(0).getOutsideTempLimit(), refrigeratingSystems.get(0).getCoolingLoadDemandLimit());
        param.setSwitchLimit(switchLimit);
        param.setProjectId(projectId);
        return Collections.singletonList(param);
    }

    private List<ColdMachineData> getColdMachineData(Long roomId, Map<Integer, List<TrendDataVo>> temp, Map<Integer, List<TrendDataVo>> power,
                                                     Map<Long, List<Long>> idMap, List<EnergySupplyToPo> supplyToPos) {
        List<Map<String, Object>> maps = modelServiceUtils.queryWithChildren(NodeLabelDef.ROOM,
                Collections.singletonList(roomId), Collections.singletonList(NodeLabelDef.COLD_WATER_MAINENGINE));
        List<ColdMachineData> coldMachineData = new ArrayList<>();
        List child = (List) maps.get(0).get(ColumnDef.CHILDREN);
        List<Map> childMap = JsonTransferUtils.transferList(child, Map.class);
        for (Map map : childMap) {
            Integer nodeId = (Integer) map.get(ColumnDef.ID);
            Long id = nodeId.longValue();
            Double cold = (Double) map.get("ratedrefrigeration");
            Double waterTemp = null;
            Double powerData = null;

            if (Objects.nonNull(temp)) {
                List<TrendDataVo> trendDataVos = temp.get(QuantityDef.getFreezingWaterForSupplyQuantitySetting().getId());
                if (CollectionUtils.isNotEmpty(trendDataVos)) {
                    Optional<Double> first = trendDataVos.stream().filter(trendDataVo -> Objects.equals(trendDataVo.getMonitoredid(), id)).filter(trendDataVo -> CollectionUtils.isNotEmpty(trendDataVo.getDataList()))
                            .flatMap(trendDataVo -> trendDataVo.getDataList().stream()).filter(datalogValue -> Objects.nonNull(datalogValue.getValue())
                                    && !Objects.equals(datalogValue.getValue(), 0.0)).map(DatalogValue::getValue).findFirst();
                    if (first.isPresent()) {
                        waterTemp = first.get();
                    }

                }
            }
            if (Objects.nonNull(power)) {
                List<TrendDataVo> trendDataVos = power.get(QuantityDef.getMainPowerQuantitySetting().getId());
                List<Long> longList = idMap.get(id);
                if (CollectionUtils.isEmpty(longList)) {
                    continue;
                }
                List<EnergySupplyToPo> energySupplyToPos = supplyToPos.stream().filter(energySupplyToPo -> longList.contains(energySupplyToPo.getSupplytoid()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(energySupplyToPos)) {
                    continue;
                }
                Set<Long> lines = energySupplyToPos.stream().map(EnergySupplyToPo::getObjectid).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(trendDataVos) && CollectionUtils.isNotEmpty(lines)) {
                    Optional<DatalogValue> first = trendDataVos.stream().filter(trendDataVo -> lines.contains(trendDataVo.getMonitoredid())).filter(trendDataVo -> CollectionUtils.isNotEmpty(trendDataVo.getDataList()))
                            .flatMap(trendDataVo -> trendDataVo.getDataList().stream())
                            .filter(datalogValue -> Objects.nonNull(datalogValue.getValue())
                                    && !Objects.equals(datalogValue.getValue(), 0.0)).findFirst();
                    if (first.isPresent()) {
                        powerData = first.get().getValue();
                    }
                }
            }
            ColdMachineData data = new ColdMachineData(id, cold, waterTemp, powerData);
            coldMachineData.add(data);
        }
        return coldMachineData;
    }

    private Map<Long,List<ColdMachineData>> getColdMachineDataBatch(Long roomId, Map<Integer, List<TrendDataVo>> temp, Map<Integer, List<TrendDataVo>> power,
                                                                    Map<Long, List<Long>> idMap, List<EnergySupplyToPo> supplyToPos, LocalDateTime time, LocalDateTime endTime) {
        Map<Long,List<ColdMachineData>> result=new HashMap<>();
        List<Map<String, Object>> maps = modelServiceUtils.queryWithChildren(NodeLabelDef.ROOM,
                Collections.singletonList(roomId), Collections.singletonList(NodeLabelDef.COLD_WATER_MAINENGINE));
        List<ColdMachineData> coldMachineData = new ArrayList<>();
        List child = (List) maps.get(0).get(ColumnDef.CHILDREN);
        List<Map> childMap = JsonTransferUtils.transferList(child, Map.class);
        List<LocalDateTime> timeRange = TimeUtil.getTimeRange(time, endTime, AggregationCycle.FIFTEEN_MINUTES);
        Map<Long, DatalogValue> tempMap = new HashMap<>();
        List<TrendDataVo> powerDataList = new ArrayList<>();

        if (Objects.nonNull(temp)) {
            List<TrendDataVo> tempData = temp.get(QuantityDef.getFreezingWaterForSupplyQuantitySetting().getId());
            if (CollectionUtils.isNotEmpty(tempData) && CollectionUtils.isNotEmpty(tempData.get(0).getDataList())) {
                List<DatalogValue> dataList = tempData.get(0).getDataList();
                tempMap = dataList.stream().collect(Collectors.toMap(DatalogValue::getTime, Function.identity()));
            }
        }
        if (Objects.nonNull(power)) {
            powerDataList = power.get(QuantityDef.getMainPowerQuantitySetting().getId());

        }
        Map<Long, List<DatalogValue>> powerMap=new HashMap<>();
        for (Map map : childMap) {
            Integer nodeId = (Integer) map.get(ColumnDef.ID);
            Long id = nodeId.longValue();
            Double cold = (Double) map.get("ratedrefrigeration");
            List<Long> longList = idMap.get(id);
            if (CollectionUtils.isEmpty(longList)) {
                continue;
            }
            List<EnergySupplyToPo> energySupplyToPos = supplyToPos.stream().filter(energySupplyToPo -> longList.contains(energySupplyToPo.getSupplytoid()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(energySupplyToPos)) {
                continue;
            }
            Set<Long> lines = energySupplyToPos.stream().map(EnergySupplyToPo::getObjectid).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(powerDataList) && CollectionUtils.isNotEmpty(lines)) {
                powerMap = powerDataList.stream().filter(trendDataVo -> lines.contains(trendDataVo.getMonitoredid())).filter(trendDataVo -> CollectionUtils.isNotEmpty(trendDataVo.getDataList()))
                        .flatMap(trendDataVo -> trendDataVo.getDataList().stream()).collect(Collectors.groupingBy(DatalogValue::getTime));
            }
            for (LocalDateTime current : timeRange) {
                Double waterTemp = null;
                Double powerData = null;
                DatalogValue data = tempMap.get(TimeUtil.localDateTime2timestamp(current));
                if (Objects.nonNull(data)) {
                    waterTemp = data.getValue();
                }
                List<DatalogValue> DatalogValue1 = powerMap.get(TimeUtil.localDateTime2timestamp(current));
                if (CollectionUtils.isNotEmpty(DatalogValue1)){
                    Optional<DatalogValue> first = DatalogValue1.stream().filter(DatalogValue -> Objects.nonNull(DatalogValue.getValue()) && !Objects.equals(DatalogValue.getValue(), 0.0)).findFirst();
                    if (first.isPresent()) {
                        powerData = first.get().getValue();
                    }
                }
                ColdMachineData coldMachineData1 = new ColdMachineData(id, cold, waterTemp, powerData);
                List<ColdMachineData> mapData = result.get(TimeUtil.localDateTime2timestamp(current));
                if (CollectionUtils.isNotEmpty(mapData)){
                    mapData.add(coldMachineData1);

                }else {
                    mapData = new ArrayList<>();
                    mapData.add(coldMachineData1);
                }
                result.put(TimeUtil.localDateTime2timestamp(current),mapData);
            }

        }
        return result;
    }

    /**
     * 请求参数
     * <p>
     * ```json
     * [
     * {
     * "project_id": null,
     * "data": [
     * {
     * "object_id": null,
     * "object_data": [
     * {
     * "rated_refrigerating": null,
     * "cooling_load": null,
     * "input_power": null,
     * "water_temp": null
     * }
     * ],
     * "max_cop":null
     * }
     * ]
     * }
     * ]
     * ```
     * <p>
     * <p>
     * <p>
     * 请求参数说明
     * <p>
     * ```yaml
     * project_id: 项目id,Long
     * data: 需要拟合的数据,List
     * object_id: 冷机id,Long
     * object_data: 冷机数据,List
     * rated_refrigerating: 冷机水组额定制冷量,Double, kW
     * cooling_load: 冷负荷,Double, kW
     * input_power: 输入功率,Double, kW
     * water_temp: 冷冻水供水温度,Double, 摄氏度
     * max_cop: 对应冷机id的最大cop
     *
     * @param time
     * @param roomId
     * @param projectId
     * @return
     */
    @Override
    public List<CopQueryParam> getCopQueryParams(LocalDateTime time, Long roomId, Long projectId) throws InstantiationException, IllegalAccessException {
        //1.冷机节点
        //2.冷机自身信息，额定制冷量 maxcop是冷机拟合结果
        List<CopMachineData> copMachineData = getCopMachineData(roomId);
        List<BaseVo> main = new ArrayList<>();
        for (CopMachineData machineData : copMachineData) {
            main.add(new BaseVo(machineData.getObjectId(), NodeLabelDef.COLD_WATER_MAINENGINE));
        }
        List<EnergySupplyToPo> energySupplyToPos = energySupplyDao.queryEnergySupplyTo(main, System.currentTimeMillis(), EnergySupplyToPo.class);
        List<BaseVo> nodes = energySupplyToPos.stream().filter(energySupplyToPo -> Objects.equals(NodeLabelDef.LINE_SEGMENT_WITH_SWITCH, energySupplyToPo.getObjectlabel()))
                .map(it -> new BaseVo(it.getObjectid(), it.getObjectlabel())).collect(Collectors.toList());
        log.info("关联冷机的一段线：{}", JsonTransferUtils.toJSONString(nodes));
        LocalDateTime st = TimeUtil.addDateTimeByCycle(time, AggregationCycle.ONE_MONTH, -1);
        //冷机功率
        Map<Integer, List<TrendDataVo>> power = quantityManageService.queryDataLogBatch(
                operationTrendService.createQuantityDataBatchSearchVo(nodes, st, time, AggregationCycle.FIFTEEN_MINUTES, QuantityDef.getMainPowerQuantitySetting()));
        //冷机冷负荷
        List<ColdActual> actuals = operationTrendService.queryEndCold(new QueryParam(roomId, NodeLabelDef.ROOM, st, time, AggregationCycle.FIFTEEN_MINUTES), projectId);
        List<TrendDataVo> powerData = power.get(getNodePowerQuantitySetting().getId());
        List<CopMachineData> copMachineDataList = assembleCopMachineData(copMachineData, energySupplyToPos, powerData, actuals, time, st);
        CopQueryParam param = new CopQueryParam();
        param.setProjectId(projectId);
        param.setData(copMachineDataList);
        return Collections.singletonList(param);
    }

    /**
     * ```json
     * [
     * {
     * "project_id": null,
     * "data": [
     * {
     * "object_id": null,
     * "pump_fit_data": [
     * {
     * "pump_power": null,
     * "total_flow": null
     * }
     * ]
     * }
     * ]
     * }
     * ]
     *
     * @param time
     * @param roomId
     * @param projectId
     * @return
     */
    @Override
    public List<PumpFitParam> getPumpFitParam(LocalDateTime time, Long roomId, Long projectId) {
        //查询冷机下对应连锁的冷冻泵功率
        //查询总管的流量
        List<DeviceChainParam> deviceChainParams = modelConfigurationService.queryAllDeviceChain(roomId, projectId);
        if (CollectionUtils.isEmpty(deviceChainParams)) {
            return Collections.emptyList();
        }
        List<NodeWithSort> nodeWithSorts = deviceChainParams.stream().filter(deviceChainParam -> CollectionUtils.isNotEmpty(deviceChainParam.getNodes()))
                .flatMap(deviceChainParam -> deviceChainParam.getNodes().stream())
                .collect(Collectors.toList());
        List<BaseVo> baseVos = nodeWithSorts.stream().filter(nodeWithSort -> Objects.equals(nodeWithSort.getModelLabel(), NodeLabelDef.PUMP))
                .map(nodeWithSort -> new BaseVo(nodeWithSort.getId(), nodeWithSort.getModelLabel())).distinct().collect(Collectors.toList());
        List<BaseVo> mains = nodeWithSorts.stream().filter(nodeWithSort -> Objects.equals(nodeWithSort.getModelLabel(), NodeLabelDef.COLD_WATER_MAINENGINE))
                .map(nodeWithSort -> new BaseVo(nodeWithSort.getId(), nodeWithSort.getModelLabel())).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(baseVos)) {
            return Collections.emptyList();
        }
        List<PumpVo> pumpVos = nodeDao.queryNodes(baseVos, PumpVo.class);
        //过滤出冷冻泵
        List<PumpVo> freezingPump = pumpVos.stream().filter(pumpVo -> Objects.equals(pumpVo.getFunctionType(), PumpFunctionType.REFRIGERATING_PUMP))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(freezingPump)) {
            return Collections.emptyList();
        }
        List<EnergySupplyToPo> energySupplyToPos = energySupplyDao.queryEnergySupplyTo(baseVos, System.currentTimeMillis(), EnergySupplyToPo.class);
        List<EnergySupplyToPo> supplyToPos = energySupplyToPos.stream().filter(energySupplyToPo -> Objects.equals(NodeLabelDef.LINE_SEGMENT_WITH_SWITCH, energySupplyToPo.getObjectlabel()))
                .collect(Collectors.toList());
        List<BaseVo> nodes = supplyToPos.stream().map(energySupplyToPo -> new BaseVo(energySupplyToPo.getObjectid(), energySupplyToPo.getObjectlabel())).distinct().collect(Collectors.toList());
        //查询总管
        List<BaseVo> totalPipeline = getTotalPipeline(mains, projectId);
        LocalDateTime st = TimeUtil.addDateTimeByCycle(time, AggregationCycle.ONE_MONTH, -1);
        //pump功率--要查一段线
        Map<Integer, List<TrendDataVo>> power = quantityManageService.queryDataLogBatch(
                operationTrendService.createQuantityDataBatchSearchVo(nodes, st, time, AggregationCycle.FIFTEEN_MINUTES, QuantityDef.getMainPowerQuantitySetting()));
        //查询流量
        Map<Integer, List<TrendDataVo>> stream = quantityManageService.queryDataLogBatch(
                operationTrendService.createQuantityDataBatchSearchVo(totalPipeline, st, time, AggregationCycle.FIFTEEN_MINUTES, QuantityDef.getColdPipelineStream()));
        List<Long> timeRange = TimeUtil.getTimeRange(TimeUtil.localDateTime2timestamp(st), TimeUtil.localDateTime2timestamp(time), AggregationCycle.FIFTEEN_MINUTES);
        return assemblePumpFitParam(power, stream, createColdWithPump(deviceChainParams, freezingPump)
                , supplyToPos, timeRange, projectId);

    }

    @Override
    public List<ColdMachineControlParam> getColdMainControlQueryParamsBatch(LocalDateTime time, LocalDateTime endTime, Long roomId, Long projectId) throws Exception {
        List<ColdLoadQueryParam> coldPredictQueryParam = getColdPredictQueryParamBatch(time, endTime, roomId, projectId);
        if (CollectionUtils.isEmpty(coldPredictQueryParam)) {
            return Collections.emptyList();
        }
        List<DeviceChainParam> deviceChainParams = modelConfigurationService.queryAllDeviceChain(roomId, projectId);
        if (CollectionUtils.isEmpty(deviceChainParams)) {
            return Collections.emptyList();
        }
        List<NodeWithSort> nodeWithSorts = deviceChainParams.stream().filter(deviceChainParam -> CollectionUtils.isNotEmpty(deviceChainParam.getNodes()))
                .flatMap(deviceChainParam -> deviceChainParam.getNodes().stream())
                .collect(Collectors.toList());
        List<BaseVo> baseVos = nodeWithSorts.stream().filter(nodeWithSort -> Objects.equals(nodeWithSort.getModelLabel(), NodeLabelDef.PUMP))
                .map(nodeWithSort -> new BaseVo(nodeWithSort.getId(), nodeWithSort.getModelLabel())).distinct().collect(Collectors.toList());
        List<BaseVo> mains = nodeWithSorts.stream().filter(nodeWithSort -> Objects.equals(nodeWithSort.getModelLabel(), NodeLabelDef.COLD_WATER_MAINENGINE))
                .map(nodeWithSort -> new BaseVo(nodeWithSort.getId(), nodeWithSort.getModelLabel())).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(baseVos)) {
            return Collections.emptyList();
        }
        List<PumpVo> pumpVos = nodeDao.queryNodes(baseVos, PumpVo.class);
        //过滤出冷冻泵
        List<PumpVo> freezingPump = pumpVos.stream().filter(pumpVo -> Objects.equals(pumpVo.getFunctionType(), PumpFunctionType.REFRIGERATING_PUMP))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(freezingPump)) {
            return Collections.emptyList();
        }
        List<EnergySupplyToPo> energySupplyToPos = energySupplyDao.queryEnergySupplyTo(baseVos, System.currentTimeMillis(), EnergySupplyToPo.class);
        List<EnergySupplyToPo> supplyToPos = energySupplyToPos.stream().filter(energySupplyToPo -> Objects.equals(NodeLabelDef.LINE_SEGMENT_WITH_SWITCH, energySupplyToPo.getObjectlabel()))
                .collect(Collectors.toList());
        List<BaseVo> nodes = supplyToPos.stream().map(energySupplyToPo -> new BaseVo(energySupplyToPo.getObjectid(), energySupplyToPo.getObjectlabel())).distinct().collect(Collectors.toList());
        //补充查询冷冻水供水水温度和冷冻泵功率
        //冷冻水供水水温度
        Map<Integer, List<TrendDataVo>> temp = quantityManageService.queryDataLogBatch(
                operationTrendService.createQuantityDataBatchSearchVo(mains, time, endTime,
                        AggregationCycle.FIFTEEN_MINUTES, QuantityDef.getFreezingWaterForSupplyQuantitySetting()));
        //冷冻泵功率
        Map<Integer, List<TrendDataVo>> power = quantityManageService.queryDataLogBatch(
                operationTrendService.createQuantityDataBatchSearchVo(nodes, time, endTime, AggregationCycle.FIFTEEN_MINUTES,
                        QuantityDef.getMainPowerQuantitySetting()));

        ColdMachineControlParam param = new ColdMachineControlParam();
        Map<Long, List<ColdMachineData>> coldMachineDataBatch = getColdMachineDataBatch(roomId, temp, power, createColdWithPump(deviceChainParams, freezingPump), energySupplyToPos, time, endTime);
        List<ColdLoadParam> predictData = coldPredictQueryParam.get(0).getPredictData();

        if (CollectionUtils.isNotEmpty(predictData)) {
            for (ColdLoadParam coldLoadParam:predictData){
                coldLoadParam.setColdMachineData(coldMachineDataBatch.get(coldLoadParam.getLogTime()));
            }
        } else {
            param.setColdLoadParams(predictData);
        }
        List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.querySystemByRoomId(roomId, projectId);
        SwitchLimit switchLimit = new SwitchLimit(refrigeratingSystems.get(0).getOutsideTempLimit(), refrigeratingSystems.get(0).getCoolingLoadDemandLimit());
        param.setSwitchLimit(switchLimit);
        param.setProjectId(projectId);
        param.setColdLoadParams(predictData);
        return Collections.singletonList(param);
    }

    @Override
    public List<ColdLoadQueryParam> getColdPredictQueryParamBatch(LocalDateTime time, LocalDateTime endTime, Long roomId, Long projectId) throws Exception {
        //1.温度湿度通过查询气象监测仪的定时记录
        //2.冷负荷通过末端冷负荷*1.01
        //3.车间用电功率，通过3个厂房关联的一段线的功率
        QueryParam queryParam = new QueryParam(roomId, NodeLabelDef.ROOM,
                time, endTime, AggregationCycle.FIFTEEN_MINUTES);

        List<BaseVo> monitor = queryNodesByMonitor(projectId);
        //冷机功率
        Map<Integer, List<TrendDataVo>> temp = quantityManageService.queryDataLogBatch(
                operationTrendService.createQuantityDataBatchSearchVo(monitor, time, endTime,
                        AggregationCycle.FIFTEEN_MINUTES, QuantityDef.getAiPredictForTempQuantitySetting()));
        //冷机供水温度
        Map<Integer, List<TrendDataVo>> hum = quantityManageService.queryDataLogBatch(
                operationTrendService.createQuantityDataBatchSearchVo(monitor, time, endTime, AggregationCycle.FIFTEEN_MINUTES,
                        QuantityDef.getAiPredictForHumQuantitySetting()));
        //冷机冷负荷
        List<ColdActual> actuals = operationTrendService.queryEndCold(new QueryParam(roomId, NodeLabelDef.ROOM, time,
                endTime, AggregationCycle.FIFTEEN_MINUTES), projectId);

        List<BaseVo> building = getBuilding();
        List<EnergySupplyToPo> energySupplyToPos = energySupplyDao.queryEnergySupplyTo(building, System.currentTimeMillis(), EnergySupplyToPo.class);
        List<BaseVo> nodes = energySupplyToPos.stream().filter(energySupplyToPo -> Objects.equals(NodeLabelDef.LINE_SEGMENT_WITH_SWITCH, energySupplyToPo.getObjectlabel()))
                .map(it -> new BaseVo(it.getObjectid(), it.getObjectlabel())).collect(Collectors.toList());
        Map<Long, List<DatalogValue>> powerSumBatch = getPowerSumBatch(nodes, queryParam);

        return assembleColdLoadQueryParamBatch(temp, hum, time, endTime, powerSumBatch, projectId, actuals);
    }

    private Map<Long, List<Long>> createColdWithPump(List<DeviceChainParam> deviceChainParams, List<PumpVo> freezingPump) {
        Set<Long> pumpId = freezingPump.stream().map(PumpVo::getId).collect(Collectors.toSet());
        Map<Long, List<Long>> map = new HashMap<>();
        for (DeviceChainParam param : deviceChainParams) {
            List<NodeWithSort> nodes = param.getNodes();
            if (CollectionUtils.isEmpty(nodes)) {
                continue;
            }
            Optional<NodeWithSort> any = nodes.stream().filter(nodeWithSort -> Objects.equals(nodeWithSort.getModelLabel(), NodeLabelDef.COLD_WATER_MAINENGINE))
                    .findAny();
            List<Long> pumpIdSingle = nodes.stream().filter(nodeWithSort -> Objects.equals(nodeWithSort.getModelLabel(), NodeLabelDef.PUMP)
                    && pumpId.contains(nodeWithSort.getId())).map(NodeWithSort::getId).collect(Collectors.toList());
            any.ifPresent(nodeWithSort -> map.put(nodeWithSort.getId(), pumpIdSingle));
        }
        return map;
    }

    private List<PumpFitParam> assemblePumpFitParam(Map<Integer, List<TrendDataVo>> power, Map<Integer, List<TrendDataVo>> stream, Map<Long, List<Long>> map
            , List<EnergySupplyToPo> supplyToPos, List<Long> timeList, Long projectId) {
        if (Objects.isNull(power) || Objects.isNull(stream)) {
            return Collections.emptyList();
        }
        List<TrendDataVo> trendDataVos = power.get(QuantityDef.getMainPowerQuantitySetting().getId());
        List<TrendDataVo> streamTrendDataVos = stream.get(QuantityDef.getColdPipelineStream().getId());
        if (CollectionUtils.isEmpty(streamTrendDataVos)) {
            streamTrendDataVos = new ArrayList<>();
        }
        Map<Long, Double> streamMap = streamTrendDataVos.stream().filter(trendDataVo -> CollectionUtils.isNotEmpty(trendDataVo.getDataList()))
                .flatMap(trendDataVo -> trendDataVo.getDataList().stream()).filter(DatalogValue -> Objects.nonNull(DatalogValue.getTime())
                        && Objects.nonNull(DatalogValue.getValue())).collect(Collectors.toMap(DatalogValue::getTime, DatalogValue::getValue, (v1, v2) -> v1));
        List<PumpFitObjectData> pumpFitObjectData = new ArrayList<>();
        for (Map.Entry<Long, List<Long>> entry : map.entrySet()) {
            List<EnergySupplyToPo> energySupplyToPos = supplyToPos.stream().filter(energySupplyToPo -> entry.getValue().contains(energySupplyToPo.getSupplytoid()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(energySupplyToPos)) {
                continue;
            }
            List<PumpFitData> pumpFitData = new ArrayList<>();
            List<Long> ids = energySupplyToPos.stream().map(EnergySupplyToPo::getObjectid).collect(Collectors.toList());
            List<TrendDataVo> dataVos = trendDataVos.stream().filter(trendDataVo -> ids.contains(trendDataVo.getMonitoredid())).collect(Collectors.toList());
            Map<Long, List<DatalogValue>> dataLogMap = dataVos.stream().filter(trendDataVo -> CollectionUtils.isNotEmpty(trendDataVo.getDataList()))
                    .flatMap(trendDataVo -> trendDataVo.getDataList().stream()).collect(Collectors.groupingBy(DatalogValue::getTime));
            for (Long time : timeList) {
                List<DatalogValue> datalogValue = dataLogMap.get(time);
                Double value = null;
                if (CollectionUtils.isNotEmpty(datalogValue)) {
                    value = datalogValue.stream().filter(DatalogValue1 -> Objects.nonNull(DatalogValue1.getValue()))
                            .mapToDouble(DatalogValue::getValue).sum();
                }
                PumpFitData fitData = new PumpFitData(streamMap.get(time), value);
                pumpFitData.add(fitData);
            }
            PumpFitObjectData fitObjectData = new PumpFitObjectData(entry.getKey(), pumpFitData);
            pumpFitObjectData.add(fitObjectData);
        }
        return Collections.singletonList(new PumpFitParam(projectId, pumpFitObjectData));
    }

    private List<BaseVo> getTotalPipeline(List<BaseVo> mains, Long projectId) {
        if (CollectionUtils.isEmpty(mains)) {
            return Collections.emptyList();
        }
        List<PipeNetworkConnectionModel> connectionModels = connectionModelDao.
                getTopOrDownBatchWithNodes(mains, false, projectId);
        return connectionModels.stream().filter(model -> Objects.equals(model.getOutflowLabel(), NodeLabelDef.PIPELINE))
                .map(model -> new BaseVo(model.getOutflowId(), model.getOutflowLabel()))
                .collect(Collectors.toList());
    }

    private List<CopMachineData> assembleCopMachineData(List<CopMachineData> copMachineData, List<EnergySupplyToPo> energySupplyToPos,
                                                        List<TrendDataVo> powerData, List<ColdActual> coldData,
                                                        LocalDateTime time, LocalDateTime st) {
        if (CollectionUtils.isEmpty(coldData)) {
            return Collections.emptyList();
        }
        List<CopMachineData> result = new ArrayList<>();
        Map<Long, List<EnergySupplyToPo>> map = energySupplyToPos.stream().filter(energySupplyToPo -> Objects.equals(NodeLabelDef.LINE_SEGMENT_WITH_SWITCH, energySupplyToPo.getObjectlabel()))
                .collect(Collectors.groupingBy(EnergySupplyToPo::getSupplytoid));
        List<LocalDateTime> timeRange = TimeUtil.getTimeRange(st, time, AggregationCycle.FIFTEEN_MINUTES);
        Map<LocalDateTime, List<ColdActual>> coldMap = coldData.stream().collect(Collectors.groupingBy(ColdActual::getLogTime));
        for (CopMachineData machineData : copMachineData) {
            Long objectId = machineData.getObjectId();
            List<CopObjectData> objectDataList = new ArrayList<>();
            CopObjectData objectData = machineData.getCopObjectData().get(0);
            Double ratedRefrigerating = objectData.getRatedRefrigerating();
            List<EnergySupplyToPo> energySupplyToPos1 = map.get(objectId);
            if (CollectionUtils.isEmpty(energySupplyToPos1)) {
                continue;
            }
            Set<BaseVo> baseVos = energySupplyToPos1.stream().map(energySupplyToPo -> new BaseVo(energySupplyToPo.getObjectid(), energySupplyToPo.getObjectlabel()))
                    .collect(Collectors.toSet());
            Map<Long, List<DatalogValue>> powerMap = powerData.stream().filter(trendDataVo -> baseVos.contains(new BaseVo(trendDataVo.getMonitoredid(), trendDataVo.getMonitoredlabel())))
                    .flatMap(trendDataVo -> trendDataVo.getDataList().stream())
                    .collect(Collectors.groupingBy(DatalogValue::getTime));
            for (LocalDateTime now : timeRange) {
                Double cold = null;
                List<ColdActual> coldDataLog = coldMap.get((now));
                if (CollectionUtils.isNotEmpty(coldDataLog)) {
                    cold = coldDataLog.stream().filter(DatalogValue -> Objects.nonNull(DatalogValue.getValue()))
                            .mapToDouble(ColdActual::getValue).sum();
                }

                Double power = null;
                List<DatalogValue> powerDataLog = powerMap.get(TimeUtil.localDateTime2timestamp(now));
                if (CollectionUtils.isNotEmpty(powerDataLog)) {
                    power = powerDataLog.stream().filter(DatalogValue -> Objects.nonNull(DatalogValue.getValue()))
                            .mapToDouble(DatalogValue::getValue).sum();
                }
                //都不能为null
                if (Objects.nonNull(cold)) {
                    cold = Math.abs(cold * 1.01);

                }
                CopObjectData data = new CopObjectData(ratedRefrigerating, cold, power);
                objectDataList.add(data);
            }
            machineData.setCopObjectData(objectDataList);
            if (CollectionUtils.isNotEmpty(objectDataList)) {
                result.add(machineData);
            }
        }
        return result;
    }


    private List<CopMachineData> getCopMachineData(Long roomId) {
        List<Map<String, Object>> maps = modelServiceUtils.queryWithChildren(NodeLabelDef.ROOM,
                Collections.singletonList(roomId), Collections.singletonList(NodeLabelDef.COLD_WATER_MAINENGINE));
        List<CopMachineData> coldMachineData = new ArrayList<>();
        List child = (List) maps.get(0).get(ColumnDef.CHILDREN);
        List<Map> childMap = JsonTransferUtils.transferList(child, Map.class);
        for (Map map : childMap) {
            CopMachineData machineData = new CopMachineData();
            Integer o = (Integer) map.get(ColumnDef.ID);
            Long id = o.longValue();
            Double cold = (Double) map.get("ratedrefrigeration");
            machineData.setObjectId(id);
            CopObjectData objectData = new CopObjectData();
            objectData.setRatedRefrigerating(cold);
            List<CopObjectData> objectDataList = new ArrayList<>();
            objectDataList.add(objectData);
            machineData.setCopObjectData(objectDataList);
            coldMachineData.add(machineData);
        }
        return coldMachineData;
    }

    /**
     * 冷水主机冷机负荷物理量
     *
     * @return
     */
    private QuantitySearchVo getMainCoolingLoadQuantitySetting() {
        return new QuantitySearchVo(6008010,
                QuantityCategoryDef.POWER,
                QuantityTypeDef.POWER,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.COLD);
    }

    /**
     * 冷冻水供水物理量
     *
     * @return
     */
    private QuantitySearchVo getFreezingWaterForSupplyQuantitySetting() {
        return new QuantitySearchVo(6007206,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.SUPPLY,
                47);
    }
}