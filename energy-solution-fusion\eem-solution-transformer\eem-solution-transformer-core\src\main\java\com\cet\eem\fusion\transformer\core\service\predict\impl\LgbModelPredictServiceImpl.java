package com.cet.eem.fusion.transformer.core.service.predict.impl;

import com.cet.eem.bll.energysaving.feign.LgbModelService;
import com.cet.eem.bll.energysaving.model.dataentryquery.*;
import com.cet.eem.bll.energysaving.model.predict.WeatherEntry;
import com.cet.eem.bll.energysaving.model.predict.WeatherReturn;
import com.cet.eem.bll.energysaving.model.weather.ForecastBasicWeatherDataVo;
import com.cet.eem.bll.energysaving.model.weather.ForecastBasicWeatherQueryVo;
import com.cet.eem.bll.energysaving.model.weather.ForecastWeather;
import com.cet.eem.bll.energysaving.model.weather.MeasureWeather;
import com.cet.eem.bll.energysaving.service.predict.LgbModelPredictService;
import com.cet.eem.bll.energysaving.service.trend.DataEntryAndQueryService;
import com.cet.eem.bll.energysaving.service.weather.WeatherCrawlingDataService;
import com.cet.eem.common.model.Result;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.weather.dao.WeatherDao;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName : LgbModelPredictServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-07-12 10:02
 */
@Service
public class LgbModelPredictServiceImpl implements LgbModelPredictService {
    @Autowired
    LgbModelService lgbModelService;
    @Autowired
    WeatherDao weatherDao;
    @Autowired
    DataEntryAndQueryService dataEntryAndQueryService;
    @Autowired
    WeatherCrawlingDataService weatherCrawlingDataService;

    @Override
    public WeatherReturn getWeatherPredict(WeatherEntry entry) {
        return lgbModelService.getHumidityPredict(JsonTransferUtils.toJSONString(entry));
    }

    @Override
    public WeatherReturn getWeatherPredictOfTemperature(WeatherEntry entry) {

        return lgbModelService.getTemperaturePredict(JsonTransferUtils.toJSONString(entry));
    }

    @Override
    public DataLogDataWrite getWeatherPredictData(ForecastBasicWeatherQueryVo query, Long projectId) throws Exception {
        List<ForecastBasicWeatherDataVo> forecastBasicWeatherDataVos = weatherCrawlingDataService.queryWeather(query, projectId);
        Result<List<ForecastBasicWeatherDataVo>> ok = Result.ok(forecastBasicWeatherDataVos);
        return lgbModelService.getWeatherPredict(ok);
    }

    @Override
    public DataLogDataWrite getWeatherPredictData(Result<List<ForecastBasicWeatherDataVo>> ok) {
        return lgbModelService.getWeatherPredict(ok);
    }


    private void setZero(List<ForecastWeather> data) {
        for (ForecastWeather weather : data) {
            if (Objects.isNull(weather.getHumidity())) {
                weather.setHumidity(0.0);
            }
            if (Objects.isNull(weather.getTemp())) {
                weather.setTemp(0.0);
            }
        }
    }

    private void setZeroData(List<MeasureWeather> measureWeathers) {
        for (MeasureWeather weather : measureWeathers) {
            if (Objects.isNull(weather.getHumidity())) {
                weather.setHumidity(0.0);
            }
            if (Objects.isNull(weather.getTemp())) {
                weather.setTemp(0.0);
            }
        }
    }

    @Override
    public DataLogDataWrite getWeatherPredictDataOfTemperature(ForecastBasicWeatherQueryVo query, Long projectId) throws Exception {
        List<ForecastBasicWeatherDataVo> forecastBasicWeatherDataVos = weatherCrawlingDataService.queryWeather(query, projectId);
        return lgbModelService.getWeatherPredictOfTemp(Result.ok(forecastBasicWeatherDataVos));
    }

    @Override
    public DataLogDataWrite getEndColdPredictData(DataQueryParam queryParam,  Long projectId) throws InstantiationException, IllegalAccessException {
        List<EndColdDataEntry> entries = dataEntryAndQueryService.queryEndColdData(queryParam, projectId);
//        filterNullDataEndCold(entries, time);
        return lgbModelService.getEndColdPredictData(Result.ok(entries));
    }

    @Override
    public DataLogDataWrite getEndColdPredictData(Result<List<EndColdDataEntry>> entries) {
        return lgbModelService.getEndColdPredictData(entries);
    }


    private void setZeroDataDataLogData(List<DatalogValue> measureWeathers) {
        for (DatalogValue weather : measureWeathers) {
            if (Objects.isNull(weather.getValue())) {
                weather.setValue(0.0);
            }

        }
    }

    @Override
    public DataLogDataWrite getPipelineLossColdPredictData(DataQueryParam queryParam, Long projectId) {
        List<PipelineLossColdDataEntry> pipelineLossColdDataEntries = dataEntryAndQueryService.queryPipelineLossColdData(queryParam, projectId);
//        filterNullPipeline(pipelineLossColdDataEntries);
        return lgbModelService.getPipelineLossColdPredictData(Result.ok(pipelineLossColdDataEntries));
    }

    private void filterNullPipeline(List<PipelineLossColdDataEntry> pipelineLossColdDataEntries) {
        for (PipelineLossColdDataEntry entry : pipelineLossColdDataEntries) {
            List<DatalogValue> humidityPredict = entry.getHumidityPredict();
            List<DatalogValue> pipelineCold = entry.getPipelineCold();
            List<DatalogValue> tempPredict = entry.getTempPredict();
            setZeroDataDataLogData(humidityPredict);
            setZeroDataDataLogData(tempPredict);
            setZeroDataDataLogData(pipelineCold);
        }
    }

    @Override
    public List<DataLogDataWrite> getOptimizationOfRefrigeratorWaterPredict(DataQueryParam queryParam, Long projectId) throws InstantiationException, IllegalAccessException {
        Result<List<OptimizationOfRefrigeratorWaterEntry>> ok = Result.ok(dataEntryAndQueryService.queryOptimizationOfRefrigeratorWater(queryParam, projectId));
        return Collections.singletonList(lgbModelService.getOptimizationOfRefrigeratorWaterPredict(ok));
    }

    @Override
    public DataLogDataWrite getTotalSystemPowerPredict(DataQueryParam queryParam, Long projectId) throws InstantiationException, IllegalAccessException {
        List<TotalSystemPowerEntry> entries = dataEntryAndQueryService.queryTotalSystemPower(queryParam, projectId);
        return lgbModelService.getTotalSystemPowerPredict(Result.ok(entries));
    }

    @Override
    public DataLogDataWrite getTotalSystemPowerPredict(Result<List<TotalSystemPowerEntry>> entries) {
        return lgbModelService.getTotalSystemPowerPredict((entries));
    }


}