# Working Compilation Error Checker - Simple and Reliable
param(
    [string]$ModulePath = "energy-solution-fusion\eem-solution-transformer\eem-solution-transformer-core",
    [string]$OutputFile = "working-compilation-errors.md"
)

# Function to get classpath from Maven
function Get-ClassPath {
    param([string]$pomPath)
    try {
        $cpOutput = & mvn dependency:build-classpath -f $pomPath -q 2>&1
        $classpath = ""
        foreach ($line in $cpOutput) {
            if ($line -match "^[A-Z]:" -or $line -match "^/") {
                $classpath = $line.ToString()
                break
            }
        }
        return $classpath
    } catch {
        return ""
    }
}

Write-Host "Working Compilation Error Checker" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# Check paths
if (-not (Test-Path $ModulePath)) {
    Write-Host "ERROR: Module path not found: $ModulePath" -ForegroundColor Red
    exit 1
}

$pomPath = Join-Path $ModulePath "pom.xml"
if (-not (Test-Path $pomPath)) {
    Write-Host "ERROR: pom.xml not found: $pomPath" -ForegroundColor Red
    exit 1
}

Write-Host "Analyzing: $ModulePath" -ForegroundColor Yellow

# Get compilation errors
$compilationErrors = @()
$processedErrors = @{}

Write-Host "Running Maven compilation..." -ForegroundColor Yellow
try {
    # First try with -fae (fail at end) to continue compiling all files even if some fail
    $compileOutput = & mvn compile -f $pomPath -fae 2>&1

    # If compilation fails due to severe errors, try individual file compilation
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Initial compilation failed, trying individual file analysis..." -ForegroundColor Yellow

        # Get all Java files
        $javaFiles = Get-ChildItem -Path (Join-Path $ModulePath "src\main\java") -Filter "*.java" -Recurse
        $individualOutput = @()

        foreach ($javaFile in $javaFiles) {
            try {
                # Try to compile each file individually to get more detailed errors
                $singleFileOutput = & javac -cp (Get-ClassPath $pomPath) -d (Join-Path $ModulePath "target\classes") $javaFile.FullName 2>&1
                if ($LASTEXITCODE -ne 0) {
                    $individualOutput += $singleFileOutput
                }
            } catch {
                Write-Host "Warning: Could not compile $($javaFile.Name) individually" -ForegroundColor Yellow
            }
        }

        # Combine outputs
        $compileOutput = $compileOutput + $individualOutput
    }
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Compilation FAILED - extracting errors..." -ForegroundColor Red
        
        foreach ($line in $compileOutput) {
            $lineStr = $line.ToString()
            
            # Parse error lines - improved regex to catch more error formats
            if ($lineStr -match "\[ERROR\]\s+.*[\\\/]([^\\\/]+\.java):\[(\d+),(\d+)\]" -or
                $lineStr -match "\[ERROR\]\s+.*[\\\/]([^\\\/]+\.java):\[(\d+)\]") {
                $fileName = $matches[1]
                $lineNum = $matches[2]
                $colNum = if ($matches[3]) { $matches[3] } else { "0" }
                
                # Avoid duplicates
                $errorKey = "$fileName-$lineNum"
                if ($processedErrors.ContainsKey($errorKey)) {
                    continue
                }
                $processedErrors[$errorKey] = $true
                
                # Get code snippet with better error handling
                $codeSnippet = ""
                try {
                    $javaFilePath = Join-Path $ModulePath "src\main\java"
                    $fullJavaFiles = Get-ChildItem -Path $javaFilePath -Filter $fileName -Recurse
                    if ($fullJavaFiles.Count -gt 0) {
                        $fileContent = Get-Content $fullJavaFiles[0].FullName -Encoding UTF8 -ErrorAction Stop
                        $lineIndex = [int]$lineNum - 1
                        if ($lineIndex -ge 0 -and $lineIndex -lt $fileContent.Count) {
                            $codeSnippet = $fileContent[$lineIndex].Trim()
                            # If the line is empty or just whitespace, try to get context
                            if ([string]::IsNullOrWhiteSpace($codeSnippet)) {
                                # Try to get the previous non-empty line for context
                                for ($i = $lineIndex - 1; $i -ge 0 -and $i -ge ($lineIndex - 3); $i--) {
                                    if (-not [string]::IsNullOrWhiteSpace($fileContent[$i])) {
                                        $codeSnippet = $fileContent[$i].Trim()
                                        break
                                    }
                                }
                            }
                        }
                    }
                } catch {
                    Write-Host "Warning: Could not read line $lineNum from $fileName" -ForegroundColor Yellow
                }
                
                # Determine error type
                $errorType = "Compilation Error"
                $errorDesc = "Compilation error"
                
                if ($lineStr -match "package.*does not exist") {
                    $errorType = "Missing Package"
                    $errorDesc = "Package does not exist"
                }
                elseif ($lineStr -match "cannot find symbol") {
                    $errorType = "Missing Symbol"
                    $errorDesc = "Cannot find symbol"
                }
                elseif ($lineStr -match "utf-8.*字符" -or $lineStr -match "unmappable character" -or $lineStr -match "非法字符") {
                    $errorType = "Character Encoding"
                    $errorDesc = "UTF-8 character encoding issue"
                }
                elseif ($lineStr -match "需要class, interface或enum") {
                    $errorType = "Syntax Error"
                    $errorDesc = "Syntax error - expected class, interface or enum"
                }
                
                $compilationErrors += [PSCustomObject]@{
                    File = $fileName
                    Line = [int]$lineNum
                    Type = $errorType
                    Description = $errorDesc
                    Code = $codeSnippet
                }
            }
        }
    } else {
        Write-Host "Compilation SUCCESS" -ForegroundColor Green
    }
} catch {
    Write-Host "Maven execution failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Additional static analysis for import issues
Write-Host "Running static analysis for import issues..." -ForegroundColor Yellow
$javaFiles = Get-ChildItem -Path (Join-Path $ModulePath "src\main\java") -Filter "*.java" -Recurse
$importIssues = @()

foreach ($javaFile in $javaFiles) {
    try {
        $content = Get-Content $javaFile.FullName -Encoding UTF8
        $lineNumber = 0

        foreach ($line in $content) {
            $lineNumber++
            $trimmedLine = $line.Trim()

            # Check for common import issues
            if ($trimmedLine -match "^import\s+") {
                # Check for old package references that might need updating
                if ($trimmedLine -match "com\.cet\.eem\.transformer\." -and
                    -not $trimmedLine -match "com\.cet\.eem\.fusion\.transformer\.") {
                    $importIssues += [PSCustomObject]@{
                        File = $javaFile.Name
                        Line = $lineNumber
                        Type = ""
                        Description = "Import uses old package structure"
                        Code = $trimmedLine
                    }
                }

                # Check for potentially missing imports (common patterns)
                if ($trimmedLine -match "com\.cet\.eem\.bll\." -or
                    $trimmedLine -match "com\.cet\.eem\.event\." -or
                    $trimmedLine -match "com\.cet\.eem\.quantity\.") {
                    # These might be missing in the new structure
                    $importIssues += [PSCustomObject]@{
                        File = $javaFile.Name
                        Line = $lineNumber
                        Type = ""
                        Description = "Import may not be available in fusion framework"
                        Code = $trimmedLine
                    }
                }
            }
        }
    } catch {
        Write-Host "Warning: Could not analyze $($javaFile.Name)" -ForegroundColor Yellow
    }
}

# Merge compilation errors and import issues
$allIssues = $compilationErrors + $importIssues

# Remove duplicates based on File and Line combination
Write-Host "Removing duplicate issues..." -ForegroundColor Yellow
$uniqueIssues = @()
$seenIssues = @{}

foreach ($issue in $allIssues) {
    $key = "$($issue.File):$($issue.Line)"

    if (-not $seenIssues.ContainsKey($key)) {
        $seenIssues[$key] = $true
        $uniqueIssues += $issue
    } else {
        Write-Host "Removed duplicate: $($issue.File) line $($issue.Line)" -ForegroundColor Gray
    }
}

$allIssues = $uniqueIssues
Write-Host "After deduplication: $($allIssues.Count) unique issues" -ForegroundColor Green

# Create report
$currentTime = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'

$report = "# Java Compilation Errors Report`n`n"
$report += "**Generated:** $currentTime`n"
$report += "**Module:** $ModulePath`n"
$report += "**Total Issues:** $($allIssues.Count) (Compilation: $($compilationErrors.Count), Import Analysis: $($importIssues.Count))`n`n"

if ($allIssues.Count -gt 0) {
    $report += "## ISSUES FOUND`n`n"

    # Group by file
    $errorsByFile = $allIssues | Group-Object File | Sort-Object Name
    
    foreach ($fileGroup in $errorsByFile) {
        $report += "### $($fileGroup.Name) ($($fileGroup.Count) issues)`n`n"

        $sortedErrors = $fileGroup.Group | Sort-Object Line
        $errorNum = 1
        foreach ($compError in $sortedErrors) {
            if ($compError.Type -and -not [string]::IsNullOrWhiteSpace($compError.Type)) {
                $report += "**Issue $errorNum - Line $($compError.Line):** $($compError.Type)`n`n"
            } else {
                $report += "**Issue $errorNum - Line $($compError.Line):**`n`n"
            }

            if ($compError.Code -and -not [string]::IsNullOrWhiteSpace($compError.Code)) {
                $report += "``$($compError.Code)```n`n"
            } else {
                $report += "- **Location:** $($compError.File):$($compError.Line)`n"
                $report += "- **Note:** Could not retrieve source code for this line`n`n"
            }

            $errorNum++
        }
    }
    
    $report += "## SUMMARY`n`n"
    $errorsByType = $allIssues | Group-Object Type
    foreach ($typeGroup in $errorsByType) {
        $report += "- **$($typeGroup.Name):** $($typeGroup.Count) issues`n"
    }
    $report += "`n"
    
} else {
    $report += "## NO ISSUES FOUND`n`n"
    $report += "All Java files compiled successfully and no import issues detected.`n`n"
}

$report += "---`n"
$report += "**Generated:** $currentTime`n"

# Save report
try {
    $fullPath = Join-Path (Get-Location) $OutputFile
    [System.IO.File]::WriteAllText($fullPath, $report, [System.Text.Encoding]::UTF8)
    Write-Host "SUCCESS: Report saved to $OutputFile" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to save report" -ForegroundColor Red
    exit 1
}

# Console summary
Write-Host "`n=================================" -ForegroundColor Green
Write-Host "ANALYSIS COMPLETE" -ForegroundColor Green
Write-Host "Total issues: $($allIssues.Count) (Compilation: $($compilationErrors.Count), Import: $($importIssues.Count))" -ForegroundColor $(if ($allIssues.Count -gt 0) { "Red" } else { "Green" })

if ($allIssues.Count -gt 0) {
    $errorsByFile = $allIssues | Group-Object File | Sort-Object Count -Descending
    Write-Host "`nFiles with issues:" -ForegroundColor Yellow
    foreach ($fileGroup in $errorsByFile) {
        Write-Host "- $($fileGroup.Name): $($fileGroup.Count) issues" -ForegroundColor Red
    }

    $errorsByType = $allIssues | Group-Object Type
    Write-Host "`nIssue types:" -ForegroundColor Yellow
    foreach ($typeGroup in $errorsByType) {
        Write-Host "- $($typeGroup.Name): $($typeGroup.Count)" -ForegroundColor Yellow
    }
}

Write-Host "`nReport: $OutputFile" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Green
