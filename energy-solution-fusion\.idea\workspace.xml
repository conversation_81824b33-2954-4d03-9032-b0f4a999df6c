<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="930225a8-cea5-4bcb-9197-cc7f9d30914d" name="Changes" comment="提示词完善补充">
      <change beforePath="$PROJECT_DIR$/eem-solution-common/src/main/java/com/cet/eem/solution/common/def/common/PluginInfoDef.java" beforeDir="false" afterPath="$PROJECT_DIR$/eem-solution-common/src/main/java/com/cet/eem/solution/common/def/common/PluginInfoDef.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/rules/02能管融合适配任务.md" beforeDir="false" afterPath="$PROJECT_DIR$/rules/02能管融合适配任务.md" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\work\software\maven\apache-maven-3.5.4" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="E:\work\software\maven\apache-maven-3.5.4\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="30OzZFrBTQooTbc2DqOhfx5PCDm" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.eem-solution-air-compress-core [clean].executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;SONARLINT_PRECOMMIT_ANALYSIS&quot;: &quot;false&quot;,
    &quot;Spring Boot.AirCompressServiceApplication (1).executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.AirCompressServiceApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.DemoServiceApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.MaintenanceServiceApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.TransformerServiceApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/work/project/ai-solution-eem-service/energy-solution-fusion/eem-solution-transformer/eem-solution-transformer-core/src/main/java/com/cet/eem/fusion/transformer/core/service&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.3643678&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;Errors&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\work\project\ai-solution-eem-service\energy-solution-fusion\eem-solution-transformer\eem-solution-transformer-core\src\main\java\com\cet\eem\fusion\transformer\core\service" />
      <recent name="E:\work\project\ai-solution-eem-service\energy-solution-fusion\eem-solution-common\src\main\java\com\cet\eem\solution\common\entity\vo" />
      <recent name="E:\work\project\ai-solution-eem-service\energy-solution-fusion\eem-solution-production-expansion\eem-solution-production-expansion-core\src\main\java\com\cet\eem\fusion\productionexpansion\core\entity\po" />
      <recent name="E:\work\project\ai-solution-eem-service\energy-solution-fusion\eem-solution-common\src\main\java\com\cet\eem\solution\common\entity" />
      <recent name="E:\work\project\ai-solution-eem-service\energy-solution-fusion\eem-solution-common\src\main\java\com\cet\eem\solution\common\feign" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.cet.eem.fusion.transformer.core.service" />
      <recent name="com.cet.eem.solution.common.service.impl" />
      <recent name="com.cet.eem.solution.common.service" />
      <recent name="com.cet.eem.fusion.maintenance.core.entity.po" />
      <recent name="com.cet.eem.fusion.maintenance.core.model" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.TransformerServiceApplication">
    <configuration name="AirCompressServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="eem-solution-air-compress-service" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.cet.eem.fusion.config.server.AirCompressServiceApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.cet.eem.fusion.config.server.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DemoServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="eem-solution-demo-service" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.cet.eem.fusion.config.server.DemoServiceApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.cet.eem.fusion.config.server.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TransformerServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="eem-solution-transformer-service" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.cet.eem.fusion.config.server.TransformerServiceApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.cet.eem.fusion.config.server.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.TransformerServiceApplication" />
        <item itemvalue="Spring Boot.AirCompressServiceApplication" />
        <item itemvalue="Spring Boot.DemoServiceApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="930225a8-cea5-4bcb-9197-cc7f9d30914d" name="Changes" comment="" />
      <created>1753513461569</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753513461569</updated>
      <workItem from="1753513463294" duration="49000" />
      <workItem from="1753513803056" duration="1824000" />
      <workItem from="1753516596935" duration="1126000" />
      <workItem from="1753517826962" duration="8481000" />
      <workItem from="1753773288969" duration="2542000" />
      <workItem from="1753847229006" duration="2299000" />
      <workItem from="1753855131549" duration="1671000" />
      <workItem from="1753864746147" duration="4851000" />
      <workItem from="1753876455534" duration="2196000" />
      <workItem from="1753922813510" duration="691000" />
      <workItem from="1753945123682" duration="17219000" />
      <workItem from="1754008392830" duration="15624000" />
      <workItem from="1754101091388" duration="8273000" />
      <workItem from="1754113058152" duration="13040000" />
      <workItem from="1754271515368" duration="9922000" />
      <workItem from="1754372140763" duration="4829000" />
      <workItem from="1754379287434" duration="12106000" />
      <workItem from="1754483703918" duration="10510000" />
      <workItem from="1754546868464" duration="24000" />
      <workItem from="1754553314779" duration="842000" />
    </task>
    <task id="LOCAL-00001" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1753513503800</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753513503800</updated>
    </task>
    <task id="LOCAL-00002" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1753519337900</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753519337900</updated>
    </task>
    <task id="LOCAL-00003" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1753519422418</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753519422418</updated>
    </task>
    <task id="LOCAL-00004" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1753521387404</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753521387404</updated>
    </task>
    <task id="LOCAL-00005" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1753773766582</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753773766582</updated>
    </task>
    <task id="LOCAL-00006" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1753773774362</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753773774362</updated>
    </task>
    <task id="LOCAL-00007" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1753773837155</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753773837155</updated>
    </task>
    <task id="LOCAL-00008" summary="修改pom文件">
      <option name="closed" value="true" />
      <created>1753864766298</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753864766298</updated>
    </task>
    <task id="LOCAL-00009" summary="修改pom文件">
      <option name="closed" value="true" />
      <created>1753867877178</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753867877178</updated>
    </task>
    <task id="LOCAL-00010" summary="修改pom文件">
      <option name="closed" value="true" />
      <created>1753868030495</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753868030495</updated>
    </task>
    <task id="LOCAL-00011" summary="Common包相关文件内容修改">
      <option name="closed" value="true" />
      <created>1753877335171</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1753877335171</updated>
    </task>
    <task id="LOCAL-00012" summary="Common包相关文件内容修改">
      <option name="closed" value="true" />
      <created>1753947871670</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1753947871670</updated>
    </task>
    <task id="LOCAL-00013" summary="Common包相关文件内容修改">
      <option name="closed" value="true" />
      <created>1753951937435</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1753951937435</updated>
    </task>
    <task id="LOCAL-00014" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1753958528684</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1753958528684</updated>
    </task>
    <task id="LOCAL-00015" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1753965120060</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1753965120060</updated>
    </task>
    <task id="LOCAL-00016" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1754026917548</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1754026917548</updated>
    </task>
    <task id="LOCAL-00017" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1754034061916</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1754034061916</updated>
    </task>
    <task id="LOCAL-00018" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1754034135038</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1754034135038</updated>
    </task>
    <task id="LOCAL-00019" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1754034153810</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1754034153810</updated>
    </task>
    <task id="LOCAL-00020" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1754034165294</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1754034165294</updated>
    </task>
    <task id="LOCAL-00021" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1754038180762</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1754038180762</updated>
    </task>
    <task id="LOCAL-00022" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1754038413943</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1754038413943</updated>
    </task>
    <task id="LOCAL-00023" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1754039398354</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1754039398354</updated>
    </task>
    <task id="LOCAL-00024" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1754101110060</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1754101110060</updated>
    </task>
    <task id="LOCAL-00025" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1754105659945</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1754105659945</updated>
    </task>
    <task id="LOCAL-00026" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1754108435465</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1754108435465</updated>
    </task>
    <task id="LOCAL-00027" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1754110195476</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1754110195476</updated>
    </task>
    <task id="LOCAL-00028" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1754110355267</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1754110355267</updated>
    </task>
    <task id="LOCAL-00029" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1754118308594</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1754118308594</updated>
    </task>
    <task id="LOCAL-00030" summary="完善提示词">
      <option name="closed" value="true" />
      <created>1754118408039</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1754118408039</updated>
    </task>
    <task id="LOCAL-00031" summary="pom文件依赖修改">
      <option name="closed" value="true" />
      <created>1754120479348</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1754120479348</updated>
    </task>
    <task id="LOCAL-00032" summary="提示词完善补充">
      <option name="closed" value="true" />
      <created>1754123725572</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1754123725572</updated>
    </task>
    <task id="LOCAL-00033" summary="提示词完善补充">
      <option name="closed" value="true" />
      <created>1754311279851</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1754311279851</updated>
    </task>
    <task id="LOCAL-00034" summary="提示词完善补充">
      <option name="closed" value="true" />
      <created>1754476906920</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1754476906920</updated>
    </task>
    <option name="localTasksCounter" value="35" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="修改pom文件" />
    <MESSAGE value="Common包相关文件内容修改" />
    <MESSAGE value="完善提示词" />
    <MESSAGE value="pom文件依赖修改" />
    <MESSAGE value="提示词完善补充" />
    <option name="LAST_COMMIT_MESSAGE" value="提示词完善补充" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>