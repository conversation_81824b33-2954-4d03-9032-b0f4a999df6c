<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="AbsoluteAlignmentInUserInterface" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AbstractClassExtendsConcreteClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AbstractClassNeverImplemented" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AbstractClassWithOnlyOneDirectInheritor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AbstractClassWithoutAbstractMethods" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AbstractMethodCallInConstructor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AbstractMethodOverridesAbstractMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AbstractMethodOverridesConcreteMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AbstractMethodWithMissingImplementations" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AccessToNonThreadSafeStaticFieldFromInstance" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="nonThreadSafeClasses">
        <value />
      </option>
      <option name="nonThreadSafeTypes" value="" />
    </inspection_tool>
    <inspection_tool class="AccessToStaticFieldLockedOnInstance" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AmbiguousFieldAccess" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AmbiguousMethodCall" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="Annotation" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AnnotationClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AnonymousClassComplexity" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="3" />
    </inspection_tool>
    <inspection_tool class="AnonymousClassMethodCount" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="1" />
    </inspection_tool>
    <inspection_tool class="AnonymousClassVariableHidesContainingMethodVariable" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AnonymousInnerClassMayBeStatic" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ArrayEquality" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ArrayLengthInLoopCondition" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AssertMessageNotString" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AssertStatement" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AssertWithoutMessage" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AssignmentOrReturnOfFieldWithMutableType" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AssignmentToForLoopParameter" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_checkForeachParameters" value="true" />
    </inspection_tool>
    <inspection_tool class="AssignmentToLambdaParameter" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AssignmentToMethodParameter" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreTransformationOfOriginalParameter" value="false" />
    </inspection_tool>
    <inspection_tool class="AssignmentToNull" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AssignmentToStaticFieldFromInstanceMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AssignmentToSuperclassField" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AutoBoxing" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreAddedToCollection" value="false" />
    </inspection_tool>
    <inspection_tool class="AutoUnboxing" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AwaitNotInLoop" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AwaitWithoutCorrespondingSignal" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="BadExceptionCaught" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="exceptionsString" value="" />
      <option name="exceptions">
        <value />
      </option>
    </inspection_tool>
    <inspection_tool class="BadExceptionDeclared" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="exceptionsString" value="" />
      <option name="exceptions">
        <value />
      </option>
      <option name="ignoreTestCases" value="false" />
      <option name="ignoreLibraryOverrides" value="false" />
    </inspection_tool>
    <inspection_tool class="BadExceptionThrown" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="exceptionsString" value="" />
      <option name="exceptions">
        <value />
      </option>
    </inspection_tool>
    <inspection_tool class="BadOddness" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="BigDecimalEquals" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="BlockMarkerComments" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="BooleanMethodNameMustStartWithQuestion" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreBooleanMethods" value="false" />
      <option name="ignoreInAnnotationInterface" value="true" />
      <option name="onlyWarnOnBaseMethods" value="true" />
      <option name="questionString" value="are,can,check,contains,could,endsWith,equals,has,is,matches,must,shall,should,startsWith,was,were,will,would" />
    </inspection_tool>
    <inspection_tool class="BooleanParameter" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="BooleanVariableAlwaysNegated" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="BoundedWildcard" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="BreakStatement" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="BreakStatementWithLabel" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="BulkFileAttributesRead" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CallToNativeMethodWhileLocked" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CallToSimpleGetterInClass" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreGetterCallsOnOtherObjects" value="false" />
      <option name="onlyReportPrivateGetter" value="false" />
    </inspection_tool>
    <inspection_tool class="CallToSimpleSetterInClass" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreSetterCallsOnOtherObjects" value="false" />
      <option name="onlyReportPrivateSetter" value="false" />
    </inspection_tool>
    <inspection_tool class="CallToStringConcatCanBeReplacedByOperator" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CallToSuspiciousStringMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CastConflictsWithInstanceof" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CastThatLosesPrecision" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreIntegerCharCasts" value="false" />
      <option name="ignoreOverflowingByteCasts" value="false" />
    </inspection_tool>
    <inspection_tool class="CastToIncompatibleInterface" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ChainedEquality" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ChainedMethodCall" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreFieldInitializations" value="true" />
      <option name="m_ignoreThisSuperCalls" value="true" />
    </inspection_tool>
    <inspection_tool class="ChannelResource" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="insideTryAllowed" value="false" />
    </inspection_tool>
    <inspection_tool class="CharUsedInArithmeticContext" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CharacterComparison" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CheckForOutOfMemoryOnLargeArrayAllocation" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="64" />
    </inspection_tool>
    <inspection_tool class="CheckedExceptionClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassComplexity" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="80" />
    </inspection_tool>
    <inspection_tool class="ClassCoupling" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_includeJavaClasses" value="false" />
      <option name="m_includeLibraryClasses" value="false" />
      <option name="m_limit" value="15" />
    </inspection_tool>
    <inspection_tool class="ClassHasNoToStringMethod" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="excludeClassNames" value="" />
      <option name="excludeException" value="true" />
      <option name="excludeDeprecated" value="true" />
      <option name="excludeEnum" value="false" />
      <option name="excludeAbstract" value="false" />
      <option name="excludeTestCode" value="false" />
      <option name="excludeInnerClasses" value="false" />
    </inspection_tool>
    <inspection_tool class="ClassIndependentOfModule" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassInheritanceDepth" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="2" />
    </inspection_tool>
    <inspection_tool class="ClassInitializer" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassLoaderInstantiation" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassNameDiffersFromFileName" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassNamePrefixedWithPackageName" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassNameSameAsAncestorName" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassNestingDepth" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="1" />
    </inspection_tool>
    <inspection_tool class="ClassNewInstance" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassOnlyUsedInOneModule" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassOnlyUsedInOnePackage" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassReferencesSubclass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassUnconnectedToPackage" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassWithMultipleLoggers" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="loggerNamesString" value="java.util.logging.Logger,org.slf4j.Logger,org.apache.commons.logging.Log,org.apache.log4j.Logger,org.apache.logging.log4j.Logger" />
    </inspection_tool>
    <inspection_tool class="ClassWithOnlyPrivateConstructors" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassWithTooManyDependencies" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="limit" value="10" />
    </inspection_tool>
    <inspection_tool class="ClassWithTooManyDependents" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="limit" value="10" />
    </inspection_tool>
    <inspection_tool class="ClassWithTooManyTransitiveDependencies" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="limit" value="35" />
    </inspection_tool>
    <inspection_tool class="ClassWithTooManyTransitiveDependents" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="limit" value="35" />
    </inspection_tool>
    <inspection_tool class="ClassWithoutConstructor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ClassWithoutLogger" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="loggerNamesString" value="java.util.logging.Logger,org.slf4j.Logger,org.apache.commons.logging.Log,org.apache.log4j.Logger,org.apache.logging.log4j.Logger" />
      <option name="ignoreSuperLoggers" value="false" />
    </inspection_tool>
    <inspection_tool class="ClassWithoutNoArgConstructor" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreClassesWithNoConstructors" value="true" />
    </inspection_tool>
    <inspection_tool class="CloneCallsConstructors" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CloneInNonCloneableClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CloneReturnsClassType" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CloneableClassInSecureContext" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CollectionsFieldAccessReplaceableByMethodCall" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CollectionsMustHaveInitialCapacity" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ComparableImplementedButEqualsNotOverridden" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ComparatorNotSerializable" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CompareToUsesNonFinalVariable" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConditionSignal" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConfusingFloatingPointLiteral" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConfusingOctalEscape" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConnectionResource" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConstantAssertArgument" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConstantAssertCondition" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConstantDeclaredInAbstractClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConstantDeclaredInInterface" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConstantMathCall" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConstantOnWrongSideOfComparison" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConstantValueVariableUse" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConstructorCount" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreDeprecatedConstructors" value="false" />
      <option name="m_limit" value="5" />
    </inspection_tool>
    <inspection_tool class="ContinueStatement" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ContinueStatementWithLabel" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConvertJavadoc" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ConvertOldAnnotations" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CovariantEquals" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CustomClassloader" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CustomSecurityManager" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CyclicClassDependency" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CyclicPackageDependency" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CyclomaticComplexity" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="10" />
    </inspection_tool>
    <inspection_tool class="DateToString" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="DeclareCollectionAsInterface" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreLocalVariables" value="false" />
      <option name="ignorePrivateMethodsAndFields" value="false" />
    </inspection_tool>
    <inspection_tool class="DesignForExtension" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="DisjointPackage" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="DollarSignInName" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="DoubleLiteralMayBeFloatLiteral" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="DriverManagerGetConnection" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="DuplicateStringLiteralInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="MIN_STRING_LENGTH" value="5" />
      <option name="IGNORE_PROPERTY_KEYS" value="false" />
    </inspection_tool>
    <inspection_tool class="DynamicRegexReplaceableByCompiledPattern" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="EmptyClass" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignorableAnnotations">
        <value />
      </option>
      <option name="ignoreClassWithParameterization" value="false" />
      <option name="ignoreThrowables" value="true" />
      <option name="commentsAreContent" value="true" />
    </inspection_tool>
    <inspection_tool class="EnumClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="EnumerationCanBeIteration" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="EqualsAndHashcode" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="EqualsCalledOnEnumConstant" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="EqualsUsesNonFinalVariable" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ErrorRethrown" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ExceptionFromCatchWhichDoesntWrap" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreGetMessage" value="false" />
      <option name="ignoreCantWrap" value="false" />
    </inspection_tool>
    <inspection_tool class="ExceptionNameDoesntEndWithException" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ExceptionPackage" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ExpectedExceptionNeverThrownTestNG" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ExtendsConcreteCollection" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ExtendsThread" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ExtendsThrowable" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ExtendsUtilityClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ExternalizableWithSerializationMethods" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="FallthruInSwitchStatement" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="FeatureEnvy" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreTestCases" value="false" />
    </inspection_tool>
    <inspection_tool class="FieldAccessNotGuarded" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="FieldAccessedSynchronizedAndUnsynchronized" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="countGettersAndSetters" value="false" />
    </inspection_tool>
    <inspection_tool class="FieldCount" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_countConstantFields" value="false" />
      <option name="m_considerStaticFinalFieldsConstant" value="false" />
      <option name="myCountEnumConstants" value="false" />
      <option name="m_limit" value="10" />
    </inspection_tool>
    <inspection_tool class="FieldHasSetterButNoGetter" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="FieldHidesSuperclassField" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreInvisibleFields" value="true" />
    </inspection_tool>
    <inspection_tool class="FieldMayBeStatic" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="FieldNamingConvention" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="FieldNotUsedInToString" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="FinalClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="FinalMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="Finalize" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreTrivialFinalizers" value="true" />
    </inspection_tool>
    <inspection_tool class="FloatingPointEquality" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ForLoopWithMissingComponent" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreCollectionLoops" value="false" />
    </inspection_tool>
    <inspection_tool class="ForeachStatement" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="Guava" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="HardCodedStringLiteral" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreForAssertStatements" value="true" />
      <option name="ignoreForExceptionConstructors" value="true" />
      <option name="ignoreForSpecifiedExceptionConstructors" value="" />
      <option name="ignoreForJUnitAsserts" value="true" />
      <option name="ignoreForClassReferences" value="true" />
      <option name="ignoreForPropertyKeyReferences" value="true" />
      <option name="ignoreForNonAlpha" value="true" />
      <option name="ignoreAssignedToConstants" value="false" />
      <option name="ignoreToString" value="false" />
      <option name="nonNlsCommentPattern" value="NON-NLS" />
    </inspection_tool>
    <inspection_tool class="HardcodedFileSeparators" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_recognizeExampleMediaType" value="false" />
    </inspection_tool>
    <inspection_tool class="HardcodedLineSeparators" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="HashCodeUsesNonFinalVariable" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="HibernateResource" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="insideTryAllowed" value="false" />
    </inspection_tool>
    <inspection_tool class="HtmlTagCanBeJavadocTag" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="IOResource" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredTypesString" value="java.io.ByteArrayOutputStream,java.io.ByteArrayInputStream,java.io.StringBufferInputStream,java.io.CharArrayWriter,java.io.CharArrayReader,java.io.StringWriter,java.io.StringReader" />
      <option name="insideTryAllowed" value="false" />
    </inspection_tool>
    <inspection_tool class="IfStatementWithTooManyBranches" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="3" />
    </inspection_tool>
    <inspection_tool class="ImplicitCallToSuper" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreForObjectSubclasses" value="false" />
    </inspection_tool>
    <inspection_tool class="ImplicitDefaultCharsetUsage" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ImplicitNumericConversion" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreWideningConversions" value="false" />
      <option name="ignoreCharConversions" value="false" />
      <option name="ignoreConstantConversions" value="false" />
    </inspection_tool>
    <inspection_tool class="InconsistentLanguageLevel" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="IncrementDecrementUsedAsExpression" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="InnerClassOnInterface" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreInnerInterfaces" value="false" />
    </inspection_tool>
    <inspection_tool class="InnerClassReferencedViaSubclass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="InnerClassVariableHidesOuterClassVariable" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreInvisibleFields" value="true" />
    </inspection_tool>
    <inspection_tool class="InstanceGuardedByStatic" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="InstanceVariableInitialization" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignorePrimitives" value="false" />
    </inspection_tool>
    <inspection_tool class="InstanceVariableUninitializedUse" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignorePrimitives" value="false" />
      <option name="annotationNamesString" value="" />
    </inspection_tool>
    <inspection_tool class="InstanceofCatchParameter" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="InstanceofChain" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreInstanceofOnLibraryClasses" value="false" />
    </inspection_tool>
    <inspection_tool class="InstanceofIncompatibleInterface" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="InstanceofThis" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="IntLiteralMayBeLongLiteral" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="InterfaceMayBeAnnotatedFunctional" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="InterfaceNeverImplemented" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreInterfacesThatOnlyDeclareConstants" value="false" />
    </inspection_tool>
    <inspection_tool class="InterfaceWithOnlyOneDirectInheritor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="IteratorNextDoesNotThrowNoSuchElementException" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="JDBCExecuteWithNonConstantString" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="JDBCPrepareStatementWithNonConstantString" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="JDBCResource" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="insideTryAllowed" value="false" />
    </inspection_tool>
    <inspection_tool class="JNDIResource" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="insideTryAllowed" value="false" />
    </inspection_tool>
    <inspection_tool class="JUnitTestNG" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="JavaLangImport" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="JavadocHtmlLint" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="KeySetIterationMayUseEntrySet" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="LabeledStatement" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="LambdaParameterHidingMemberVariable" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="LambdaParameterNamingConvention" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="LambdaUnfriendlyMethodOverload" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="LawOfDemeter" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreLibraryCalls" value="true" />
    </inspection_tool>
    <inspection_tool class="LengthOneStringInIndexOf" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ListenerMayUseAdapter" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="checkForEmptyMethods" value="true" />
    </inspection_tool>
    <inspection_tool class="LiteralAsArgToStringEquals" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="LoadLibraryWithNonConstantString" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="LocalCanBeFinal" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="REPORT_VARIABLES" value="true" />
      <option name="REPORT_PARAMETERS" value="true" />
    </inspection_tool>
    <inspection_tool class="LocalVariableHidingMemberVariable" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreInvisibleFields" value="true" />
      <option name="m_ignoreStaticMethods" value="true" />
    </inspection_tool>
    <inspection_tool class="LocalVariableNamingConvention" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreForLoopParameters" value="false" />
      <option name="m_ignoreCatchParameters" value="false" />
      <option name="m_regex" value="[a-z][A-Za-z\d]*" />
      <option name="m_minLength" value="1" />
      <option name="m_maxLength" value="20" />
    </inspection_tool>
    <inspection_tool class="LoopWithImplicitTerminationCondition" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MagicCharacter" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MagicNumber" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MapReplaceableByEnumMap" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MarkerInterface" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MethodCallInLoopCondition" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MethodCount" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="20" />
      <option name="ignoreGettersAndSetters" value="false" />
      <option name="ignoreOverridingMethods" value="false" />
    </inspection_tool>
    <inspection_tool class="MethodCoupling" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_includeJavaClasses" value="false" />
      <option name="m_includeLibraryClasses" value="false" />
      <option name="m_limit" value="10" />
    </inspection_tool>
    <inspection_tool class="MethodMayBeStatic" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_onlyPrivateOrFinal" value="false" />
      <option name="m_ignoreEmptyMethods" value="true" />
    </inspection_tool>
    <inspection_tool class="MethodMayBeSynchronized" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MethodNameSameAsParentName" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MethodOnlyUsedFromInnerClass" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreMethodsAccessedFromAnonymousClass" value="false" />
      <option name="ignoreStaticMethodsFromNonStaticInnerClass" value="false" />
      <option name="onlyReportStaticMethods" value="false" />
    </inspection_tool>
    <inspection_tool class="MethodOverloadsParentMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MethodOverridesInaccessibleMethodOfSuper" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MethodOverridesStaticMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MethodReturnAlwaysConstant" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MethodWithMultipleLoops" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MissingDeprecatedAnnotation" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MissingJavadoc" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MissingPackageInfo" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MisspelledEquals" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MisspelledMethodName" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ModuleWithTooFewClasses" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="limit" value="10" />
    </inspection_tool>
    <inspection_tool class="ModuleWithTooManyClasses" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="limit" value="100" />
    </inspection_tool>
    <inspection_tool class="MultipleReturnPointsPerMethod" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreGuardClauses" value="false" />
      <option name="ignoreEqualsMethod" value="false" />
      <option name="m_limit" value="1" />
    </inspection_tool>
    <inspection_tool class="MultipleTopLevelClassesInFile" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="MultiplyOrDivideByPowerOfTwo" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="checkDivision" value="false" />
    </inspection_tool>
    <inspection_tool class="NakedNotify" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NativeMethods" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NegatedConditional" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreNegatedNullComparison" value="true" />
    </inspection_tool>
    <inspection_tool class="NegatedConditionalExpression" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NegatedEqualityExpression" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NegatedIfElse" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreNegatedNullComparison" value="true" />
      <option name="m_ignoreNegatedZeroComparison" value="false" />
    </inspection_tool>
    <inspection_tool class="NegativelyNamedBooleanVariable" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NestedAssignment" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NestedConditionalExpression" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NestedMethodCall" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreFieldInitializations" value="true" />
    </inspection_tool>
    <inspection_tool class="NestedSwitchStatement" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NestedSynchronizedStatement" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NestedTryStatement" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NestingDepth" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="5" />
    </inspection_tool>
    <inspection_tool class="NewExceptionWithoutArguments" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NewMethodNamingConvention" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonBooleanMethodNameMayNotStartWithQuestion" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="questionString" value="are,can,check,contains,could,endsWith,equals,has,is,matches,must,shall,should,startsWith,was,were,will,would" />
      <option name="ignoreBooleanMethods" value="false" />
      <option name="onlyWarnOnBaseMethods" value="true" />
    </inspection_tool>
    <inspection_tool class="NonCommentSourceStatements" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="30" />
    </inspection_tool>
    <inspection_tool class="NonExceptionNameEndsWithException" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonFinalClone" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonFinalFieldInImmutable" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonFinalFieldOfException" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonFinalGuard" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonFinalStaticVariableUsedInClassInitialization" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonFinalUtilityClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonProtectedConstructorInAbstractClass" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreNonPublicClasses" value="false" />
    </inspection_tool>
    <inspection_tool class="NonPublicClone" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonReproducibleMathCall" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonSerializableFieldInSerializableClass" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignorableAnnotations">
        <value />
      </option>
      <option name="ignoreAnonymousInnerClasses" value="false" />
      <option name="superClassString" value="java.awt.Component" />
    </inspection_tool>
    <inspection_tool class="NonSerializableObjectBoundToHttpSession" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonSerializableObjectPassedToObjectStream" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonSerializableWithSerializationMethods" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonShortCircuitBoolean" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonStaticFinalLogger" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="loggerClassName" value="java.util.logging.Logger,org.slf4j.Logger,org.apache.commons.logging.Log,org.apache.log4j.Logger,org.apache.logging.log4j.Logger" />
    </inspection_tool>
    <inspection_tool class="NonSynchronizedMethodOverridesSynchronizedMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonThreadSafeLazyInitialization" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NoopMethodInAbstractClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NotifyCalledOnCondition" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NotifyWithoutCorrespondingWait" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NullThrown" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NumericToString" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ObjectAllocationInLoop" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ObjectInstantiationInEqualsHashCode" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ObjectNotify" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ObjectToString" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ObsoleteCollection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreRequiredObsoleteCollectionTypes" value="true" />
    </inspection_tool>
    <inspection_tool class="OctalAndDecimalIntegersMixed" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="OnDemandImport" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="OptionalContainsCollection" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="OverloadedMethodsWithSameNumberOfParameters" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreInconvertibleTypes" value="true" />
    </inspection_tool>
    <inspection_tool class="OverloadedVarargsMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="OverlyComplexArithmeticExpression" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="6" />
    </inspection_tool>
    <inspection_tool class="OverlyComplexBooleanExpression" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="3" />
      <option name="m_ignorePureConjunctionsDisjunctions" value="true" />
    </inspection_tool>
    <inspection_tool class="OverlyLargePrimitiveArrayInitializer" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="64" />
    </inspection_tool>
    <inspection_tool class="OverlyLongLambda" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="OverlyStrongTypeCast" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreInMatchingInstanceof" value="false" />
    </inspection_tool>
    <inspection_tool class="OverridableMethodCallDuringObjectConstruction" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="OverriddenMethodCallDuringObjectConstruction" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PackageDotHtmlMayBePackageInfo" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PackageInMultipleModules" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PackageNamingConvention" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_regex" value="[a-z]*" />
      <option name="m_minLength" value="3" />
      <option name="m_maxLength" value="16" />
    </inspection_tool>
    <inspection_tool class="PackageVisibleField" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PackageVisibleInnerClass" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreEnums" value="false" />
      <option name="ignoreInterfaces" value="false" />
    </inspection_tool>
    <inspection_tool class="PackageWithTooFewClasses" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="limit" value="3" />
    </inspection_tool>
    <inspection_tool class="PackageWithTooManyClasses" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="limit" value="50" />
    </inspection_tool>
    <inspection_tool class="ParameterHidingMemberVariable" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreInvisibleFields" value="true" />
      <option name="m_ignoreStaticMethodParametersHidingInstanceFields" value="true" />
      <option name="m_ignoreForConstructors" value="false" />
      <option name="m_ignoreForPropertySetters" value="false" />
      <option name="m_ignoreForAbstractMethods" value="false" />
    </inspection_tool>
    <inspection_tool class="ParameterNameDiffersFromOverriddenParameter" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreSingleCharacterNames" value="false" />
      <option name="m_ignoreOverridesOfLibraryMethods" value="false" />
    </inspection_tool>
    <inspection_tool class="ParameterNamingConvention" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_regex" value="[a-z][A-Za-z\d]*" />
      <option name="m_minLength" value="1" />
      <option name="m_maxLength" value="20" />
    </inspection_tool>
    <inspection_tool class="ParameterTypePreventsOverriding" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ParametersPerConstructor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ParametersPerMethod" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="5" />
    </inspection_tool>
    <inspection_tool class="PointlessIndexOfComparison" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PrivateMemberAccessBetweenOuterAndInnerClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ProblematicVarargsMethodOverride" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PropertyValueSetToItself" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ProtectedField" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ProtectedInnerClass" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreEnums" value="false" />
      <option name="ignoreInterfaces" value="false" />
    </inspection_tool>
    <inspection_tool class="PublicConstructor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PublicConstructorInNonPublicClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PublicFieldAccessedInSynchronizedContext" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PublicInnerClass" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreEnums" value="false" />
      <option name="ignoreInterfaces" value="false" />
    </inspection_tool>
    <inspection_tool class="PublicMethodNotExposedInInterface" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignorableAnnotations">
        <value />
      </option>
      <option name="onlyWarnIfContainingClassImplementsAnInterface" value="false" />
    </inspection_tool>
    <inspection_tool class="PublicMethodWithoutLogging" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="loggerClassName" value="java.util.logging.Logger,org.slf4j.Logger,org.apache.commons.logging.Log,org.apache.log4j.Logger,org.apache.logging.log4j.Logger" />
    </inspection_tool>
    <inspection_tool class="PublicStaticArrayField" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PublicStaticCollectionField" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="QuestionableName" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="nameString" value="aa,abc,bad,bar,bar2,baz,baz1,baz2,baz3,bb,blah,bogus,bool,cc,dd,defau1t,dummy,dummy2,ee,fa1se,ff,foo,foo1,foo2,foo3,foobar,four,fred,fred1,fred2,gg,hh,hello,hello1,hello2,hello3,ii,nu11,one,silly,silly2,string,two,that,then,three,whi1e,var" />
    </inspection_tool>
    <inspection_tool class="RandomDoubleForRandomInteger" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ReadObjectAndWriteObjectPrivate" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ReadObjectInitialization" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ReadResolveAndWriteReplaceProtected" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="RecordStoreResource" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="RedundantFieldInitialization" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="RedundantImplements" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreSerializable" value="false" />
      <option name="ignoreCloneable" value="false" />
    </inspection_tool>
    <inspection_tool class="RedundantModifiersUtilityClassLombok" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ResultOfObjectAllocationIgnored" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ResultSetIndexZero" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ReturnNull" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_reportObjectMethods" value="true" />
      <option name="m_reportArrayMethods" value="true" />
      <option name="m_reportCollectionMethods" value="true" />
      <option name="m_ignorePrivateMethods" value="false" />
    </inspection_tool>
    <inspection_tool class="ReturnOfInnerClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ReturnThis" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="RuntimeExec" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="RuntimeExecWithNonConstantString" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SafeLock" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SamePackageImport" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SerialPersistentFieldsWithWrongSignature" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SerializableDeserializableClassInSecureContext" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SerializableHasSerializationMethods" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreAnonymousInnerClasses" value="false" />
      <option name="superClassString" value="java.io.Externalizable,java.awt.Component" />
    </inspection_tool>
    <inspection_tool class="SerializableInnerClassHasSerialVersionUIDField" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreAnonymousInnerClasses" value="false" />
      <option name="superClassString" value="java.awt.Component" />
    </inspection_tool>
    <inspection_tool class="SerializableInnerClassWithNonSerializableOuterClass" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreAnonymousInnerClasses" value="false" />
      <option name="superClassString" value="java.awt.Component" />
    </inspection_tool>
    <inspection_tool class="SerializableStoresNonSerializable" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SerializableWithUnconstructableAncestor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SetReplaceableByEnumSet" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SharedThreadLocalRandom" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SignalWithoutCorrespondingAwait" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SimpleDateFormatWithoutLocale" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SimplifiableAnnotation" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SimplifiableEqualsExpression" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SingleCharacterStartsWith" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SingleClassImport" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="Singleton" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SleepWhileHoldingLock" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SocketResource" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="insideTryAllowed" value="false" />
    </inspection_tool>
    <inspection_tool class="StandardVariableNames" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StaticCallOnSubclass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StaticCollection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreWeakCollections" value="false" />
    </inspection_tool>
    <inspection_tool class="StaticFieldReferenceOnSubclass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StaticGuardedByInstance" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StaticImport" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StaticInheritance" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StaticMethodOnlyUsedInOneClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StaticNonFinalField" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StaticPseudoFunctionalStyleMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StaticVariableInitialization" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignorePrimitives" value="false" />
    </inspection_tool>
    <inspection_tool class="StaticVariableUninitializedUse" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignorePrimitives" value="false" />
    </inspection_tool>
    <inspection_tool class="StringBufferField" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StringBufferMustHaveInitialCapacity" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StringConcatenation" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreAsserts" value="false" />
      <option name="ignoreSystemOuts" value="false" />
      <option name="ignoreSystemErrs" value="false" />
      <option name="ignoreThrowableArguments" value="false" />
      <option name="ignoreConstantInitializers" value="false" />
      <option name="ignoreInTestCode" value="false" />
      <option name="ignoreInToString" value="false" />
    </inspection_tool>
    <inspection_tool class="StringConcatenationInFormatCall" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StringConcatenationInMessageFormatCall" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StringConcatenationMissingWhitespace" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StringReplaceableByStringBuffer" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="onlyWarnOnLoop" value="true" />
    </inspection_tool>
    <inspection_tool class="StringToUpperWithoutLocale" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="StringTokenizer" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SubtractionInCompareTo" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SuspiciousArrayCast" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SuspiciousGetterSetter" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SuspiciousLiteralUnderscore" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SwitchStatement" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SwitchStatementDensity" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="20" />
    </inspection_tool>
    <inspection_tool class="SwitchStatementWithTooManyBranches" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="10" />
    </inspection_tool>
    <inspection_tool class="SynchronizationOnStaticField" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SynchronizeOnLock" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SynchronizeOnThis" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SynchronizedMethod" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_includeNativeMethods" value="true" />
      <option name="ignoreSynchronizedSuperMethods" value="true" />
    </inspection_tool>
    <inspection_tool class="SynchronizedOnLiteralObject" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SystemExit" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SystemGC" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SystemGetenv" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SystemProperties" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="SystemSetSecurityManager" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ThisEscapedInConstructor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ThreadDeathRethrown" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ThreadDumpStack" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ThreadLocalNotStaticFinal" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ThreadPriority" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ThreadStartInConstruction" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ThreadStopSuspendResume" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ThreadYield" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ThreeNegationsPerMethod" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreInEquals" value="true" />
      <option name="ignoreInAssert" value="false" />
    </inspection_tool>
    <inspection_tool class="ThrowCaughtLocally" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreRethrownExceptions" value="false" />
    </inspection_tool>
    <inspection_tool class="ThrownExceptionsPerMethod" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_limit" value="3" />
    </inspection_tool>
    <inspection_tool class="ThrowsRuntimeException" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="TimeToString" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="TooBroadCatch" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="TooBroadThrows" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="TransientFieldInNonSerializableClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="TransientFieldNotInitialized" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="TypeMayBeWeakened" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="useRighthandTypeAsWeakestTypeInAssignments" value="true" />
      <option name="useParameterizedTypeForCollectionMethods" value="true" />
      <option name="doNotWeakenToJavaLangObject" value="true" />
      <option name="onlyWeakentoInterface" value="true" />
    </inspection_tool>
    <inspection_tool class="TypeParameterExtendsFinalClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UncheckedExceptionClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnconditionalWait" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UndeclaredTests" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnknownGuard" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnnecessarilyQualifiedStaticUsage" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreStaticFieldAccesses" value="false" />
      <option name="m_ignoreStaticMethodCalls" value="false" />
      <option name="m_ignoreStaticAccessFromStaticContext" value="false" />
    </inspection_tool>
    <inspection_tool class="UnnecessarilyQualifiedStaticallyImportedElement" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryConstantArrayCreationExpression" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryConstructor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryExplicitNumericCast" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryFinalOnLocalVariableOrParameter" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryInheritDoc" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryJavaDocLink" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreInlineLinkToSuper" value="false" />
    </inspection_tool>
    <inspection_tool class="UnnecessaryQualifierForThis" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnnecessarySuperConstructor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnnecessarySuperQualifier" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryThis" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnqualifiedFieldAccess" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnqualifiedInnerClassAccess" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreReferencesToLocalInnerClasses" value="true" />
    </inspection_tool>
    <inspection_tool class="UnqualifiedMethodAccess" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnqualifiedStaticUsage" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="m_ignoreStaticFieldAccesses" value="false" />
      <option name="m_ignoreStaticMethodCalls" value="false" />
      <option name="m_ignoreStaticAccessFromStaticContext" value="false" />
    </inspection_tool>
    <inspection_tool class="UnreachableCode" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnsecureRandomNumberGeneration" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UnusedLibrary" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UpperCaseFieldNameNotConstant" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UseOfAWTPeerClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UseOfAnotherObjectsPrivateField" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoreSameClass" value="false" />
      <option name="ignoreEquals" value="false" />
    </inspection_tool>
    <inspection_tool class="UseOfClone" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UseOfConcreteClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UseOfJDBCDriverClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UseOfObsoleteDateTimeApi" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UseOfProcessBuilder" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UseOfPropertiesAsHashtable" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UseOfSunClasses" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UtilityClass" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignorableAnnotations">
        <value />
      </option>
    </inspection_tool>
    <inspection_tool class="UtilityClassCanBeEnum" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UtilityClassWithPublicConstructor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="UtilityClassWithoutPrivateConstructor" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignorableAnnotations">
        <value />
      </option>
      <option name="ignoreClassesWithOnlyMain" value="false" />
    </inspection_tool>
    <inspection_tool class="VariableNotUsedInsideIf" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="VolatileArrayField" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="WaitCalledOnCondition" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="WaitNotInLoop" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="WaitNotifyNotInSynchronizedContext" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="WaitOrAwaitWithoutTimeout" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="WaitWithoutCorrespondingNotify" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="WeakerAccess" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="SUGGEST_PACKAGE_LOCAL_FOR_MEMBERS" value="true" />
      <option name="SUGGEST_PACKAGE_LOCAL_FOR_TOP_CLASSES" value="true" />
      <option name="SUGGEST_PRIVATE_FOR_INNERS" value="false" />
    </inspection_tool>
    <inspection_tool class="ZeroLengthArrayInitialization" enabled="true" level="WARNING" enabled_by_default="true" />
  </profile>
</component>