# IntelliJ Platform SDK 安装脚本
param(
    [string]$IntellijPath = "",
    [switch]$Help
)

if ($Help) {
    Write-Host "IntelliJ Platform SDK 安装脚本" -ForegroundColor Green
    Write-Host ""
    Write-Host "用法:"
    Write-Host "  .\setup-intellij-sdk.ps1 -IntellijPath <IntelliJ IDEA安装路径>"
    Write-Host ""
    Write-Host "示例:"
    Write-Host "  .\setup-intellij-sdk.ps1 -IntellijPath 'C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2022.3.3'"
    Write-Host ""
    Write-Host "如果不指定路径，脚本会尝试自动查找IntelliJ IDEA安装目录"
    exit 0
}

Write-Host "========================================" -ForegroundColor Green
Write-Host "IntelliJ Platform SDK 安装脚本" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 自动查找IntelliJ IDEA安装路径
if ([string]::IsNullOrEmpty($IntellijPath)) {
    Write-Host "正在查找IntelliJ IDEA安装路径..." -ForegroundColor Yellow
    
    $possiblePaths = @(
        "C:\Program Files\JetBrains\IntelliJ IDEA Community Edition*",
        "C:\Program Files (x86)\JetBrains\IntelliJ IDEA Community Edition*",
        "$env:LOCALAPPDATA\JetBrains\IntelliJ IDEA Community Edition*",
        "C:\Users\<USER>\AppData\Local\JetBrains\IntelliJ IDEA Community Edition*"
    )
    
    foreach ($path in $possiblePaths) {
        $found = Get-ChildItem -Path $path -ErrorAction SilentlyContinue | Sort-Object Name -Descending | Select-Object -First 1
        if ($found) {
            $IntellijPath = $found.FullName
            Write-Host "找到IntelliJ IDEA: $IntellijPath" -ForegroundColor Green
            break
        }
    }
    
    if ([string]::IsNullOrEmpty($IntellijPath)) {
        Write-Host "错误: 未找到IntelliJ IDEA安装目录" -ForegroundColor Red
        Write-Host "请手动指定路径: .\setup-intellij-sdk.ps1 -IntellijPath '<路径>'" -ForegroundColor Yellow
        exit 1
    }
}

# 验证路径
if (-not (Test-Path $IntellijPath)) {
    Write-Host "错误: 指定的路径不存在: $IntellijPath" -ForegroundColor Red
    exit 1
}

Write-Host "使用IntelliJ IDEA路径: $IntellijPath" -ForegroundColor Green

# 查找必要的JAR文件
$libPath = Join-Path $IntellijPath "lib"
if (-not (Test-Path $libPath)) {
    Write-Host "错误: 未找到lib目录: $libPath" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "正在查找必要的JAR文件..." -ForegroundColor Yellow

$requiredJars = @(
    "idea.jar",
    "openapi.jar",
    "util.jar",
    "extensions.jar",
    "annotations.jar",
    "platform-api.jar",
    "platform-impl.jar",
    "analysis-api.jar",
    "analysis-impl.jar",
    "java-api.jar",
    "java-impl.jar"
)

$foundJars = @()
$missingJars = @()

foreach ($jar in $requiredJars) {
    $jarPath = Join-Path $libPath $jar
    if (Test-Path $jarPath) {
        $foundJars += $jarPath
        Write-Host "✓ 找到: $jar" -ForegroundColor Green
    } else {
        $missingJars += $jar
        Write-Host "✗ 缺失: $jar" -ForegroundColor Red
    }
}

# 查找所有JAR文件作为备选
$allJars = Get-ChildItem -Path $libPath -Filter "*.jar" | Sort-Object Name

Write-Host ""
Write-Host "lib目录中的所有JAR文件:" -ForegroundColor Yellow
foreach ($jar in $allJars) {
    Write-Host "  - $($jar.Name)" -ForegroundColor White
}

# 更新pom.xml文件
Write-Host ""
Write-Host "正在更新pom.xml文件..." -ForegroundColor Yellow

$pomPath = "pom.xml"
if (-not (Test-Path $pomPath)) {
    Write-Host "错误: 未找到pom.xml文件" -ForegroundColor Red
    exit 1
}

# 读取pom.xml内容
$pomContent = Get-Content $pomPath -Raw

# 生成依赖配置
$dependenciesXml = @"

        <!-- IntelliJ Platform SDK 依赖 -->
        <!-- 基于本地IntelliJ IDEA安装: $IntellijPath -->
"@

# 添加找到的JAR文件作为系统依赖
foreach ($jarPath in $foundJars) {
    $jarName = [System.IO.Path]::GetFileNameWithoutExtension($jarPath)
    $dependenciesXml += @"

        <dependency>
            <groupId>com.jetbrains.intellij</groupId>
            <artifactId>$jarName</artifactId>
            <version>local</version>
            <scope>system</scope>
            <systemPath>$jarPath</systemPath>
        </dependency>
"@
}

# 添加主要的JAR文件
$mainJars = @("idea.jar", "openapi.jar", "util.jar", "extensions.jar", "annotations.jar")
foreach ($mainJar in $mainJars) {
    $jarPath = Join-Path $libPath $mainJar
    if (Test-Path $jarPath) {
        $jarName = [System.IO.Path]::GetFileNameWithoutExtension($jarPath)
        if ($dependenciesXml -notlike "*$jarName*") {
            $dependenciesXml += @"

        <dependency>
            <groupId>com.jetbrains.intellij</groupId>
            <artifactId>$jarName</artifactId>
            <version>local</version>
            <scope>system</scope>
            <systemPath>$jarPath</systemPath>
        </dependency>
"@
        }
    }
}

# 在现有依赖之前插入新的依赖
$insertPoint = $pomContent.IndexOf("        <!-- Logging -->")
if ($insertPoint -gt 0) {
    $newPomContent = $pomContent.Substring(0, $insertPoint) + $dependenciesXml + "`n`n        " + $pomContent.Substring($insertPoint)
    
    # 备份原文件
    Copy-Item $pomPath "$pomPath.backup"
    
    # 写入新内容
    $newPomContent | Out-File -FilePath $pomPath -Encoding UTF8
    
    Write-Host "✓ pom.xml文件已更新" -ForegroundColor Green
    Write-Host "✓ 原文件已备份为 pom.xml.backup" -ForegroundColor Green
} else {
    Write-Host "警告: 无法找到插入点，请手动添加依赖" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "安装完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "下一步:" -ForegroundColor Yellow
Write-Host "1. 运行 .\build.ps1 构建项目" -ForegroundColor White
Write-Host "2. 运行 .\test.ps1 测试项目" -ForegroundColor White
Write-Host ""
Write-Host "注意事项:" -ForegroundColor Yellow
Write-Host "- 如果构建失败，可能需要调整JAR文件路径" -ForegroundColor White
Write-Host "- 确保使用的IntelliJ IDEA版本与项目兼容" -ForegroundColor White
Write-Host "- 如果遇到问题，请检查IntelliJ IDEA安装是否完整" -ForegroundColor White
