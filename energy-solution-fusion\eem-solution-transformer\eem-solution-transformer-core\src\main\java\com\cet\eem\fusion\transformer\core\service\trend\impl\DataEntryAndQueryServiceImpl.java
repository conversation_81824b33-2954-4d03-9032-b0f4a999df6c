package com.cet.eem.fusion.transformer.core.service.trend.impl;

import com.cet.eem.bll.common.dao.node.EnergySupplyDao;
import com.cet.eem.bll.common.dao.pipenetwork.PipeNetworkConnectionModelDao;
import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;
import com.cet.eem.bll.common.model.domain.subject.energysaving.ColdActual;
import com.cet.eem.bll.common.model.domain.subject.energysaving.ColdPredict;
import com.cet.eem.bll.common.model.domain.subject.energysaving.WeatherPredict;
import com.cet.eem.bll.common.model.node.relations.EnergySupplyToPo;
import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.bll.energysaving.dao.aioptimization.AiStartStopStrategyDao;
import com.cet.eem.bll.energysaving.dao.aioptimization.MesShiftDao;
import com.cet.eem.bll.energysaving.dao.aioptimization.ProductionDataDockingDao;
import com.cet.eem.bll.energysaving.dao.aioptimization.StrategyObjectMapDao;
import com.cet.eem.bll.energysaving.dao.colddata.ColdActualDao;
import com.cet.eem.bll.energysaving.dao.weather.ColdPredictDao;
import com.cet.eem.bll.energysaving.dao.weather.DeviceChainDao;
import com.cet.eem.bll.energysaving.dao.weather.RefrigeratingSystemDao;
import com.cet.eem.bll.energysaving.dao.weather.WeatherPredictDao;
import com.cet.eem.bll.energysaving.model.aioptimization.AiStartStopStrategy;
import com.cet.eem.bll.energysaving.model.aioptimization.StrategyObjectMap;
import com.cet.eem.bll.energysaving.model.aioptimization.StrategyTypeDef;
import com.cet.eem.bll.energysaving.model.config.MesShift;
import com.cet.eem.bll.energysaving.model.config.PumpFunctionType;
import com.cet.eem.bll.energysaving.model.config.system.RefrigeratingSystem;
import com.cet.eem.bll.energysaving.model.dataentryquery.*;
import com.cet.eem.bll.energysaving.model.def.QuantityDef;
import com.cet.eem.bll.energysaving.model.weather.*;
import com.cet.eem.bll.energysaving.service.trend.DataEntryAndQueryService;
import com.cet.eem.bll.energysaving.service.trend.OperationTrendService;
import com.cet.eem.bll.energysaving.service.weather.WeatherCrawlingDataService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.EnergyTypeDef;
import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.common.constant.ExcelType;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.file.FileUtils;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.ResultWithTotal;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.utils.PoiExcelUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.node.service.Topology1Service;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.eem.weather.dao.WeatherDao;
import com.cet.eem.weather.model.Weather;
import com.cet.eem.weather.vo.QueryWeatherParam;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import com.ibm.icu.text.DecimalFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : DataEntryAndQueryServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-04-08 14:20
 */
@Service
@Slf4j
public class DataEntryAndQueryServiceImpl implements DataEntryAndQueryService {
    @Autowired
    WeatherPredictDao weatherPredictDao;
    @Autowired
    RefrigeratingSystemDao refrigeratingSystemDao;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    Topology1Service topology1Service;
    @Autowired
    PipeNetworkConnectionModelDao connectionModelDao;
    @Autowired
    QuantityManageService quantityManageService;
    @Autowired
    OperationTrendService operationTrendService;
    @Autowired
    ColdActualDao coldActualDao;
    @Autowired
    ColdPredictDao coldPredictDao;
    @Autowired
    AiStartStopStrategyDao aiStartStopStrategyDao;
    @Autowired
    StrategyObjectMapDao strategyObjectMapDao;
    @Autowired
    DeviceChainDao deviceChainDao;
    @Autowired
    EnergySupplyDao energySupplyDao;
    @Autowired
    WeatherDao weatherDao;
    @Autowired
    WeatherCrawlingDataService weatherCrawlingDataService;
    @Autowired
    ProductionDataDockingDao productionDataDockingDao;
    @Autowired
    MesShiftDao mesShiftDao;
    //gj转kw
    public static final Double UNIT = 0.0036D;

    @Override
    public List<EndColdDataEntry> queryEndColdData(DataQueryParam queryParam, Long projectId) throws IllegalAccessException, InstantiationException {
        //处理数据时间数据
        handleQueryParamTime(queryParam);
        List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.querySystem(projectId);
        //查询房间下的冷水主机
        List<Long> roomIds = refrigeratingSystems.stream().map(RefrigeratingSystem::getRoomId).collect(Collectors.toList());
        List<Map<String, Object>> roomWithChildren = modelServiceUtils.queryWithChildren(NodeLabelDef.ROOM, roomIds, Collections.singletonList(NodeLabelDef.COLD_WATER_MAINENGINE));
        //房间和冷机节点
        List<BaseVo> baseVos = JsonTransferUtils.transferList(roomWithChildren, BaseVo.class);
        List<BaseVo> coldDevices = new ArrayList<>();
        //获得冷机
        getEndPipeLine(baseVos, coldDevices);

        //查询冷水主机下关联的冷冻水管
        List<PipeNetworkConnectionModel> connectionModels = connectionModelDao.
                getTopOrDownBatchWithNodes(coldDevices, false, projectId);
        // 查询制冷房间关联的管道
        List<BaseVo> pipeLineList = topology1Service.queryFlowNode(projectId, EnergyTypeDef.COLD, coldDevices, NodeLabelDef.PIPELINE);
        List<BaseVo> endPipeline = new ArrayList<>();
        getEndPipeLine(pipeLineList, endPipeline);
        //处理系统和末端管道的关系
        Map<Long, List<BaseVo>> longListMap = handleDataWithSystem(baseVos, refrigeratingSystems, connectionModels, pipeLineList);
        //空调
        List<BaseVo> airs = weatherCrawlingDataService.queryNodesByMonitor(projectId);
        endPipeline = endPipeline.stream().distinct().collect(Collectors.toList());
        List<BaseVo> baseVos1 = new ArrayList<>(endPipeline);
        baseVos1.addAll(airs);
        Map<Integer, List<TrendDataVo>> dataLogResult = quantityManageService.queryDataLogBatch(
                operationTrendService.createQuantityDataSearchVo(baseVos1, new QueryParam(queryParam.getStartTime(),
                        queryParam.getEndTime(), AggregationCycle.FIVE_MINUTES), Arrays.asList(QuantityDef.getFreezingWaterPipelineForStream(),
                        QuantityDef.getAiPredictForHumQuantitySetting(), QuantityDef.getAiPredictForTempQuantitySetting())));
        // 取出设备数据
        List<TrendDataVo> dataVoList = dataLogResult.get(QuantityDef.getFreezingWaterPipelineForStream().getId());
        if (Objects.isNull(dataVoList)) {
            dataVoList = new ArrayList<>();
        }
        Map<BaseVo, List<TrendDataVo>> map = dataVoList.stream().
                collect(Collectors.groupingBy(trendDataVo -> new BaseVo(trendDataVo.getMonitoredid(), trendDataVo.getMonitoredlabel())));
        Map<BaseVo, List<DatalogValue>> dataLogMap = new HashMap<>();
        for (Map.Entry<BaseVo, List<TrendDataVo>> entryMap : map.entrySet()) {
            List<TrendDataVo> value = entryMap.getValue();
            //过滤出属于某个设备的采集设备的数据，再取第一个有值的，然后把这个类型的值全部加起来
            List<DatalogValue> dataList = operationTrendService.getNonValueDataLog(value);
            dataLogMap.put(entryMap.getKey(), assembleData(dataList, queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle()));
        }
        List<WeatherPredict> weatherPredicts = weatherPredictDao.queryWeather(queryParam.getStartTimePredict(), queryParam.getEndTimePredict());
        Map<BaseVo, List<MesData>> mesData = assembleMesData(endPipeline, queryParam);
        return assembleEndColdDataEntry(longListMap, dataLogMap, weatherPredicts, projectId, dataLogResult, queryParam, mesData);
    }

    private Map<BaseVo, List<MesData>> assembleMesData(List<BaseVo> endPipeline, DataQueryParam queryParam) {
        //获得当前管道的关联的车间
        List<EnergySupplyToPo> energySupplyToPos = energySupplyDao.queryEnergySupplyByObjectNodes(endPipeline, EnergySupplyToPo.class);
        List<BaseVo> baseVos = energySupplyToPos.stream().map(energySupplyToPo -> new BaseVo(energySupplyToPo.getSupplytoid(), energySupplyToPo.getSupplytolabel()))
                .distinct().collect(Collectors.toList());
        Map<BaseVo, List<EnergySupplyToPo>> map = energySupplyToPos.stream().collect(Collectors.groupingBy(energySupplyToPo -> new BaseVo(energySupplyToPo.getSupplytoid(), energySupplyToPo.getSupplytolabel())));
        List<ProductionDataDocking> productionDataDockings = productionDataDockingDao.queryByRoomId(baseVos, TimeUtil.localDateTime2timestamp(queryParam.getStartTime()), TimeUtil.localDateTime2timestamp(queryParam.getEndTime()));
        List<MesShift> mesShifts = mesShiftDao.queryMesShift(baseVos);
        if (CollectionUtils.isEmpty(productionDataDockings) || CollectionUtils.isEmpty(mesShifts)) {
            return Collections.emptyMap();
        }
        List<Long> timeRange = TimeUtil.getTimeRange(TimeUtil.localDateTime2timestamp(queryParam.getStartTime()), TimeUtil.localDateTime2timestamp(queryParam.getEndTime()), queryParam.getCycle());
        //拼接车间和mes信息的关系
        Map<BaseVo, List<MesData>> baseVoListMap = assembleMesTimeValue(timeRange, productionDataDockings, mesShifts);
        //返回管道和mes信息的关系
        Map<BaseVo, List<MesData>> result = new HashMap<>();
        for (Map.Entry<BaseVo, List<MesData>> entry : baseVoListMap.entrySet()) {
            List<EnergySupplyToPo> energySupplyToPos1 = map.get(entry.getKey());
            List<BaseVo> pipelines = energySupplyToPos1.stream().map(energySupplyToPo -> new BaseVo(energySupplyToPo.getObjectid(), energySupplyToPo.getObjectlabel()))
                    .distinct().collect(Collectors.toList());
            for (BaseVo baseVo : pipelines) {
                result.put(baseVo, entry.getValue());
            }
        }
        return result;
    }

    private Map<BaseVo, List<MesData>> assembleMesTimeValue(List<Long> timeRange, List<ProductionDataDocking> productionDataDockings, List<MesShift> mesShifts) {
        Map<BaseVo, List<MesData>> result = new HashMap<>();
        Map<BaseVo, List<ProductionDataDocking>> map = productionDataDockings.stream().collect
                (Collectors.groupingBy(productionDataDocking -> new BaseVo(productionDataDocking.getObjectId(), productionDataDocking.getObjectLabel())));
        for (Map.Entry<BaseVo, List<ProductionDataDocking>> entry : map.entrySet()) {
            List<ProductionDataDocking> value = entry.getValue();
            List<MesData> mesDataList = new ArrayList<>();
            List<ProductionDataDocking> sort = value.stream().sorted(Comparator.comparing(ProductionDataDocking::getLogTime).reversed()).collect(Collectors.toList());
            List<MesShift> shifts = mesShifts.stream().filter(mesShift -> Objects.equals(entry.getKey().getId(), mesShift.getObjectId()) &&
                    Objects.equals(entry.getKey().getModelLabel(), mesShift.getObjectLabel())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(shifts)) {
                continue;
            }
            for (Long time : timeRange) {
                ProductionDataDocking closeData = getCloseData(sort, time);
                MesData mesData = assembleMesData(closeData, shifts, time);
                mesDataList.add(mesData);
            }
            result.put(entry.getKey(), mesDataList);
        }
        return result;
    }

    private List<MesData> assembleEmpty(DataQueryParam queryParam) {
        List<Long> timeRange = TimeUtil.getTimeRange(TimeUtil.localDateTime2timestamp(queryParam.getStartTime()), TimeUtil.localDateTime2timestamp(queryParam.getEndTime()), queryParam.getCycle());
        List<MesData> result = new ArrayList<>();
        for (Long time : timeRange) {
            MesData mesData = new MesData();
            mesData.setLogTime(time);
            result.add(mesData);
        }
        return result;
    }

    private MesData assembleMesData(ProductionDataDocking closeData, List<MesShift> shifts, Long time) {
        String shiftName = closeData.getShiftName();
        List<MesShift> mesShifts = shifts.stream().filter(mesShift -> Objects.equals(shiftName, mesShift.getShiftType())).sorted(Comparator.comparing(MesShift::getLogTime).reversed())
                .collect(Collectors.toList());
        MesShift shift;
        MesData mesData = new MesData();
        mesData.setLogTime(time);
        if (CollectionUtils.isEmpty(mesShifts)) {
            return mesData;
        } else {
            shift = mesShifts.get(0);
        }
        mesData.setHour(CommonUtils.calcLong(shift.getDuration(), TimeUtil.HOUR, EnumOperationType.DIVISION.getId()).doubleValue());
        mesData.setRhythm(closeData.getTaktTime());
        Long aLong = calculateStartTime(shift.getStartTime(), closeData.getLogTime());
        mesData.setShiftStartTime(aLong);
        mesData.setShiftEndTime(aLong + shift.getDuration());
        mesData.setProductionPlan(closeData.getPlanNum() - closeData.getCompleteNum());
        return mesData;
    }

    private Long calculateStartTime(Long st, Long time) {
        if (Objects.isNull(st) || Objects.isNull(time)) {
            return 0L;
        }
        LocalDateTime localDateTime = TimeUtil.timestamp2LocalDateTime(st);
        LocalDateTime today = TimeUtil.getFirstTimeOfDay(TimeUtil.timestamp2LocalDateTime(time));
        LocalDateTime time1 = today.plusHours(localDateTime.getHour());
        LocalDateTime time2 = time1.plusMinutes(localDateTime.getMinute());
        return TimeUtil.localDateTime2timestamp(time2);
    }

    private ProductionDataDocking getCloseData(List<ProductionDataDocking> sort, Long time) {

        for (int i = 0; i < sort.size(); i++) {
            ProductionDataDocking productionDataDocking = sort.get(i);
            Long logTime = productionDataDocking.getLogTime();
            if (Objects.equals(logTime, time)) {
                return productionDataDocking;
            }
            if (logTime < time && i != 0) {
                ProductionDataDocking productionDataDocking1 = sort.get(i - 1);
                Long logTime1 = productionDataDocking1.getLogTime();
                if (logTime - time > logTime1 - time) {
                    return productionDataDocking1;
                } else {
                    return productionDataDocking;
                }
            }
        }
        return sort.get(0);
    }

    private void handleQueryParamTime(DataQueryParam queryParam) {
        if (Objects.isNull(queryParam.getStartTimePredict())) {
            queryParam.setStartTimePredict(queryParam.getStartTime());
        }
        if (Objects.isNull(queryParam.getEndTimePredict())) {
            queryParam.setEndTimePredict(queryParam.getEndTime());
        }
        if (Objects.isNull(queryParam.getCycle())) {
            queryParam.setCycle(AggregationCycle.FIFTEEN_MINUTES);
        }
    }

    private List<DatalogValue> assembleData(List<DatalogValue> before, LocalDateTime st, LocalDateTime et, Integer cycle) {
        if (CollectionUtils.isEmpty(before)) {
            before = new ArrayList<>();
        }
        List<DatalogValue> after = new ArrayList<>();
        List<LocalDateTime> timeRange = TimeUtil.getTimeRange(st, et, cycle);
        for (LocalDateTime time : timeRange) {
            DatalogValue data = before.stream().filter(dataLogData -> Objects.equals(dataLogData.getTime(), TimeUtil.localDateTime2timestamp(time)))
                    .findFirst().orElse(new DatalogValue());
            if (Objects.isNull(data.getTime())) {
                data.setTime(TimeUtil.localDateTime2timestamp(time));
            }
            after.add(data);
        }
        return after;
    }

    @Override
    public List<PipelineLossColdDataEntry> queryPipelineLossColdData(DataQueryParam queryParam, Long projectId) {
        handleQueryParamTime(queryParam);
        List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.querySystem(projectId);
        //查询房间下的冷水主机
        List<Long> roomIds = refrigeratingSystems.stream().map(RefrigeratingSystem::getRoomId).collect(Collectors.toList());
        //管损
        List<ColdActual> actuals = coldActualDao.queryColdActualData(Collections.singletonList(ColdLoadType.TOTAL), Collections.singletonList(PredictDataType.LOSS),
                queryParam.getStartTime(), queryParam.getEndTime(), roomIds);
        //天气预测
        List<WeatherPredict> weatherPredicts = weatherPredictDao.queryWeather(queryParam.getStartTimePredict(), queryParam.getEndTimePredict());
        List<DatalogValue> temp = transData(weatherPredicts, true, queryParam);
        List<DatalogValue> hum = transData(weatherPredicts, false, queryParam);
        List<PipelineLossColdDataEntry> result = new ArrayList<>();
        //空调
        List<BaseVo> airs = weatherCrawlingDataService.queryNodesByMonitor(projectId);
        Map<Integer, List<TrendDataVo>> dataLogResult = quantityManageService.queryDataLogBatch(
                operationTrendService.createQuantityDataSearchVo(airs, new QueryParam(queryParam.getStartTime(),
                        queryParam.getEndTime(), AggregationCycle.FIVE_MINUTES), Arrays.asList(
                        QuantityDef.getAiPredictForHumQuantitySetting(), QuantityDef.getAiPredictForTempQuantitySetting())));
        //温湿度实际值
        List<TrendDataVo> tempActual = dataLogResult.get(QuantityDef.getAiPredictForTempQuantitySetting().getId());
        List<TrendDataVo> humActual = dataLogResult.get(QuantityDef.getAiPredictForHumQuantitySetting().getId());
        for (RefrigeratingSystem refrigeratingSystem : refrigeratingSystems) {
            List<ColdActual> coldActuals = actuals.stream().filter(coldActual -> Objects.equals(coldActual.getRoomId(), refrigeratingSystem.getRoomId()))
                    .collect(Collectors.toList());
            PipelineLossColdDataEntry entry = new PipelineLossColdDataEntry();
            entry.setProjectId(refrigeratingSystem.getProjectId());
            entry.setSystemId(refrigeratingSystem.getId());
            entry.setPipelineCold(transData(coldActuals, queryParam));
            entry.setHumidityPredict(hum);
            entry.setTempPredict(temp);
            entry.setTempActual(assembleData(operationTrendService.getNonValueDataLog(tempActual), queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle()));
            entry.setHumidityActual(assembleData(operationTrendService.getNonValueDataLog(humActual), queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle()));
            result.add(entry);

        }
        return result;
    }

    @Override
    public List<OptimizationOfRefrigeratorWaterEntry> queryOptimizationOfRefrigeratorWater(DataQueryParam queryParam, Long projectId) throws IllegalAccessException, InstantiationException {
        handleQueryParamTime(queryParam);
        List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.querySystem(projectId);
        //查询房间下的冷水主机
        List<Long> roomIds = refrigeratingSystems.stream().map(RefrigeratingSystem::getRoomId).collect(Collectors.toList());
        List<Map<String, Object>> roomWithChildren = modelServiceUtils.queryWithChildren(NodeLabelDef.ROOM, roomIds, Collections.singletonList(NodeLabelDef.COLD_WATER_MAINENGINE));
        //房间和冷机节点
        List<BaseVo> baseVos = JsonTransferUtils.transferList(roomWithChildren, BaseVo.class);
        List<BaseVo> coldDevices = new ArrayList<>();
        //获得冷机
        getEndPipeLine(baseVos, coldDevices);
        //查询冷水主机下关联的冷冻水管
        List<PipeNetworkConnectionModel> connectionModels = connectionModelDao.
                getTopOrDownBatchWithNodes(coldDevices, false, projectId);
        List<BaseVo> coldTotalPipeLines = connectionModels.stream().filter(model -> Objects.equals(model.getOutflowLabel(), NodeLabelDef.PIPELINE))
                .map(model -> new BaseVo(model.getOutflowId(), model.getOutflowLabel()))
                .collect(Collectors.toList());

        List<BaseVo> pipeLineList = topology1Service.queryFlowNode(projectId, EnergyTypeDef.COLD, coldDevices, NodeLabelDef.PIPELINE);
        //获得支管
        List<BaseVo> endPipeline = new ArrayList<>();
        getEndPipeLine(pipeLineList, endPipeline);
        List<BaseVo> allNodes = new ArrayList<>(coldDevices);
        allNodes.addAll(coldTotalPipeLines);
        allNodes.addAll(endPipeline);
        //查询冷冻水总管的流量和回水温度 查询冷机出水温度  加瞬时热流量
        Map<Integer, List<TrendDataVo>> dataLogResult = quantityManageService.queryDataLogBatch(
                operationTrendService.createQuantityDataSearchVo(allNodes, new QueryParam(queryParam.getStartTime(),
                        queryParam.getEndTime(), AggregationCycle.FIVE_MINUTES), Arrays.asList(QuantityDef.getEndReturnTemp(),
                        QuantityDef.getColdPipelineStream(), QuantityDef.getFreezingWaterForSupplyStandardQuantitySetting(),
                        QuantityDef.getFreezingWaterPipelineForStream())));
        //查询房间的冷量需求预测值
        Map<Long, List<DatalogValue>> predictColdLoadData = getPredictColdLoadData(queryParam.getStartTimePredict(), queryParam.getEndTimePredict(), roomIds, queryParam.getCycle());
        //处理系统和末端管道的关系
        Map<Long, List<BaseVo>> longListMap = handleDataWithSystem(baseVos, refrigeratingSystems, connectionModels, pipeLineList);
        return assembleRefrigeratorWater(dataLogResult, refrigeratingSystems,
                predictColdLoadData, baseVos, connectionModels, longListMap, queryParam);
    }

    /**
     * 冷机状态、冷冻泵状态、冷却泵状态、冷却塔状态,泵，冷却塔频率来确定状态，冷机--1000212电流代替开启的测点
     * 冷机功率、冷冻泵功率、冷却泵功率、冷却塔功率
     * 冷机额定冷负荷、冷机额定功率、冷冻泵额定功率、冷却泵额定功率、冷却塔额定功率
     * 总冷负荷值、总冷负荷需求预测值
     * 厂区温度、湿度预测值
     * 下一时刻冷机状态、冷冻泵状态、冷却泵状态、冷却塔状态
     *
     * @param queryParam
     * @return
     */
    @Override
    public List<TotalSystemPowerEntry> queryTotalSystemPower(DataQueryParam queryParam, Long projectId) throws IllegalAccessException, InstantiationException {
        handleQueryParamTime(queryParam);
        List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.querySystem(projectId);
        List<Long> systemIds = refrigeratingSystems.stream().map(RefrigeratingSystem::getId).collect(Collectors.toList());
        //查询房间下的冷水主机
        List<Long> roomIds = refrigeratingSystems.stream().map(RefrigeratingSystem::getRoomId).collect(Collectors.toList());
        List<Map<String, Object>> roomWithChildren = modelServiceUtils.queryWithChildren(NodeLabelDef.ROOM, roomIds, Arrays.asList(NodeLabelDef.COLD_WATER_MAINENGINE,
                NodeLabelDef.PUMP, NodeLabelDef.COOLING_TOWER));
        //房间和底下的节点
        List<PumpVo> baseVos = JsonTransferUtils.transferList(roomWithChildren, PumpVo.class);
        List<BaseVo> allNodesWithRoom = JsonTransferUtils.transferList(roomWithChildren, BaseVo.class);
        List<BaseVo> device = new ArrayList<>();
        getEndPipeLine(allNodesWithRoom, device);
        // 查询制冷房间关联的管道
        List<BaseVo> mains = device.stream().filter(baseVo -> Objects.equals(baseVo.getModelLabel(), NodeLabelDef.COLD_WATER_MAINENGINE)).collect(Collectors.toList());
        List<EnergySupplyToPo> energySupplyToPos = energySupplyDao.queryEnergySupplyTo(mains, System.currentTimeMillis(), EnergySupplyToPo.class);
        //冷机关联的一段线
        List<BaseVo> lineNodeList = energySupplyToPos.stream()
                .filter(energySupplyToPo -> Objects.equals(NodeLabelDef.LINE_SEGMENT_WITH_SWITCH, energySupplyToPo.getObjectlabel())).map(energySupplyToPo -> new BaseVo(energySupplyToPo.getObjectid(), energySupplyToPo.getObjectlabel())).collect(Collectors.toList());

        List<BaseVo> pipeLineList = topology1Service.queryFlowNode(projectId, EnergyTypeDef.COLD, mains, NodeLabelDef.PIPELINE);
        //查询冷水主机下关联的冷冻水管
        List<PipeNetworkConnectionModel> connectionModels = connectionModelDao.
                getTopOrDownBatchWithNodes(mains, false, projectId);
        //获得支管
        List<BaseVo> endPipeline = new ArrayList<>();
        //空调
        List<BaseVo> airs = weatherCrawlingDataService.queryNodesByMonitor(projectId);
        getEndPipeLine(pipeLineList, endPipeline);
        device.addAll(endPipeline);
        device.addAll(lineNodeList);
        device.addAll(airs);
        //总冷负荷值还需要支管
        Map<Integer, List<TrendDataVo>> dataLogResult = quantityManageService.queryDataLogBatch(
                operationTrendService.createQuantityDataSearchVo(device, new QueryParam(queryParam.getStartTime(),
                        queryParam.getEndTime(), AggregationCycle.FIVE_MINUTES), Arrays.asList(QuantityDef.getMainPowerQuantitySetting(),
                        QuantityDef.getDeviceTowerEnergy(), QuantityDef.getFreezingDeviceFrequency(), QuantityDef.getDeviceFrequency(),
                        QuantityDef.getMachineI(), QuantityDef.getFreezingWaterPipelineForStream(), QuantityDef.getAiPredictForHumQuantitySetting(), QuantityDef.getAiPredictForTempQuantitySetting())));
        List<WeatherPredict> weatherPredicts = weatherPredictDao.queryWeather(queryParam.getStartTimePredict(), queryParam.getEndTimePredict());
        Map<Long, List<DatalogValue>> predictColdLoadData = getPredictColdLoadData(queryParam.getStartTimePredict(), queryParam.getEndTimePredict(), roomIds, queryParam.getCycle());
        //查询策略
        List<AiStartStopStrategy> strategies = aiStartStopStrategyDao.queryAiStartStopStrategy(systemIds,
                Arrays.asList(StrategyTypeDef.start, StrategyTypeDef.stop), queryParam.getStartTimePredict(), queryParam.getEndTimePredict());
        List<Long> ids = strategies.stream().map(AiStartStopStrategy::getId).distinct().collect(Collectors.toList());
        //查询策略详情，和连锁信息
        List<StrategyObjectMap> strategyObjectMaps = strategyObjectMapDao.queryStrategyObjectMap(ids);
        //管道瞬时热流量
        List<TrendDataVo> pipeline = dataLogResult.get(QuantityDef.getFreezingWaterPipelineForStream().getId());
        //处理系统和末端管道的关系
        Map<Long, List<BaseVo>> longListMap = handleDataWithSystem(allNodesWithRoom, refrigeratingSystems, connectionModels, pipeLineList);
        List<TotalSystemPowerEntry> result = assembleColdMachine(dataLogResult, baseVos, refrigeratingSystems, strategies,
                strategyObjectMaps, energySupplyToPos, queryParam);
        assembleTotalCold(pipeline, result, weatherPredicts, predictColdLoadData, longListMap, refrigeratingSystems, queryParam);
        return result;
    }

    @Override
    public void exportWeatherDataCompare(DataQueryParam queryParam, HttpServletResponse response) throws Exception {
        String fileName = "天气数据对比文档";
        Workbook workBook = null;
        try {
            workBook = PoiExcelUtils.createWorkBook(ExcelType.BIG_DATA);
            QueryParam param = new QueryParam(queryParam.getStartTime(), queryParam.getEndTime(), AggregationCycle.FIFTEEN_MINUTES);
            List<AIPredictWeatherVo> hum = operationTrendService.queryAiPredictForHum(param,GlobalInfoUtils.getProjectId());
            List<AIPredictWeatherVo> temp = operationTrendService.queryAiPredictForTemp(param, GlobalInfoUtils.getProjectId());
            QueryWeatherParam queryWeatherParam = new QueryWeatherParam();
            queryWeatherParam.setStarttime(TimeUtil.localDateTime2timestamp(queryParam.getStartTime()));
            queryWeatherParam.setEndtime(TimeUtil.localDateTime2timestamp(queryParam.getEndTime()));
            ResultWithTotal<List<Weather>> listResultWithTotal = weatherDao.queryWeather(queryWeatherParam);
            List<Weather> data = listResultWithTotal.getData();
            List<Integer> colWidth = Arrays.asList(18, 18, 18, 18, 18, 18, 18, 18, 18, 18, 18, 18, 18, 18, 18);
            PoiExcelUtils.createSheet(workBook, fileName, (sheet, baseCellStyle, rowIndex) -> {
                int rowNum = 0;
                writeHeader(sheet, baseCellStyle, rowNum++);
                writeData(sheet, baseCellStyle, rowNum, hum, temp, data);
            }, colWidth);
            FileUtils.downloadExcel(response, workBook, "天气数据对比" + LocalDateTime.now().format(TimeUtil.SECONDTIMEFORMAT), CommonUtils.APPLICATION_MSEXCEL);

        } catch (Exception e) {
            log.info("输出天气对比文件异常");
        } finally {
            if (Objects.nonNull(workBook)) {
                workBook.close();
            }
        }

    }

    private void writeData(Sheet sheet, CellStyle baseCellStyle, int rowNum, List<AIPredictWeatherVo> hum, List<AIPredictWeatherVo> temp, List<Weather> data) {
        int col;

        for (AIPredictWeatherVo item : hum) {
            col = 0;
            Row row = PoiExcelUtils.createRow(sheet, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, TimeUtil.format(item.getLogtime(), TimeUtil.LONG_TIME_FORMAT));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, (item.getLogtime()));
            AIPredictWeatherVo temp1 = temp.stream().filter(aiPredictWeatherVo -> Objects.equals(item.getLogtime(), aiPredictWeatherVo.getLogtime()))
                    .findAny().orElse(new AIPredictWeatherVo());
            Weather weather = data.stream().filter(aiPredictWeatherVo -> Objects.equals(item.getLogtime(), aiPredictWeatherVo.getLogTime()))
                    .findAny().orElse(new Weather());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, handleNoneData(temp1.getFactValue()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, handleNoneData(weather.getTempAvg()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, handleNoneData(temp1.getPredictValue()));
            Double value = CommonUtils.calcDouble(weather.getTempAvg(), temp1.getFactValue(), EnumOperationType.RATE.getId());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, handleNoneData(value));
            Double aDouble = CommonUtils.calcDouble(temp1.getPredictValue(), temp1.getFactValue(), EnumOperationType.RATE.getId());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, handleNoneData(aDouble));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, handleNoneData(CommonUtils.calcDouble(aDouble, value, EnumOperationType.SUBTRACT.getId())));

            PoiExcelUtils.createCell(row, col++, baseCellStyle, handleNoneData(item.getFactValue()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, handleNoneData(weather.getHumidity()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, handleNoneData(item.getPredictValue()));
            Double value1 = CommonUtils.calcDouble(weather.getHumidity(), item.getFactValue(), EnumOperationType.RATE.getId());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, handleNoneData(value1));
            Double aDouble1 = CommonUtils.calcDouble(item.getPredictValue(), item.getFactValue(), EnumOperationType.RATE.getId());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, handleNoneData(aDouble1));
            PoiExcelUtils.createCell(row, col, baseCellStyle, handleNoneData(CommonUtils.calcDouble(aDouble1, value1, EnumOperationType.SUBTRACT.getId())));
            rowNum++;
        }
    }

    /**
     * 保留2位
     *
     * @param value
     * @return
     */
    private String handleNoneData(Double value) {
        if (Objects.isNull(value)) {
            return CommonUtils.BLANK_STR;
        }
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(value);
    }

    private void writeHeader(Sheet sheet, CellStyle baseCellStyle, int startRow) {
        LinkedHashMap<String, CellStyle> headerMap = new LinkedHashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        headerMap.put("序号", baseCellStyle);
        headerMap.put("时间", baseCellStyle);
        headerMap.put("时间戳", baseCellStyle);
        headerMap.put("温度实际值", baseCellStyle);
        headerMap.put("温度爬虫预测值", baseCellStyle);
        headerMap.put("温度华科预测值", baseCellStyle);
        headerMap.put("温度爬虫预测值--实际值比率", baseCellStyle);
        headerMap.put("温度华科预测值--实际值比率", baseCellStyle);
        headerMap.put("温度华科预测值--实际值比率减去温度爬虫预测值--实际值比率", baseCellStyle);
        headerMap.put("湿度实际值", baseCellStyle);
        headerMap.put("湿度爬虫预测值", baseCellStyle);
        headerMap.put("湿度华科预测值", baseCellStyle);
        headerMap.put("湿度爬虫预测值--实际值比率", baseCellStyle);
        headerMap.put("湿度华科预测值--实际值比率", baseCellStyle);
        headerMap.put("湿度华科预测值--实际值比率减去湿度爬虫预测值--实际值比率", baseCellStyle);
        PoiExcelUtils.createHeaderName(sheet, startRow, headerMap);
    }

    /**
     * 拼接天气数据
     *
     * @param pipeline
     * @param result
     * @param weatherPredicts
     * @param predictColdLoadData
     * @param longListMap
     * @param refrigeratingSystems
     */
    private void assembleTotalCold(List<TrendDataVo> pipeline, List<TotalSystemPowerEntry> result,
                                   List<WeatherPredict> weatherPredicts, Map<Long, List<DatalogValue>> predictColdLoadData
            , Map<Long, List<BaseVo>> longListMap, List<RefrigeratingSystem> refrigeratingSystems, DataQueryParam queryParam) {
        for (TotalSystemPowerEntry entry : result) {
            RefrigeratingSystem system = refrigeratingSystems.stream().filter(refrigeratingSystem -> Objects.equals(entry.getSystemId(), refrigeratingSystem.getId()))
                    .findAny().orElse(new RefrigeratingSystem());
            if (Objects.isNull(system.getRoomId())) {
                continue;
            }
            List<DatalogValue> dataLogData = transData(weatherPredicts, true, queryParam);
            List<DatalogValue> logData = transData(weatherPredicts, false, queryParam);
            List<DatalogValue> dataList = predictColdLoadData.get(system.getRoomId());
            List<BaseVo> baseVos = longListMap.get(system.getId());
            HashSet<BaseVo> hashSet = new HashSet<>(baseVos);
            List<TrendDataVo> dataVoList = pipeline.stream().filter(trendDataVo -> hashSet.contains(new BaseVo(trendDataVo.getMonitoredid(), trendDataVo.getMonitoredlabel())))
                    .collect(Collectors.toList());
            List<DatalogValue> endCold = calculateEndCold(dataVoList);
            entry.setTotalColdLoad(assembleData(endCold, queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle()));
            entry.setHumidityPredict(logData);
            entry.setTempPredict(dataLogData);
            entry.setTotalColdLoadPredict(assembleData(dataList, queryParam.getStartTimePredict(), queryParam.getEndTimePredict(), queryParam.getCycle()));
        }


    }

    private List<DatalogValue> calculateEndCold(List<TrendDataVo> dataVoList) {
        List<DatalogValue> result = new ArrayList<>();
        Map<BaseVo, List<TrendDataVo>> map = dataVoList.stream().
                collect(Collectors.groupingBy(trendDataVo -> new BaseVo(trendDataVo.getMonitoredid(), trendDataVo.getMonitoredlabel())));
        List<DatalogValue> dataLogData = new ArrayList<>();
        for (Map.Entry<BaseVo, List<TrendDataVo>> entryMap : map.entrySet()) {
            List<TrendDataVo> value = entryMap.getValue();
            //过滤出属于某个设备的采集设备的数据，再取第一个有值的，然后把这个类型的值全部加起来
            List<DatalogValue> dataList = operationTrendService.getNonValueDataLog(value);
            dataLogData.addAll(dataList);
        }
        Map<Long, List<DatalogValue>> value = dataLogData.stream().filter(dataLogData1 -> Objects.nonNull(dataLogData1.getValue()))
                .collect(Collectors.groupingBy(DatalogValue::getTime));
        for (Map.Entry<Long, List<DatalogValue>> entry : value.entrySet()) {
            double sum = entry.getValue().stream().mapToDouble(DatalogValue::getValue).sum();
            Double aDouble = CommonUtils.calcDouble(sum, UNIT, EnumOperationType.DIVISION.getId());
            DatalogValue e = new DatalogValue();
            e.setTime(entry.getKey());
            e.setValue(aDouble);
            result.add(e);
        }
        return result;
    }

    /**
     * 拼接设备的数据
     *
     * @param dataLogResult
     * @param baseVos
     * @param refrigeratingSystems
     * @param strategies
     * @param strategyObjectMaps
     * @return
     */
    private List<TotalSystemPowerEntry> assembleColdMachine(Map<Integer, List<TrendDataVo>> dataLogResult,
                                                            List<PumpVo> baseVos, List<RefrigeratingSystem> refrigeratingSystems,
                                                            List<AiStartStopStrategy> strategies, List<StrategyObjectMap> strategyObjectMaps,
                                                            List<EnergySupplyToPo> energySupplyToPos, DataQueryParam queryParam) {
        //功率
        List<TrendDataVo> power = dataLogResult.get(QuantityDef.getMainPowerQuantitySetting().getId());
        //冷却泵频率
        List<TrendDataVo> coolPump = dataLogResult.get(QuantityDef.getFreezingDeviceFrequency().getId());
        //塔频率
        List<TrendDataVo> towerData = dataLogResult.get(QuantityDef.getDeviceTowerEnergy().getId());
        //冷冻泵频率
        List<TrendDataVo> freezingPump = dataLogResult.get(QuantityDef.getDeviceFrequency().getId());
        //冷机电流
        List<TrendDataVo> main = dataLogResult.get(QuantityDef.getMachineI().getId());
        //温湿度实际值
        List<TrendDataVo> tempActual = dataLogResult.get(QuantityDef.getAiPredictForTempQuantitySetting().getId());
        List<TrendDataVo> humActual = dataLogResult.get(QuantityDef.getAiPredictForHumQuantitySetting().getId());
        List<TotalSystemPowerEntry> result = new ArrayList<>();
        for (RefrigeratingSystem system : refrigeratingSystems) {
            TotalSystemPowerEntry entry = new TotalSystemPowerEntry();
            List<ColdMachineVo> machineVos = new ArrayList<>();
            entry.setColdMachineVos(machineVos);
            entry.setSystemId(system.getId());
            entry.setProjectId(system.getProjectId());
            PumpVo pumpData = baseVos.stream().filter(pumpVo -> Objects.equals(system.getRoomId(), pumpVo.getId()))
                    .findAny().orElse(new PumpVo());
            if (CollectionUtils.isEmpty(pumpData.getChildren())) {
                continue;
            }
            List<PumpVo> towers = pumpData.getChildren().stream().filter(pumpVo1 -> Objects.equals(pumpVo1.getModelLabel(), NodeLabelDef.COOLING_TOWER)).collect(Collectors.toList());
            List<PumpVo> freezingPumps = pumpData.getChildren().stream().filter(pumpVo1 -> Objects.equals(pumpVo1.getModelLabel(), NodeLabelDef.PUMP)
                    && Objects.equals(pumpVo1.getFunctionType(), PumpFunctionType.REFRIGERATING_PUMP)).collect(Collectors.toList());
            List<PumpVo> coolPumps = pumpData.getChildren().stream().filter(pumpVo1 -> Objects.equals(pumpVo1.getModelLabel(), NodeLabelDef.PUMP)
                    && Objects.equals(pumpVo1.getFunctionType(), PumpFunctionType.COOLING_PUMP)).collect(Collectors.toList());
            List<PumpVo> mains = pumpData.getChildren().stream().filter(pumpVo1 -> Objects.equals(pumpVo1.getModelLabel(), NodeLabelDef.COLD_WATER_MAINENGINE)).collect(Collectors.toList());
            machineVos.addAll(assembleListColdMachineVo(mains, power, main, strategies, strategyObjectMaps, energySupplyToPos, queryParam));
            machineVos.addAll(assembleListColdMachineVo(towers, power, towerData, strategies, strategyObjectMaps, energySupplyToPos, queryParam));
            machineVos.addAll(assembleListColdMachineVo(coolPumps, power, coolPump, strategies, strategyObjectMaps, energySupplyToPos, queryParam));
            machineVos.addAll(assembleListColdMachineVo(freezingPumps, power, freezingPump, strategies, strategyObjectMaps, energySupplyToPos, queryParam));
            if (CollectionUtils.isNotEmpty(machineVos)) {
                entry.setTempActual(assembleData(operationTrendService.getNonValueDataLog(tempActual), queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle()));
                entry.setHumidityActual(assembleData(operationTrendService.getNonValueDataLog(humActual), queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle()));
                result.add(entry);
            }
        }
        return result;
    }

    /**
     * 拼接某类设备数据
     *
     * @param mains
     * @param power
     * @param main
     * @param strategies
     * @param strategyObjectMaps
     * @return
     */
    private List<ColdMachineVo> assembleListColdMachineVo(List<PumpVo> mains, List<TrendDataVo> power, List<TrendDataVo> main,
                                                          List<AiStartStopStrategy> strategies, List<StrategyObjectMap> strategyObjectMaps,
                                                          List<EnergySupplyToPo> energySupplyToPos, DataQueryParam queryParam) {
        List<ColdMachineVo> coldMachineVos = new ArrayList<>();
        Map<BaseVo, List<TrendDataVo>> map = main.stream().collect(Collectors.groupingBy(trendDataVo -> new BaseVo(trendDataVo.getMonitoredid(), trendDataVo.getMonitoredlabel())));
        Map<BaseVo, List<EnergySupplyToPo>> baseVoListMap = energySupplyToPos.stream()
                .collect(Collectors.groupingBy(
                        energySupplyToPo -> new BaseVo(energySupplyToPo.getSupplytoid(), energySupplyToPo.getSupplytolabel())));
        for (PumpVo baseVo : mains) {
            //功率
            List<DatalogValue> powerData = assembleSingleDeviceData(baseVoListMap.get(new BaseVo(baseVo.getId(), baseVo.getModelLabel())), power);
            //状态
            List<DatalogValue> data = operationTrendService.getNonValueDataLog(map.get(new BaseVo(baseVo.getId(), baseVo.getModelLabel())));
            //下一时刻
            ColdMachineVo coldMachineVo = new ColdMachineVo();
            coldMachineVo.setObjectId(baseVo.getId());
            coldMachineVo.setObjectLabel(baseVo.getModelLabel());
            coldMachineVo.setPower(assembleData(powerData, queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle()));
            coldMachineVo.setStatus(assembleData(data, queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle()));
            coldMachineVo.setPumpFunctionType(baseVo.getFunctionType());
            coldMachineVo.setRatedCoolingLoad(baseVo.getRatedRefrigeration());
            coldMachineVo.setRatedPower(baseVo.getRatedMotorPower());
            coldMachineVos.add(coldMachineVo);
            assembleNextStatus(baseVo, strategies, strategyObjectMaps, coldMachineVo, queryParam);

        }
        return coldMachineVos;
    }

    private List<DatalogValue> assembleSingleDeviceData(List<EnergySupplyToPo> energySupplyToPos, List<TrendDataVo> dataVoList) {
        List<DatalogValue> dataLogData = new ArrayList<>();
        if (CollectionUtils.isEmpty(energySupplyToPos)) {
            return Collections.emptyList();
        }
        Set<BaseVo> lines = energySupplyToPos.stream().map(energySupplyToPo -> new BaseVo(energySupplyToPo.getObjectid(), energySupplyToPo.getObjectlabel()))
                .collect(Collectors.toSet());
        for (BaseVo baseVo : lines) {
            //过滤出属于某个设备的采集设备的数据，再取第一个有值的，然后把这个类型的值全部加起来
            List<TrendDataVo> trendDataVos = dataVoList.stream().
                    filter(trendDataVo -> Objects.equals(baseVo.getId(), trendDataVo.getMonitoredid())
                            && Objects.equals(baseVo.getModelLabel(), trendDataVo.getMonitoredlabel())).collect(Collectors.toList());
            List<DatalogValue> dataList = operationTrendService.getNonValueDataLog(trendDataVos);
            dataLogData.addAll(dataList);
        }
        return transAllData(dataLogData);
    }

    private List<DatalogValue> transAllData(List<DatalogValue> dataLogData) {
        if (CollectionUtils.isEmpty(dataLogData)) {
            return Collections.emptyList();
        }
        List<DatalogValue> operationTrendVoList = new ArrayList<>();
        Map<Long, List<DatalogValue>> longListMap = dataLogData.stream().collect(Collectors.groupingBy(DatalogValue::getTime));
        for (Map.Entry<Long, List<DatalogValue>> entry : longListMap.entrySet()) {
            List<DatalogValue> collect = entry.getValue().stream().filter(dataLogData1 -> Objects.nonNull(dataLogData1.getValue())).collect(Collectors.toList());
            DatalogValue operationTrendVo = new DatalogValue();
            if (CollectionUtils.isNotEmpty(collect)) {
                double sum = collect.stream().mapToDouble(DatalogValue::getValue).sum();
                operationTrendVo.setValue(sum);
            }
            operationTrendVo.setTime(entry.getKey());
            operationTrendVoList.add(operationTrendVo);
        }
        return operationTrendVoList;
    }

    private void assembleNextStatus(PumpVo baseVo, List<AiStartStopStrategy> strategies, List<StrategyObjectMap> strategyObjectMaps,
                                    ColdMachineVo coldMachineVo, DataQueryParam queryParam) {
        List<DatalogValue> nextStatus = new ArrayList<>();
        for (AiStartStopStrategy strategy : strategies) {
            Set<Long> ids = strategyObjectMaps.stream().filter(strategyObjectMap -> Objects.equals(strategyObjectMap.getObjectId(), baseVo.getId())
                    && Objects.equals(strategyObjectMap.getObjectLabel(), baseVo.getModelLabel())).map(StrategyObjectMap::getStrategyId).collect(Collectors.toSet());
            if (ids.contains(strategy.getId())) {
                if (Objects.equals(strategy.getStrategyType(), StrategyTypeDef.start)) {
                    DatalogValue e = new DatalogValue();
                    e.setTime(strategy.getOperationTime());
                    e.setValue(1.0);
                    nextStatus.add(e);
                } else if (Objects.equals(strategy.getStrategyType(), StrategyTypeDef.stop)) {
                    DatalogValue e = new DatalogValue();
                    e.setTime(strategy.getOperationTime());
                    e.setValue(0.0);
                    nextStatus.add(e);
                }
            }
        }
        coldMachineVo.setNextStatus(nextStatus);
        List<LocalDateTime> timeRange = TimeUtil.getTimeRange(queryParam.getStartTimePredict(), queryParam.getEndTimePredict(), queryParam.getCycle());
        for (LocalDateTime time : timeRange) {
            DatalogValue data = nextStatus.stream().filter(dataLogData -> Objects.equals(TimeUtil.localDateTime2timestamp(time), dataLogData.getTime()))
                    .findAny().orElse(new DatalogValue());
            if (Objects.isNull(data.getTime())) {
                data.setTime(TimeUtil.localDateTime2timestamp(time));
                nextStatus.add(data);
            }
        }
    }

    private List<OptimizationOfRefrigeratorWaterEntry> assembleRefrigeratorWater(Map<Integer, List<TrendDataVo>> dataLogResult, List<RefrigeratingSystem> refrigeratingSystems,
                                                                                 Map<Long, List<DatalogValue>> predictColdLoadData, List<BaseVo> baseVos,
                                                                                 List<PipeNetworkConnectionModel> connectionModels, Map<Long, List<BaseVo>> longListMap, DataQueryParam param) {
        //查询冷冻水总管的流量
        List<TrendDataVo> stream = dataLogResult.get(QuantityDef.getColdPipelineStream().getId());
        //查询冷冻水总管的回水温度
        List<TrendDataVo> cold = dataLogResult.get(QuantityDef.getEndReturnTemp().getId());
        //查询冷机出水温度
        List<TrendDataVo> temp = dataLogResult.get(QuantityDef.getFreezingWaterForSupplyStandardQuantitySetting().getId());
        //处理系统和总管的关系
        Map<Long, List<BaseVo>> systemWithPipeLine = handleDataWithSystemAndTotalPipeLine(baseVos, refrigeratingSystems, connectionModels);
        //处理系统和冷机的关系
        Map<Long, List<BaseVo>> systemWithMachine = handleDataWithSystemAndColdMachine(baseVos, refrigeratingSystems);
        //管道瞬时热流量
        List<TrendDataVo> pipeline = dataLogResult.get(QuantityDef.getFreezingWaterPipelineForStream().getId());
        List<OptimizationOfRefrigeratorWaterEntry> result = new ArrayList<>();
        for (RefrigeratingSystem system : refrigeratingSystems) {
            OptimizationOfRefrigeratorWaterEntry entry = new OptimizationOfRefrigeratorWaterEntry();
            entry.setSystemId(system.getId());
            entry.setProjectId(system.getProjectId());
            List<DatalogValue> dataLogData = predictColdLoadData.get(system.getRoomId());
            entry.setColdPredict(assembleData(dataLogData, param.getStartTimePredict(), param.getEndTimePredict(), param.getCycle()));
            List<SingleDataEntry> tempEntries = assembleSingleEntry(systemWithMachine, temp, system.getId(), param);
            List<SingleDataEntry> streamEntries = assembleSingleEntry(systemWithPipeLine, stream, system.getId(), param);
            List<SingleDataEntry> machineEntries = assembleSingleEntry(systemWithPipeLine, cold, system.getId(), param);
            List<BaseVo> nodes = longListMap.get(system.getId());
            HashSet<BaseVo> hashSet = new HashSet<>(nodes);
            List<TrendDataVo> dataVoList = pipeline.stream().filter(trendDataVo -> hashSet.contains(new BaseVo(trendDataVo.getMonitoredid(), trendDataVo.getMonitoredlabel())))
                    .collect(Collectors.toList());
            List<DatalogValue> endCold = calculateEndCold(dataVoList);
            entry.setTotalColdLoad(assembleData(endCold, param.getStartTime(), param.getEndTime(), param.getCycle()));
            entry.setEndReturnWaterTemp(tempEntries);
            entry.setReturnWaterSet(machineEntries);
            entry.setFreezingPipelineFlow(streamEntries);
            result.add(entry);
        }
        return result;
    }

    private Map<Long, List<BaseVo>> handleDataWithSystemAndColdMachine(List<BaseVo> baseVos, List<RefrigeratingSystem> refrigeratingSystems) {
        Map<Long, List<BaseVo>> result = new HashMap<>();
        for (RefrigeratingSystem system : refrigeratingSystems) {

            BaseVo room = baseVos.stream().filter(baseVo -> Objects.equals(baseVo.getId(), system.getRoomId()))
                    .findAny().orElse(new BaseVo());
            if (CollectionUtils.isEmpty(room.getChildren())) {
                continue;
            }
            //房间和冷机对应关系
            List<BaseVo> children = room.getChildren();
            //冷机和冷冻总管对应关系
            result.put(system.getId(), children);
            //系统和末端管道对应关系
        }
        return result;
    }

    private Map<Long, List<BaseVo>> handleDataWithSystemAndTotalPipeLine(List<BaseVo> baseVos, List<RefrigeratingSystem> refrigeratingSystems, List<PipeNetworkConnectionModel> connectionModels
    ) {
        Map<Long, List<BaseVo>> result = new HashMap<>();
        connectionModels = connectionModels.stream().filter(pipeNetworkConnectionModel -> Objects.equals(pipeNetworkConnectionModel.getOutflowLabel(), NodeLabelDef.PIPELINE))
                .collect(Collectors.toList());
        Map<BaseVo, List<PipeNetworkConnectionModel>> mapOfTotalPipesAndCold = connectionModels.stream()
                .collect(Collectors.groupingBy(model -> new BaseVo(model.getInflowId(), model.getInflowLabel())));
        for (RefrigeratingSystem system : refrigeratingSystems) {

            BaseVo room = baseVos.stream().filter(baseVo -> Objects.equals(baseVo.getId(), system.getRoomId()))
                    .findAny().orElse(new BaseVo());
            if (CollectionUtils.isEmpty(room.getChildren())) {
                continue;
            }
            //房间和冷机对应关系
            List<BaseVo> children = room.getChildren();
            //冷机和冷冻总管对应关系
            List<BaseVo> totalPipeLine = new ArrayList<>();
            for (BaseVo baseVo : children) {
                List<PipeNetworkConnectionModel> pipeNetworkConnectionModels = mapOfTotalPipesAndCold.get(baseVo);
                if (CollectionUtils.isNotEmpty(pipeNetworkConnectionModels)) {
                    totalPipeLine.addAll(pipeNetworkConnectionModels.stream().map(model -> new BaseVo(model.getOutflowId(), model.getOutflowLabel()))
                            .collect(Collectors.toList()));
                }
            }
            result.put(system.getId(), totalPipeLine);
            //系统和末端管道对应关系
        }
        return result;
    }

    private List<SingleDataEntry> assembleSingleEntry(Map<Long, List<BaseVo>> systemWithPipeLine, List<TrendDataVo> temp, Long systemId, DataQueryParam param) {
        //系统-房间-冷机-管道
        Map<BaseVo, List<TrendDataVo>> nodes = temp.stream().collect(Collectors.groupingBy(trendDataVo -> new BaseVo(trendDataVo.getMonitoredid(), trendDataVo.getMonitoredlabel())));
        List<BaseVo> baseVos = systemWithPipeLine.get(systemId);
        if (CollectionUtils.isEmpty(baseVos)) {
            return Collections.emptyList();
        }
        List<SingleDataEntry> result = new ArrayList<>();
        for (BaseVo baseVo : baseVos) {
            List<TrendDataVo> trendDataVos = nodes.get(baseVo);
            SingleDataEntry entry1 = new SingleDataEntry();
            List<DatalogValue> dataList = operationTrendService.getNonValueDataLog(trendDataVos);
            entry1.setObjectId(baseVo.getId());
            entry1.setObjectLabel(baseVo.getModelLabel());
            entry1.setDataList(assembleData(dataList, param.getStartTime(), param.getEndTime(), param.getCycle()));
            result.add(entry1);
        }
        return result;
    }

    private Map<Long, List<DatalogValue>> getPredictColdLoadData(LocalDateTime st, LocalDateTime et, List<Long> roomId, Integer cycle) {
        List<ColdPredict> result = coldPredictDao.queryColdPredictData(Arrays.asList(ColdLoadType.END, ColdLoadType.TOTAL), Arrays.asList(PredictDataType.COLD_LOAD, PredictDataType.LOSS),
                st, et, roomId);
        Map<Long, List<DatalogValue>> resultMap = new HashMap<>();
        Map<Long, List<ColdPredict>> map = result.stream().collect(Collectors.groupingBy(ColdPredict::getRoomId));
        for (Map.Entry<Long, List<ColdPredict>> entry : map.entrySet()) {
            List<ColdPredict> endActuals = entry.getValue().stream().filter(coldActual -> Objects.equals(coldActual.getColdLoadType(), ColdLoadType.END) && Objects.equals(coldActual.getPredictDataType(),
                    PredictDataType.COLD_LOAD)).collect(Collectors.toList());

            List<ColdPredict> totalActuals = entry.getValue().stream().filter(coldActual -> Objects.equals(coldActual.getColdLoadType(), ColdLoadType.TOTAL) && Objects.equals(coldActual.getPredictDataType(),
                    PredictDataType.LOSS)).collect(Collectors.toList());
            List<DatalogValue> data = new ArrayList<>();
            List<LocalDateTime> timeRange = TimeUtil.getTimeRange(st, et, cycle);
            for (LocalDateTime time : timeRange) {
                ColdPredict coldPredict1 = endActuals.stream().filter(coldPredict -> Objects.equals(time, (coldPredict.getLogTime()))).findAny().orElse(new ColdPredict());
                ColdPredict coldPredict2 = totalActuals.stream().filter(coldPredict -> Objects.equals(time, (coldPredict.getLogTime()))).findAny().orElse(new ColdPredict());
                Double aDouble = operationTrendService.addTwoValue(coldPredict1, coldPredict2);
                DatalogValue e = new DatalogValue();
                e.setValue(aDouble);
                e.setTime(TimeUtil.localDateTime2timestamp(time));
                data.add(e);
            }
            resultMap.put(entry.getKey(), data);
        }
        return resultMap;
    }

    private List<EndColdDataEntry> assembleEndColdDataEntry(Map<Long, List<BaseVo>> longListMap,
                                                            Map<BaseVo, List<DatalogValue>> dataLogMap, List<WeatherPredict> weatherPredicts, Long projectId
            , Map<Integer, List<TrendDataVo>> dataLogResult, DataQueryParam queryParam, Map<BaseVo, List<MesData>> mesData) {
        //温湿度实际值
        List<TrendDataVo> tempActual = dataLogResult.get(QuantityDef.getAiPredictForTempQuantitySetting().getId());
        List<TrendDataVo> humActual = dataLogResult.get(QuantityDef.getAiPredictForHumQuantitySetting().getId());
        List<EndColdDataEntry> result = new ArrayList<>();
        List<DatalogValue> temp = transData(weatherPredicts, true, queryParam);
        List<DatalogValue> hum = transData(weatherPredicts, false, queryParam);
        for (Map.Entry<Long, List<BaseVo>> entry : longListMap.entrySet()) {
            EndColdDataEntry entry1 = new EndColdDataEntry();
            List<BaseVo> value = entry.getValue();
            if (CollectionUtils.isEmpty(value)) {
                continue;
            }
            entry1.setSystemId(entry.getKey());
            entry1.setProjectId(projectId);
            entry1.setTempPredict(temp);
            entry1.setHumidityPredict(hum);
            List<EndColdDataVo> endColdDataVos = new ArrayList<>();
            entry1.setEndColdDataVos(endColdDataVos);
            entry1.setTempActual(assembleData(operationTrendService.getNonValueDataLog(tempActual), queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle()));
            entry1.setHumidityActual(assembleData(operationTrendService.getNonValueDataLog(humActual), queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle()));
            for (BaseVo baseVo : value) {
                EndColdDataVo coldDataVo = new EndColdDataVo();
                coldDataVo.setObjectId(baseVo.getId());
                coldDataVo.setObjectLabel(baseVo.getModelLabel());
                List<DatalogValue> dataLogData = dataLogMap.get(baseVo);
                dataLogData = assembleData(dataLogData, queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle());
                for (DatalogValue data : dataLogData) {
                    data.setValue(CommonUtils.calcDouble(data.getValue(), UNIT, EnumOperationType.DIVISION.getId()));
                }
                coldDataVo.setEndCold(dataLogData);
                endColdDataVos.add(coldDataVo);
                List<MesData> dataList = mesData.get(baseVo);
                if (CollectionUtils.isEmpty(dataList)) {
                    coldDataVo.setMesData(assembleEmpty(queryParam));
                } else {
                    coldDataVo.setMesData(dataList);
                }
            }
            if (CollectionUtils.isEmpty(endColdDataVos)) {
                continue;
            }
            result.add(entry1);
        }
        return result;
    }

    private List<DatalogValue> transData(List<ColdActual> weatherPredicts, DataQueryParam queryParam) {
        List<DatalogValue> after = new ArrayList<>();
        List<LocalDateTime> timeRange = TimeUtil.getTimeRange(queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle());
        for (LocalDateTime time : timeRange) {
            DatalogValue logData = new DatalogValue();
            logData.setTime(TimeUtil.localDateTime2timestamp(time));
            ColdActual data = weatherPredicts.stream().filter(dataLogData -> Objects.equals(dataLogData.getLogTime(), (time)))
                    .findFirst().orElse(new ColdActual());
            logData.setValue(data.getValue());
            after.add(logData);
        }
        return after;
    }

    private List<DatalogValue> transData(List<WeatherPredict> weatherPredicts, Boolean isTemp, DataQueryParam queryParam) {
        List<DatalogValue> after = new ArrayList<>();
        List<LocalDateTime> timeRange = TimeUtil.getTimeRange(queryParam.getStartTimePredict(), queryParam.getEndTimePredict(), queryParam.getCycle());
        for (LocalDateTime time : timeRange) {
            DatalogValue single = new DatalogValue();
            single.setTime(TimeUtil.localDateTime2timestamp(time));
            WeatherPredict weatherPredict = weatherPredicts.stream().filter(dataLogData -> Objects.equals(dataLogData.getLogTime(), (time)))
                    .findFirst().orElse(new WeatherPredict());
            if (Objects.nonNull(weatherPredict.getLogTime())) {
                if (Boolean.TRUE.equals(isTemp)) {
                    single.setValue(weatherPredict.getTemp());
                } else {
                    single.setValue(weatherPredict.getHumidity());
                }
            }
            after.add(single);
        }
        return after;
    }

    /**
     * 系统和支管对应关系
     *
     * @param baseVos              房间和其底下子节点
     * @param refrigeratingSystems
     * @param connectionModels
     * @param pipeLineList         总管和其底下支管
     * @return
     */
    private Map<Long, List<BaseVo>> handleDataWithSystem(List<BaseVo> baseVos, List<RefrigeratingSystem> refrigeratingSystems, List<PipeNetworkConnectionModel> connectionModels
            , List<BaseVo> pipeLineList) {
        Map<Long, List<BaseVo>> result = new HashMap<>();
        connectionModels = connectionModels.stream().filter(pipeNetworkConnectionModel -> Objects.equals(pipeNetworkConnectionModel.getOutflowLabel(), NodeLabelDef.PIPELINE))
                .collect(Collectors.toList());
        Map<BaseVo, List<PipeNetworkConnectionModel>> mapOfTotalPipesAndCold = connectionModels.stream()
                .collect(Collectors.groupingBy(model -> new BaseVo(model.getInflowId(), model.getInflowLabel())));
        for (RefrigeratingSystem system : refrigeratingSystems) {

            BaseVo room = baseVos.stream().filter(baseVo -> Objects.equals(baseVo.getId(), system.getRoomId()))
                    .findAny().orElse(new BaseVo());
            if (CollectionUtils.isEmpty(room.getChildren())) {
                continue;
            }
            //房间和冷机对应关系
            List<BaseVo> children = room.getChildren();
            //冷机和冷冻总管对应关系
            List<BaseVo> totalPipeLine = new ArrayList<>();
            for (BaseVo baseVo : children) {
                List<PipeNetworkConnectionModel> pipeNetworkConnectionModels = mapOfTotalPipesAndCold.get(baseVo);
                if (CollectionUtils.isNotEmpty(pipeNetworkConnectionModels)) {
                    totalPipeLine.addAll(pipeNetworkConnectionModels.stream().map(model -> new BaseVo(model.getOutflowId(), model.getOutflowLabel()))
                            .collect(Collectors.toList()));
                }
            }
            List<BaseVo> endPipeLine = new ArrayList<>();
            //总管和末端管道对应关系
            for (BaseVo baseVo : totalPipeLine) {
                BaseVo pipeLine = pipeLineList.stream().filter(baseVo1 -> Objects.equals(baseVo.getId(), baseVo1.getId()))
                        .findAny().orElse(new BaseVo());
                if (CollectionUtils.isNotEmpty(pipeLine.getChildren())) {
                    endPipeLine.addAll(pipeLine.getChildren());
                }

            }
            endPipeLine = endPipeLine.stream().distinct().collect(Collectors.toList());
            //系统和末端管道对应关系
            result.put(system.getId(), endPipeLine);

        }
        return result;
    }


    private void getEndPipeLine(List<BaseVo> pipeLineList, List<BaseVo> endPipeline) {
        if (CollectionUtils.isEmpty(pipeLineList)) {
            return;
        }
        for (BaseVo baseVo : pipeLineList) {
            if (CollectionUtils.isEmpty(baseVo.getChildren()) && !Objects.equals(baseVo.getModelLabel(), NodeLabelDef.ROOM)) {
                endPipeline.add(baseVo);
            } else {
                getEndPipeLine(baseVo.getChildren(), endPipeline);
            }
        }
    }

}