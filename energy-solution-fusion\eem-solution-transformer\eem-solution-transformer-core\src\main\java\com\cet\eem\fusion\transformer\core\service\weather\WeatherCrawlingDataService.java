package com.cet.eem.fusion.transformer.core.service.weather;

import com.cet.eem.bll.common.model.domain.subject.energysaving.WeatherPredict;
import com.cet.eem.bll.energysaving.model.weather.CurrentWeather;
import com.cet.eem.bll.energysaving.model.weather.ForecastBasicWeatherDataVo;
import com.cet.eem.bll.energysaving.model.weather.ForecastBasicWeatherQueryVo;
import com.cet.eem.bll.energysaving.model.weather.QueryParam;
import com.cet.eem.common.model.BaseVo;

import java.util.List;

/**
 * @ClassName : WeatherCrawlingService
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-14 09:52
 */
public interface WeatherCrawlingDataService {
    /**
     * 查询当前天气
     * @return
     */
    CurrentWeather queryWeatherCurrentData(QueryParam queryParam, Long projectId);

    /**
     * 写入经过算法计算过的天气预测数据
     * @param dataList
     */
    void writeWeatherPredict(List<WeatherPredict> dataList);

    /**
     * 查询天气数据，包括基础的预测，历史和通过实测值
     * @param query
     * @return
     * @throws Exception
     */
    List<ForecastBasicWeatherDataVo> queryWeather(ForecastBasicWeatherQueryVo query, Long project) throws Exception;

    /**
     * 查询气象监测仪节点
     * @return
     */
    List<BaseVo> queryNodesByMonitor(Long projectId);
}
