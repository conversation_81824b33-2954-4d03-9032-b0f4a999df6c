package com.cet.eem.fusion.transformer.core.service.trend.impl;

import com.cet.eem.bll.common.dao.node.EnergySupplyDao;
import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.bll.common.dao.pipenetwork.PipeNetworkConnectionModelDao;
import com.cet.eem.bll.common.dao.project.ProjectDao;
import com.cet.eem.bll.common.def.quantity.FrequencyDef;
import com.cet.eem.bll.common.def.quantity.PhasorDef;
import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;
import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;
import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;
import com.cet.eem.bll.common.model.domain.object.organization.City;
import com.cet.eem.bll.common.model.domain.object.organization.Project;
import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumption;
import com.cet.eem.bll.common.model.domain.subject.energysaving.ColdActual;
import com.cet.eem.bll.common.model.domain.subject.energysaving.ColdPredict;
import com.cet.eem.bll.common.model.domain.subject.energysaving.RefrigeratingRunningStrategy;
import com.cet.eem.bll.common.model.node.relations.EnergySupplyToPo;
import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.bll.energy.dao.consumption.EnergyConsumptionDao;
import com.cet.eem.bll.energysaving.dao.colddata.ColdActualDao;
import com.cet.eem.bll.energysaving.dao.weather.ColdPredictDao;
import com.cet.eem.bll.energysaving.dao.weather.RefrigeratingRunningStrategyDao;
import com.cet.eem.bll.energysaving.dao.weather.WeatherPredictDao;
import com.cet.eem.bll.energysaving.model.config.PumpFunctionType;
import com.cet.eem.bll.energysaving.model.def.ColdOptimizationLabelDef;
import com.cet.eem.bll.energysaving.model.def.QuantityDef;
import com.cet.eem.bll.energysaving.model.weather.*;
import com.cet.eem.bll.energysaving.service.trend.ModelConfigurationService;
import com.cet.eem.bll.energysaving.service.trend.OperationTrendService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.EnergyTypeDef;
import com.cet.eem.common.constant.EnumDataTypeId;
import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.model.realtime.RealTimeValue;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.base.ConditionBlock;
import com.cet.eem.model.base.ConditionBlockCompose;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.base.SingleModelConditionDTO;
import com.cet.eem.model.feign.ModelDataService;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import com.cet.eem.model.tool.SubConditionBuilder;
import com.cet.eem.node.service.Topology1Service;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.eem.weather.dao.WeatherDao;
import com.cet.eem.weather.model.Weather;
import com.cet.eem.weather.model.WeatherValueType;
import com.cet.eem.weather.vo.QueryWeatherParam;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : OperationTrendServiceImpl
 * @Description : 运行趋势
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-15 16:59
 */
@Service
public class OperationTrendServiceImpl implements OperationTrendService {
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    QuantityManageService quantityManageService;
    @Autowired
    EnergyConsumptionDao energyConsumptionDao;
    @Autowired
    WeatherPredictDao weatherPredictDao;
    @Autowired
    ColdPredictDao coldPredictDao;
    @Autowired
    RefrigeratingRunningStrategyDao refrigeratingRunningStrategyDao;
    @Autowired
    EnergySupplyDao energySupplyDao;
    @Autowired
    ColdActualDao coldActualDao;
    @Autowired
    Topology1Service topology1Service;
    @Autowired
    ModelDataService modelService;
    @Autowired
    PipeNetworkConnectionModelDao connectionModelDao;
    @Autowired
    ModelConfigurationService modelConfigurationService;
    @Autowired
    NodeDao nodeDao;
    @Autowired
    ProjectDao projectDao;
    @Autowired
    WeatherDao weatherDao;
    public static final Integer FIFTEENMINUTE = 18;
    //gj转kw
    public static final Double UNIT = 0.0036D;

    /**
     * 冷冻水供回水温度,配置也是根据物理量读取的,冷水主机、总管直接关联表计
     * 暂时的处理方法是可以筛选查的是总管或者冷水主机的关联表计的6008008的测点数据
     *
     * @param queryParam
     * @return
     * @throws Exception
     */
    @Override
    public List<OperationTrendVo> queryCoolingWaterTrend(QueryParam queryParam) throws Exception {
        List<BaseVo> baseVos;
        List<BaseVo> mains = queryColdWaterMainEngine(queryParam, NodeLabelDef.COLD_WATER_MAINENGINE);
        //传0就是查总管
        if (Objects.equals(queryParam.getDeviceId(), 0L)) {
            List<PipeNetworkConnectionModel> connectionModels = connectionModelDao.
                    getTopOrDownBatchWithNodes(mains, false, GlobalInfoUtils.getProjectId());
            baseVos = connectionModels.stream().filter(model -> Objects.equals(model.getOutflowLabel(), NodeLabelDef.PIPELINE))
                    .map(model -> new BaseVo(model.getOutflowId(), model.getOutflowLabel()))
                    .collect(Collectors.toList());
            return queryDataTrend(baseVos, queryParam,
                    Arrays.asList(QuantityDef.getEndSupplyTemp(), QuantityDef.getEndReturnTemp(),
                            QuantityDef.getFreezingWaterPipelineForSupplyStandardQuantitySetting(),
                            QuantityDef.getFreezingWaterPipelineForReturnStandardQuantitySetting()), true);
        } else {
            //查询冷水主机关联表计的该数据
            baseVos = Collections.singletonList(new BaseVo(queryParam.getDeviceId(), queryParam.getDeviceLabel()));
            return queryDataTrend(baseVos, queryParam,
                    Arrays.asList(getFreezingWaterForSupplyQuantitySetting(), getFreezingWaterForReturnQuantitySetting(),
                            getFreezingWaterForSupplyStandardQuantitySetting(), getFreezingWaterForReturnStandardQuantitySetting()), true);
        }


    }

    /**
     * 查询定时任务中的内容
     */
    private List<OperationTrendVo> queryDataTrend(List<BaseVo> baseVos, QueryParam queryParam, List<QuantitySearchVo> quantitySearchVoList, Boolean isCold
    ) {
        Map<Integer, List<TrendDataVo>> integerListMap = quantityManageService.queryDataLogBatch(createQuantityDataSearchVo(baseVos, queryParam, quantitySearchVoList));
        // 取出设备数据
        List<TrendDataVo> supplyTempList;
        List<TrendDataVo> returnTempList;
        List<TrendDataVo> supplyTempSettingList;
        List<TrendDataVo> returnTempSettingList;
        if (Objects.nonNull(integerListMap)) {
            supplyTempList = integerListMap.get(quantitySearchVoList.get(0).getId());
            returnTempList = integerListMap.get(quantitySearchVoList.get(1).getId());
            supplyTempSettingList = integerListMap.get(quantitySearchVoList.get(2).getId());
            returnTempSettingList = integerListMap.get(quantitySearchVoList.get(3).getId());
        } else {
            return Collections.emptyList();
        }
        List<OperationTrendVo> operationTrendVoList = transData(supplyTempList, returnTempList, supplyTempSettingList, returnTempSettingList, isCold);
        return assembleOperationTrendVo(operationTrendVoList, queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle());

    }


    /**
     * 冷却水供回水温度，,冷水主机直接关联表计
     *
     * @param queryParam
     * @return
     * @throws Exception
     */
    @Override
    public List<OperationTrendVo> queryCoolingTowerTrend(QueryParam queryParam) throws Exception {
        List<BaseVo> baseVos = Collections.singletonList(new BaseVo(queryParam.getDeviceId(), queryParam.getDeviceLabel()));
        return queryDataTrend(baseVos, queryParam,
                Arrays.asList(getCoolingWaterForSupplyQuantitySetting(), getCoolingWaterForReturnQuantitySetting(), getCoolingWaterForSupplyStandaQuantitySetting()
                        , getCoolingWaterForReturnStandaQuantitySetting()), false);
    }

    /**
     * 系统制冷能效分析:1.系统总功率（定时记录dataid=2000004）：冷水机组、冷冻水泵、冷却水泵、冷却塔功率之和，通过各设备关联的开关柜或一段线关联的表计拿到数据；
     * 2.冷负荷：冷水主机中冷机负荷，取拓扑下端的管道关联的表计的6008008测点之和
     * 3.系统COPE：系统冷负荷/系统总功率
     *
     * @param queryParam
     * @return
     * @throws Exception
     */
    @Override
    public List<OperationTrendVo> querySystemCoolingWaterTrend(QueryParam queryParam, Long projectId) throws Exception {

        List<EnergySupplyToPo> energySupplyToPos = queryEnergySupplyToPo(queryParam, NodeLabelDef.COLD_WATER_MAINENGINE);
        List<OperationTrendVo> operationTrendVoList = querySingleDataLogWithDevices(energySupplyToPos, queryParam, getMainPowerQuantitySetting());
        List<EnergySupplyToPo> coolingWaterBaseVos = queryEnergySupplyToPo(queryParam, NodeLabelDef.COOLING_TOWER);
        List<OperationTrendVo> coolingWaterVoList = querySingleDataLogWithDevices(coolingWaterBaseVos, queryParam, getTowerPowerQuantitySetting());
        List<OperationTrendVo> operationTrendVoListOfPump1 = querySingleDataLogWithDevices(queryLinesegmentWithPump(queryParam, PumpFunctionType.REFRIGERATING_PUMP), queryParam, getFreezingPumpPowerQuantitySetting());
        List<OperationTrendVo> operationTrendVoListOfPump2 = querySingleDataLogWithDevices(queryLinesegmentWithPump(queryParam, PumpFunctionType.COOLING_PUMP), queryParam, getCoolingPumpPowerQuantitySetting());
        //冷负荷修改成末端加管损
        List<ColdActual> endAndLossColdActual = getEndAndLossColdActual(queryParam, projectId);
        return assembleSystemTrendData(operationTrendVoList, coolingWaterVoList, operationTrendVoListOfPump1, operationTrendVoListOfPump2, endAndLossColdActual);
    }

    @Override
    public OperationTrendVo querySystemCoolingWaterSingData(QueryParam queryParam) {
        //查询房间底下设备
        List<BaseVo> mains = queryDeviceOfRoom(queryParam, Collections.singletonList(NodeLabelDef.COLD_WATER_MAINENGINE));
        //查询关联的一段线
        List<EnergySupplyToPo> energySupplyToPos = energySupplyDao.queryEnergySupplyTo(mains, System.currentTimeMillis(), EnergySupplyToPo.class);
        List<EnergySupplyToPo> supplyToPos = energySupplyToPos.stream().filter(energySupplyToPo -> Objects.equals(NodeLabelDef.LINE_SEGMENT_WITH_SWITCH, energySupplyToPo.getObjectlabel()))
                .collect(Collectors.toList());
        List<PipeNetworkConnectionModel> connectionModels = connectionModelDao.getTopOrDownBatchWithNodes(mains, false, GlobalInfoUtils.getProjectId());
        List<BaseVo> pipeLines = connectionModels.stream().filter(model -> Objects.equals(model.getOutflowLabel(), NodeLabelDef.PIPELINE))
                .map(model -> new BaseVo(model.getOutflowId(), model.getOutflowLabel()))
                .collect(Collectors.toList());
        List<BaseVo> queryNodes = supplyToPos.stream().map(energySupplyToPo -> new BaseVo(energySupplyToPo.getObjectid(), energySupplyToPo.getObjectlabel())).collect(Collectors.toList());
        queryNodes.addAll(pipeLines);
        Map<Integer, List<RealTimeValue>> integerListMap = quantityManageService.queryRealTimeBath(
                quantityDataBatchSearchVo(queryNodes, queryParam, Arrays.asList(getMainPowerQuantitySetting(), QuantityDef.getFreezingWaterPipelineForStream())));
        return assemblePowerData(integerListMap, supplyToPos);
    }

    private OperationTrendVo assemblePowerData(Map<Integer, List<RealTimeValue>> integerListMap, List<EnergySupplyToPo> supplyToPos) {
        List<RealTimeValue> realTimeValues = integerListMap.get(getMainPowerQuantitySetting().getId());
        Map<BaseVo, List<EnergySupplyToPo>> baseVoListMap = supplyToPos.stream()
                .collect(Collectors.groupingBy(
                        energySupplyToPo -> new BaseVo(energySupplyToPo.getSupplytoid(), energySupplyToPo.getSupplytolabel())));
        // 取出设备数据
        Double aDouble = assembleSingleRealTimeDeviceData(baseVoListMap, realTimeValues);
        OperationTrendVo item = new OperationTrendVo();
        item.setTotalSystemPower(aDouble);
        List<RealTimeValue> dataVoList = integerListMap.get(QuantityDef.getFreezingWaterPipelineForStream().getId());
        List<RealTimeValue> collect = dataVoList.stream().filter(realTimeValue -> Objects.nonNull(realTimeValue.getValue()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return item;
        }
        double sum = collect.stream().mapToDouble(RealTimeValue::getValue).sum();
        if (sum <= 0.0) {
            item.setCoolingLoad(Math.abs(CommonUtils.calcDouble(Math.abs(sum) * 1.01, UNIT, EnumOperationType.DIVISION.getId())));
        }
        item.setDifference(CommonUtils.calcDouble(item.getCoolingLoad(), item.getTotalSystemPower(), EnumOperationType.DIVISION.getId()));
        return item;
    }

    @Override
    public Double querySystemCold(List<BaseVo> baseVos) {
        List<PipeNetworkConnectionModel> connectionModels = connectionModelDao.getTopOrDownBatchWithNodes(baseVos, false, GlobalInfoUtils.getProjectId());
        List<BaseVo> pipeLines = connectionModels.stream().filter(model -> Objects.equals(model.getOutflowLabel(), NodeLabelDef.PIPELINE))
                .map(model -> new BaseVo(model.getOutflowId(), model.getOutflowLabel()))
                .collect(Collectors.toList());
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime firstTimeOfHour = TimeUtil.getFirstTimeOfHour(now);
        return querySingleRealTimeAdd(pipeLines, new QueryParam(firstTimeOfHour, now), QuantityDef.getFreezingWaterPipelineForStream());
    }

    private Double querySingleRealTimeAdd(List<BaseVo> baseVos, QueryParam queryParam, QuantitySearchVo quantitySearchVo) {
        Map<Integer, List<RealTimeValue>> dataLogResult = quantityManageService.queryRealTimeBath(
                createQuantityDataBatchSearchVo(baseVos, queryParam, quantitySearchVo));
        if (Objects.isNull(dataLogResult)) {
            return null;
        }
        // 取出设备数据
        List<RealTimeValue> dataVoList = dataLogResult.get(quantitySearchVo.getId());
        List<RealTimeValue> collect = dataVoList.stream().filter(realTimeValue -> Objects.nonNull(realTimeValue.getValue()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return null;
        }
        double sum = collect.stream().mapToDouble(RealTimeValue::getValue).sum();
        return CommonUtils.calcDouble(sum, UNIT, EnumOperationType.DIVISION.getId());
    }

    private Double querySingleRealTime(List<BaseVo> baseVos, QueryParam queryParam, QuantitySearchVo quantitySearchVo) {
        Map<Integer, List<RealTimeValue>> dataLogResult = quantityManageService.queryRealTimeBath(
                createQuantityDataBatchSearchVo(baseVos, queryParam, quantitySearchVo));
        if (Objects.isNull(dataLogResult)) {
            return null;
        }
        // 取出设备数据
        List<RealTimeValue> dataVoList = dataLogResult.get(quantitySearchVo.getId());
        RealTimeValue realTimeValue1 = dataVoList.stream().filter(realTimeValue -> Objects.nonNull(realTimeValue.getValue())).findAny().orElse(new RealTimeValue());
        return realTimeValue1.getValue();
    }

    /**
     * 查询空调机房对应的设备，然后查询它关联的一段线或者开关柜
     * 需要考虑多个设备相加的情况（两个冷水主机的功率相加，这里需要返回）
     *
     * @param queryParam
     * @param type
     * @return
     */
    private List<EnergySupplyToPo> queryEnergySupplyToPo(QueryParam queryParam, String type) {
        QueryCondition builder = new QueryConditionBuilder<>(queryParam.getObjectLabel(), queryParam.getObjectId())
                .leftJoin(type).queryAsTree().build();
        List<BaseVo> query = modelServiceUtils.query(builder, BaseVo.class);
        List<BaseVo> typeNodes = new ArrayList<>();
        for (BaseVo baseVo : query) {
            if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                typeNodes.addAll(baseVo.getChildren());
            }
        }
        if (CollectionUtils.isEmpty(typeNodes)) {
            return Collections.emptyList();
        }
        List<EnergySupplyToPo> energySupplyToPos = energySupplyDao.queryEnergySupplyTo(typeNodes, System.currentTimeMillis(), EnergySupplyToPo.class);


        return energySupplyToPos.stream().filter(energySupplyToPo -> Objects.equals(NodeLabelDef.LINE_SEGMENT_WITH_SWITCH, energySupplyToPo.getObjectlabel()))
                .collect(Collectors.toList());
    }


    private List<EnergySupplyToPo> queryLinesegmentWithPump(QueryParam queryParam, Integer type) {
        List<SingleModelConditionDTO> subChildren = new ArrayList<>();
        SingleModelConditionDTO singleModelConditionDTO = new SubConditionBuilder(NodeLabelDef.PUMP).build();
        subChildren.add(singleModelConditionDTO);
        List<ConditionBlock> filters = new ArrayList<>();
        filters.add(new ConditionBlock(ColdOptimizationLabelDef.FUNCTION_TYPE, ConditionBlock.OPERATOR_EQ, type));
        singleModelConditionDTO.setFilter(new ConditionBlockCompose(filters));
        QueryCondition condition = new QueryConditionBuilder<>(queryParam.getObjectLabel(), queryParam.getObjectId())
                .leftJoinCondition(subChildren)
                .queryAsTree()
                .build();
        List<BaseVo> query = modelServiceUtils.query(condition, BaseVo.class);
        List<BaseVo> typeNodes = new ArrayList<>();
        for (BaseVo baseVo : query) {
            if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                typeNodes.addAll(baseVo.getChildren());

            }
        }
        if (CollectionUtils.isEmpty(typeNodes)) {
            return Collections.emptyList();
        }
        List<EnergySupplyToPo> energySupplyToPos = energySupplyDao.queryEnergySupplyTo(typeNodes, System.currentTimeMillis(), EnergySupplyToPo.class);
        return energySupplyToPos.stream().filter(energySupplyToPo -> Objects.equals(NodeLabelDef.LINE_SEGMENT_WITH_SWITCH, energySupplyToPo.getObjectlabel()))
                .collect(Collectors.toList());
    }

    /**
     * 查询各个设备的耗电数据
     *
     * @param queryParam
     * @return
     */
    @Override
    public EquipmentGroupConsumption queryEquipmentGroupConsumption(QueryParam queryParam) {
        List<BaseVo> baseVos = queryEquipmentGroupDevice(queryParam);
        if (CollectionUtils.isEmpty(baseVos)) {
            return new EquipmentGroupConsumption();
        }
        List<EnergyConsumption> consumptions = energyConsumptionDao.queryEnergyConsumption(baseVos, TimeUtil.localDateTime2timestamp(queryParam.getStartTime())
                , TimeUtil.localDateTime2timestamp(queryParam.getEndTime()), queryParam.getCycle(), Collections.singletonList(EnergyTypeDef.ELECTRIC));
        if (CollectionUtils.isEmpty(consumptions)) {
            return new EquipmentGroupConsumption();
        }
        Map<String, List<EnergyConsumption>> collect = consumptions.stream().collect(Collectors.groupingBy(EnergyConsumption::getObjectlabel));
        EquipmentGroupConsumption consumption = new EquipmentGroupConsumption();
        for (Map.Entry<String, List<EnergyConsumption>> entry : collect.entrySet()) {
            List<EnergyConsumption> value = entry.getValue();
            List<EnergyConsumption> nonNullData = value.stream().filter(energyConsumption -> Objects.nonNull(energyConsumption.getUsage())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(nonNullData)) {
                continue;
            }
            double sum = nonNullData.stream().mapToDouble(EnergyConsumption::getUsage).sum();
            if (Objects.equals(NodeLabelDef.COLD_WATER_MAINENGINE, entry.getKey())) {
                consumption.setColdWaterMainEngineConsumption(sum);
            } else if (Objects.equals(NodeLabelDef.COOLING_TOWER, entry.getKey())) {
                consumption.setCoolingTowerConsumption(sum);
            } else {
                List<BaseVo> baseVos1 = queryOtherPump(baseVos);
                calculatePump(baseVos1, nonNullData, consumption);
            }
        }
        calculatePercent(consumption);
        return consumption;
    }

    private List<BaseVo> queryNodesByMonitor(Long projectId) {
        QueryCondition condition = new QueryConditionBuilder<>(NodeLabelDef.PROJECT, projectId)
                .leftJoin(ColdOptimizationLabelDef.METEORO_LOGICAL_MONITOR).queryAsTree().build();
        List<BaseVo> query = modelServiceUtils.query(condition, BaseVo.class);
        List<BaseVo> result = new ArrayList<>();
        for (BaseVo baseVo : query) {
            if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                result.addAll(baseVo.getChildren());
            }
        }
        return result;
    }

    /**
     * 气象监测仪直接关联表计
     *
     * @param queryParam
     * @return
     * @throws Exception
     */
    @Override
    public List<AIPredictWeatherVo> queryAiPredictForTemp(QueryParam queryParam,Long projectId) throws Exception {
        List<BaseVo> baseVos = queryNodesByMonitor(projectId);
        List<OperationTrendVo> operationTrendVoList = querySingleDataLog(
                baseVos, queryParam, getAiPredictForTempQuantitySetting());
        List<Weather> weathers = queryWeather(queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle(), GlobalInfoUtils.getProjectId());
        return transToWeatherVo(operationTrendVoList, weathers);
    }

    @Override
    public List<Weather> queryWeather(LocalDateTime st, LocalDateTime et, Integer cycle, Long projectId) {
        QueryWeatherParam queryParam = new QueryWeatherParam();
        queryParam.setCodes(queryCityCode(projectId));
        queryParam.setStarttime(TimeUtil.localDateTime2timestamp(st));
        queryParam.setEndtime(TimeUtil.localDateTime2timestamp(et));
        queryParam.setAggregationcycle(cycle);
        queryParam.setType(WeatherValueType.PREDICT.getId());
        return weatherDao.queryWeather(queryParam).getData();
    }


    private List<Long> queryCityCode(Long projectId) {
        Project project = projectDao.selectById(projectId);
        List<City> cities = queryCity(Collections.singletonList(project.getCity()));
        return cities.stream().map(City::getCode).collect(Collectors.toList());
    }

    private List<City> queryCity(List<String> cityNames) {
        QueryCondition queryCondition = new QueryConditionBuilder<>(ModelLabelDef.CITY).in(ColumnDef.NAME, cityNames)
                .queryAsTree().build();
        List<Map<String, Object>> queryCitys = modelServiceUtils.query(queryCondition);
        return JsonTransferUtils.transferList(queryCitys, City.class);

    }

    @Override
    public List<AIPredictWeatherVo> queryAiPredictForHum(QueryParam queryParam,Long projectid) throws Exception {
        List<BaseVo> baseVos = queryNodesByMonitor(projectid);
        List<OperationTrendVo> operationTrendVoList = querySingleDataLog(
                baseVos, queryParam, getAiPredictForHumQuantitySetting());
        List<Weather> weathers = queryWeather(queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle(), GlobalInfoUtils.getProjectId());
        return transToWeatherVoForHum(operationTrendVoList, weathers);
    }

    private void setTimeParam(QueryParam queryFact, QueryParam queryPredict, long l, QueryParam queryParam) {
        if (l < TimeUtil.localDateTime2timestamp(queryParam.getStartTime())) {
            //未来时间
            BeanUtils.copyProperties(queryParam, queryPredict);
            return;
        }
        BeanUtils.copyProperties(queryParam, queryFact);
        BeanUtils.copyProperties(queryParam, queryPredict);
        queryFact.setEndTime(TimeUtil.timestamp2LocalDateTime(l));
        LocalDateTime localDateTime = TimeUtil.timestamp2LocalDateTime(l);
        LocalDateTime predict = TimeUtil.addDateTimeByCycle(localDateTime, queryParam.getCycle(), -1);
        queryPredict.setStartTime(predict);
    }

    /**
     * 温湿度实时值通过气象监测仪的表计，预测值来源weatherpredict，总冷负荷实际值来源于末端冷量加管损转存结果
     * 总冷负荷预测值来源于末端冷量加管损预测值相加
     * 末端冷量来源于冷水主机拓扑下端关联的总管的拓扑下端支管6008008数据求和
     *
     * @param queryParam
     * @return
     * @throws Exception
     */
    @Override
    public List<AIPredictWeatherWithEnergyVo> queryAiPredictWithEnergyForTemp(QueryParam queryParam, Long projectId) throws Exception {
        long l = System.currentTimeMillis();
        List<AIPredictWeatherWithEnergyVo> result = checkIfFuture(queryParam, l);
        if (CollectionUtils.isNotEmpty(result)) {
            return result;
        }
        QueryParam queryFact = new QueryParam();
        QueryParam queryPredict = new QueryParam();
        setTimeParam(queryFact, queryPredict, l, queryParam);
        List<OperationTrendVo> operationTrendVoList = new ArrayList<>();
        if (Objects.nonNull(queryFact.getStartTime())) {
            operationTrendVoList = querySingleDataLogWithOut(
                    queryNodesByMonitor(projectId), queryFact, getAiPredictForTempQuantitySetting());
        }
        List<Weather> weathers = queryWeather(queryPredict.getStartTime(), queryPredict.getEndTime(), queryParam.getCycle(), projectId);
        List<ColdActual> endActuals = queryEndCold(queryFact, projectId);
        List<ColdPredict> coldPredicts = getPredictColdLoadData(setPredictStartTime(l, queryParam.getCycle()), queryPredict.getEndTime(), queryParam.getCycle(), queryParam.getObjectId());
        List<AIPredictWeatherWithEnergyVo> aiPredictWeatherWithEnergyVos = assembleAIPredictWeatherWithEnergyVo(operationTrendVoList, weathers
                , endActuals, coldPredicts, queryParam, true);
        changePredictStartValue(aiPredictWeatherWithEnergyVos, l, queryParam.getCycle());
        transFactAndPredictToOne(aiPredictWeatherWithEnergyVos, l);
        return aiPredictWeatherWithEnergyVos;
    }

    private List<AIPredictWeatherWithEnergyVo> checkIfFuture(QueryParam queryParam, long l) {
        long st = TimeUtil.localDateTime2timestamp(queryParam.getStartTime());
        if (l < st) {
            List<AIPredictWeatherWithEnergyVo> timeValues = new ArrayList<>();
            List<Long> timeRange = TimeUtil.getTimeRange(st, TimeUtil.localDateTime2timestamp(queryParam.getEndTime()), queryParam.getCycle());
            for (Long time : timeRange) {
                AIPredictWeatherWithEnergyVo aiPredictWeatherWithEnergyVo = new AIPredictWeatherWithEnergyVo(time);
                timeValues.add(aiPredictWeatherWithEnergyVo);
            }
            return timeValues;
        }
        return Collections.emptyList();
    }

    private LocalDateTime setPredictStartTime(long l, Integer cycle) {
        LocalDateTime now = TimeUtil.timestamp2LocalDateTime(l);
        LocalDateTime firstTimeOfHour = TimeUtil.getFirstTimeOfHour(now);
        List<LocalDateTime> timeRangeForFifteen = TimeUtil.getTimeRange(firstTimeOfHour, TimeUtil.addDateTimeByCycle(firstTimeOfHour, AggregationCycle.ONE_HOUR, 2), cycle);
        for (LocalDateTime time : timeRangeForFifteen) {
            if (Boolean.TRUE.equals(time.isAfter(now))) {
                return time;
            }

        }
        return null;
    }

    /**
     * 处理冷负荷数据，负值取绝对值，正值赋值null
     *
     * @param aiPredictWeatherWithEnergyVos
     */
    private void handleColdData(List<AIPredictWeatherWithEnergyVo> aiPredictWeatherWithEnergyVos) {
        for (AIPredictWeatherWithEnergyVo energyVo : aiPredictWeatherWithEnergyVos) {
            energyVo.setCoolingLoadFactValue(handleDoubleData(energyVo.getCoolingLoadFactValue()));
            energyVo.setCoolingLoadPredictValue(handleDoubleData(energyVo.getCoolingLoadPredictValue()));

        }
    }

    private Map<Integer, List<ColdActual>> getFactColdLoadData(QueryParam queryFact, QueryParam queryParam, Long projectId) throws InstantiationException, IllegalAccessException {
        List<ColdActual> totalActuals = coldActualDao.queryColdActualData(Collections.singletonList(ColdLoadType.TOTAL), Collections.singletonList(PredictDataType.LOSS),
                getTimeRangeForFifteen(queryFact.getStartTime(), queryFact.getEndTime(), queryFact.getCycle()), Collections.singletonList(queryParam.getObjectId()));
        List<ColdActual> endActuals = queryEndCold(queryFact, projectId);
        Map<Integer, List<ColdActual>> map = new HashMap<>();
        map.put(ColdLoadType.END, endActuals);
        map.put(ColdLoadType.TOTAL, totalActuals);
        return map;
    }

    private void getEndPipeLine(List<BaseVo> pipeLineList, List<BaseVo> endPipeline) {
        for (BaseVo baseVo : pipeLineList) {
            if (CollectionUtils.isEmpty(baseVo.getChildren())) {
                endPipeline.add(baseVo);
            } else {
                getEndPipeLine(baseVo.getChildren(), endPipeline);
            }
        }
    }

    /**
     * 末端冷量实际值变更为末端管道关联的6008008测点
     *
     * @param queryFact
     * @return
     */
    @Override
    public  List<ColdActual> queryEndCold(QueryParam queryFact, Long projectId) throws IllegalAccessException, InstantiationException {
        List<BaseVo> baseVos = queryDeviceOfRoom(queryFact, Collections.singletonList(NodeLabelDef.COLD_WATER_MAINENGINE));
        // 查询制冷房间关联的管道
        List<BaseVo> pipeLineList = topology1Service.queryFlowNode(projectId, EnergyTypeDef.COLD, baseVos, NodeLabelDef.PIPELINE);
        List<BaseVo> endPipeline = new ArrayList<>();
        getEndPipeLine(pipeLineList, endPipeline);
        Map<Integer, List<TrendDataVo>> dataLogResult = quantityManageService.queryDataLogBatch(
                createQuantityDataBatchSearchVo(endPipeline, queryFact, QuantityDef.getFreezingWaterPipelineForStream()));
        List<ColdActual> result = new ArrayList<>();
        // 取出设备数据
        List<TrendDataVo> dataVoList = dataLogResult.get(QuantityDef.getFreezingWaterPipelineForStream().getId());
        if (Objects.isNull(dataVoList)) {
            return Collections.emptyList();
        }
        Map<BaseVo, List<TrendDataVo>> map = dataVoList.stream().
                collect(Collectors.groupingBy(trendDataVo -> new BaseVo(trendDataVo.getMonitoredid(), trendDataVo.getMonitoredlabel())));
        List<DatalogValue> datalogValue = new ArrayList<>();
        for (Map.Entry<BaseVo, List<TrendDataVo>> entryMap : map.entrySet()) {
            List<TrendDataVo> value = entryMap.getValue();
            //过滤出属于某个设备的采集设备的数据，再取第一个有值的，然后把这个类型的值全部加起来
            List<DatalogValue> dataList = getNonValueDataLog(value);
            datalogValue.addAll(dataList);
        }
        Map<Long, List<DatalogValue>> value = datalogValue.stream().filter(DatalogValue1 -> Objects.nonNull(DatalogValue1.getValue()))
                .collect(Collectors.groupingBy(DatalogValue::getTime));
        for (Map.Entry<Long, List<DatalogValue>> entry : value.entrySet()) {
            Double sum = entry.getValue().stream().mapToDouble(DatalogValue::getValue).sum();
            ColdActual coldActual = new ColdActual();
            coldActual.setLogTime(TimeUtil.timestamp2LocalDateTime(entry.getKey()));
            coldActual.setValue(CommonUtils.calcDouble(sum, UNIT, EnumOperationType.DIVISION.getId()));
            result.add(coldActual);
        }
        return result;
    }

    private List<ColdPredict> getPredictColdLoadData(LocalDateTime st, LocalDateTime et, Integer cycle, Long roomId) {
        List<ColdPredict> result = coldPredictDao.queryColdPredictData(Collections.singletonList(ColdLoadType.TOTAL), Collections.singletonList(PredictDataType.COLD_LOAD),
                st, et, roomId);
        List<ColdPredict> data = new ArrayList<>();

        List<Long> timeRangeForFifteen = getTimeRangeForFifteen(st, et, cycle);
        for (Long time : timeRangeForFifteen) {
            ColdPredict coldPredict2 = result.stream().filter(coldPredict -> Objects.equals(time, TimeUtil.localDateTime2timestamp(coldPredict.getLogTime()))).findAny().orElse(new ColdPredict());
            ColdPredict coldPredict = new ColdPredict();
            coldPredict.setLogTime(TimeUtil.timestamp2LocalDateTime(time));
            coldPredict.setValue(coldPredict2.getValue());
            data.add(coldPredict);
        }
        return data;
    }

    /**
     * 按时间节点，为了平滑的曲线，返回值只需要两个字段
     *
     * @param result
     * @param l
     */
    private void transFactAndPredictToOne(List<AIPredictWeatherWithEnergyVo> result, long l) {
        for (AIPredictWeatherWithEnergyVo it : result) {
            if (it.getLogtime() >= l) {
                it.setCoolingLoadFactValue(it.getCoolingLoadPredictValue());
                it.setWeatherFactValue(it.getWeatherPredictValue());

            }
        }
    }

    /**
     * 当前时间节点之间，比如现在是6.04，那么把6.00的预测值赋值成实际值
     *
     * @param result
     * @param l
     */
    private void changePredictStartValue(List<AIPredictWeatherWithEnergyVo> result, long l, Integer cycle) {
        for (AIPredictWeatherWithEnergyVo it : result) {
            if (it.getLogtime() <= l) {
                LocalDateTime localDateTime = TimeUtil.timestamp2LocalDateTime(it.getLogtime());
                LocalDateTime predict = TimeUtil.addDateTimeByCycle(localDateTime, cycle, 1);
                long timestamp = TimeUtil.localDateTime2timestamp(predict);
                if (timestamp > l) {
                    it.setCoolingLoadPredictValue(it.getCoolingLoadFactValue());
                    it.setWeatherPredictValue(it.getWeatherFactValue());
                    return;
                }
            }
        }
    }

    /**
     * 温湿度实时值通过气象监测仪的表计，预测值来源weatherpredict，总冷负荷实际值来源于末端冷量加管损转存结果
     * 总冷负荷预测值来源于末端冷量加管损预测值相加
     * 末端冷量来源于冷水主机拓扑下端关联的总管的拓扑下端支管6008008数据求和
     *
     * @param queryParam
     * @return
     * @throws Exception
     */
    @Override
    public List<AIPredictWeatherWithEnergyVo> queryAiPredictWithEnergyForHum(QueryParam queryParam, Long projectId) throws Exception {
        long l = System.currentTimeMillis();
        List<AIPredictWeatherWithEnergyVo> result = checkIfFuture(queryParam, l);
        if (CollectionUtils.isNotEmpty(result)) {
            return result;
        }
        QueryParam queryFact = new QueryParam();
        QueryParam queryPredict = new QueryParam();
        setTimeParam(queryFact, queryPredict, l, queryParam);
        List<OperationTrendVo> operationTrendVoList = new ArrayList<>();
        if (Objects.nonNull(queryFact.getStartTime())) {
            operationTrendVoList = querySingleDataLogWithOut(
                    queryNodesByMonitor(projectId), queryFact, getAiPredictForHumQuantitySetting());
        }
        List<Weather> weathers = queryWeather(queryPredict.getStartTime(), queryPredict.getEndTime(), queryParam.getCycle(), projectId);
        List<ColdActual> endActuals = queryEndCold(queryFact, projectId);
        List<ColdPredict> coldPredicts = getPredictColdLoadData(setPredictStartTime(l, queryParam.getCycle()), queryPredict.getEndTime(), queryParam.getCycle(), queryParam.getObjectId());
        List<AIPredictWeatherWithEnergyVo> aiPredictWeatherWithEnergyVos = assembleAIPredictWeatherWithEnergyVo(operationTrendVoList, weathers
                , endActuals, coldPredicts, queryParam, false);
        changePredictStartValue(aiPredictWeatherWithEnergyVos, l, queryParam.getCycle());
        transFactAndPredictToOne(aiPredictWeatherWithEnergyVos, l);
        return aiPredictWeatherWithEnergyVos;
    }

    /**
     * 冷水机组、冷冻水泵、冷却水泵、冷却塔功率之和，取自定时记录--修改成冷机功率
     *
     * @param queryParam
     * @return
     * @throws Exception
     */
    @Override
    public List<AIPredictWeatherVo> queryAiPredictWithEnergy(QueryParam queryParam) throws Exception {
        List<EnergySupplyToPo> energySupplyToPos = queryEnergySupplyToPo(queryParam, NodeLabelDef.COLD_WATER_MAINENGINE);
        List<OperationTrendVo> operationTrendVoList = querySingleDataLogWithDevices(energySupplyToPos, queryParam, getMainPowerQuantitySetting());
        List<ColdPredict> coldPredicts = coldPredictDao.querySingleDeviceData(queryParam.getStartTime(), queryParam.getEndTime(), ColdLoadType.TOTAL, PredictDataType.POWER, queryParam.getObjectId(), GlobalInfoUtils.getProjectId());
        return assembleSystemPowerTrendData(operationTrendVoList, coldPredicts);

    }

    /**
     * 系统COP（RT/ kW）（=系统冷却负荷（冷水主机）/系统总功率）
     * 实际值来源于冷负荷和功率实际值做除法，预测值来源于末端加管损的预测值求和除以功率预测值
     * 修改成总管冷负荷=末端加管损/冷机总功率
     *
     * @param queryParam
     * @return
     * @throws Exception
     */
    @Override
    public List<AIPredictWeatherVo> queryAiPredictWithEnergyCop(QueryParam queryParam, Long projectId) throws Exception {
        List<OperationTrendVo> operationTrendVoList = querySystemCoolingWaterTrendOnlyMain(queryParam, projectId);

        List<ColdPredict> coldPredicts = getPredictCopData(queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle(), queryParam.getObjectId());
        List<AIPredictWeatherVo> result = new ArrayList<>();
        for (OperationTrendVo operationTrendVo : operationTrendVoList) {
            AIPredictWeatherVo item = new AIPredictWeatherVo();
            item.setFactValue(CommonUtils.calcDouble(operationTrendVo.getCoolingLoad(), operationTrendVo.getTotalSystemPower(), EnumOperationType.DIVISION.getId()));
            item.setLogtime(operationTrendVo.getTime());
            List<ColdPredict> predicts = coldPredicts.stream().filter(coldPredict -> Objects.equals(TimeUtil.localDateTime2timestamp(coldPredict.getLogTime()), operationTrendVo.getTime())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(predicts)) {
                item.setPredictValue(predicts.get(0).getValue());
            }
            item.setDifference(calcAbsDouble(item.getPredictValue(), item.getFactValue()));
            result.add(item);
        }
        return result;
    }

    /**
     * 冷机功率和总冷负荷
     * @param queryParam
     * @param projectId
     * @return
     * @throws Exception
     */
    private List<OperationTrendVo> querySystemCoolingWaterTrendOnlyMain(QueryParam queryParam, Long projectId) throws Exception {

        List<EnergySupplyToPo> energySupplyToPos = queryEnergySupplyToPo(queryParam, NodeLabelDef.COLD_WATER_MAINENGINE);
        List<OperationTrendVo> operationTrendVoList = querySingleDataLogWithDevices(energySupplyToPos, queryParam, getMainPowerQuantitySetting());
        //冷负荷修改成末端加管损
        List<ColdActual> endAndLossColdActual = getEndAndLossColdActual(queryParam, projectId);
        List<OperationTrendVo> result = new ArrayList<>();
        for (int i = 0; i < operationTrendVoList.size(); i++) {
            OperationTrendVo item = new OperationTrendVo();
            item.setCoolingLoad((endAndLossColdActual.get(i).getValue()));
            item.setTotalSystemPower(operationTrendVoList.get(i).getValue());
            item.setTime(operationTrendVoList.get(i).getTime());
            item.setDifference(CommonUtils.calcDouble(item.getCoolingLoad(), item.getTotalSystemPower(), EnumOperationType.DIVISION.getId()));
            result.add(item);
        }
        return result;
    }

    private List<ColdPredict> getPredictCopData(LocalDateTime st, LocalDateTime et, Integer cycle, Long roomId) {
        List<ColdPredict> result = coldPredictDao.queryColdPredictData(Collections.singletonList(ColdLoadType.TOTAL),
                Arrays.asList(PredictDataType.COLD_LOAD, PredictDataType.POWER),
                st, et, roomId);
        List<ColdPredict> endActuals = result.stream().filter(coldActual -> Objects.equals(coldActual.getColdLoadType(), ColdLoadType.TOTAL)
                && Objects.equals(coldActual.getPredictDataType(),
                PredictDataType.COLD_LOAD)).collect(Collectors.toList());
        List<ColdPredict> power = result.stream().filter(coldActual -> Objects.equals(coldActual.getColdLoadType(), ColdLoadType.TOTAL) && Objects.equals(coldActual.getPredictDataType(),
                PredictDataType.POWER)).collect(Collectors.toList());
        List<ColdPredict> data = new ArrayList<>();
        List<Long> timeRangeForFifteen = getTimeRangeForFifteen(st, et, cycle);
        for (Long time : timeRangeForFifteen) {
            ColdPredict coldPredict1 = endActuals.stream().filter(coldPredict -> Objects.equals(time, TimeUtil.localDateTime2timestamp(coldPredict.getLogTime()))).findAny().orElse(new ColdPredict());
            ColdPredict coldPredict3 = power.stream().filter(coldPredict -> Objects.equals(time, TimeUtil.localDateTime2timestamp(coldPredict.getLogTime()))).findAny().orElse(new ColdPredict());
            ColdPredict coldPredict = new ColdPredict();
            coldPredict.setLogTime(TimeUtil.timestamp2LocalDateTime(time));
            coldPredict.setValue(CommonUtils.calcDouble((coldPredict1.getValue()), coldPredict3.getValue(), EnumOperationType.DIVISION.getId()));
            data.add(coldPredict);
        }
        return data;
    }

    public List<BaseVo> queryDeviceOfRoom(QueryParam queryParam, List<String> labels) {
        QueryCondition condition = new QueryConditionBuilder<>(queryParam.getObjectLabel(), queryParam.getObjectId())
                .leftJoin(labels)
                .queryAsTree()
                .build();
        List<Map<String, Object>> query = modelServiceUtils.query(condition);

        List<BaseVo> baseVos = JsonTransferUtils.transferList(query, BaseVo.class);
        List<BaseVo> result = new ArrayList<>();
        for (BaseVo baseVo : baseVos) {
            if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                result.addAll(baseVo.getChildren());
            }
        }
        return result;
    }

    @Override
    public List<AiPredictWithTime> queryAiPredictWithTime(QueryParam queryParam) {
        //查询预测的数据
        List<RefrigeratingRunningStrategy> refrigeratingRunningStrategies = refrigeratingRunningStrategyDao.queryStrategy(queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle(), queryParam.getObjectId(), null);
        return assembleStartAndEndTimeValue(refrigeratingRunningStrategies, queryParam);
    }

    /**
     * 冷水主机拓扑下端冷量管道的拓扑下端支管6008008求和，预测值来源于末端预测值
     *
     * @param queryParam
     * @return
     * @throws InstantiationException
     * @throws IllegalAccessException
     */
    @Override
    public List<AIPredictWeatherVo> queryEndColdTrend(QueryParam queryParam, Long projectId) throws InstantiationException, IllegalAccessException {
        List<Long> timeRangeForFifteen = getTimeRangeForFifteen(queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle());
        List<ColdActual> coldActuals = queryEndCold(queryParam, projectId);
        List<ColdPredict> coldPredicts = coldPredictDao.querySingleDeviceData(queryParam.getStartTime(), queryParam.getEndTime(), ColdLoadType.END, PredictDataType.COLD_LOAD, queryParam.getObjectId(),
                GlobalInfoUtils.getProjectId());

        return assembleTimeValue(coldActuals, coldPredicts, timeRangeForFifteen, true);
    }

    /**
     * 实际值来源于转存，预测值直接取
     *
     * @param queryParam
     * @return
     */
    @Override
    public List<AIPredictWeatherVo> queryFreezingPipeline(QueryParam queryParam, Long projectId) throws IllegalAccessException, InstantiationException {
        List<AIPredictWeatherVo> aiPredictWeatherVos = queryEndColdTrend(queryParam, projectId);
        for (AIPredictWeatherVo vo : aiPredictWeatherVos) {
            if (Objects.nonNull(vo.getFactValue())) {
                vo.setFactValue(vo.getFactValue() * 0.01);
            }
            if (Objects.nonNull(vo.getPredictValue())) {
                vo.setPredictValue(vo.getPredictValue() * 0.01);
            }
        }
        return aiPredictWeatherVos;
    }

    /**
     * 和前面总冷负荷预测值和温湿度逻辑一样
     *
     * @param queryParam
     * @return
     * @throws InstantiationException
     * @throws IllegalAccessException
     */
    @Override
    public List<AIPredictWeatherVo> queryTotalColdLoadingTrend(QueryParam queryParam, Long projectId) throws InstantiationException, IllegalAccessException {

        List<Long> timeRangeForFifteen = getTimeRangeForFifteen(queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle());
        List<ColdActual> endActuals = queryEndCold(queryParam, projectId);

        List<ColdPredict> predicts = getPredictColdLoadData(queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle(), queryParam.getObjectId());
        return assembleTimeValueWithLoss(endActuals, predicts, timeRangeForFifteen);
    }

    private List<ColdActual> getEndAndLossColdActual(QueryParam queryParam, Long projectId) throws InstantiationException, IllegalAccessException {
        List<ColdActual> result = new ArrayList<>();
        List<Long> timeRangeForFifteen = getTimeRangeForFifteen(queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle());
        List<ColdActual> endActuals = queryEndCold(queryParam, projectId);
        for (Long time : timeRangeForFifteen) {
            ColdActual coldActual = new ColdActual();
            coldActual.setLogTime(TimeUtil.timestamp2LocalDateTime(time));

            List<ColdActual> endActual = endActuals.stream().filter(it -> Objects.equals(TimeUtil.localDateTime2timestamp(it.getLogTime()), time)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(endActual)) {
                Double data = handleDoubleData(endActual.get(0).getValue());
                if (Objects.nonNull(data)){
                    coldActual.setValue(data * 1.01);
                }

            }
            result.add(coldActual);
        }
        return result;
    }

    @Override
    public Double getRealTimeColdLoad(QueryParam queryParam, List<BaseVo> pipeLines) {

        return querySingleRealTime(pipeLines, queryParam, getMainCoolingLoadQuantitySetting());

    }

    @Override
    public List<BaseVo> getMainEngineAfterPipe(QueryParam queryParam) {
        List<BaseVo> baseVos = queryColdWaterMainEngine(queryParam, NodeLabelDef.COLD_WATER_MAINENGINE);
        List<PipeNetworkConnectionModel> connectionModels = connectionModelDao.getTopOrDownBatchWithNodes(baseVos, false, GlobalInfoUtils.getProjectId());
        return connectionModels.stream().filter(model -> Objects.equals(model.getOutflowLabel(), NodeLabelDef.PIPELINE))
                .map(model -> new BaseVo(model.getOutflowId(), model.getOutflowLabel()))
                .collect(Collectors.toList());
    }

    private List<AIPredictWeatherVo> assembleTimeValue(List<ColdActual> coldActuals, List<ColdPredict> coldPredicts, List<Long> timeRangeForFifteen, Boolean isEnd) {
        List<AIPredictWeatherVo> timeValues = new ArrayList<>();
        for (Long time : timeRangeForFifteen) {
            AIPredictWeatherVo aiPredictWeatherVo = new AIPredictWeatherVo();
            aiPredictWeatherVo.setLogtime(time);
            timeValues.add(aiPredictWeatherVo);
            if (CollectionUtils.isEmpty(coldActuals) && CollectionUtils.isEmpty(coldPredicts)) {
                continue;
            }

            List<ColdActual> actual = coldActuals.stream().filter(it -> Objects.equals(TimeUtil.localDateTime2timestamp(it.getLogTime()), time)).collect(Collectors.toList());
            List<ColdPredict> predict = coldPredicts.stream().filter(it -> Objects.equals(TimeUtil.localDateTime2timestamp(it.getLogTime()), time)).collect(Collectors.toList());
            handleAIPredictWeatherVo(isEnd, actual, predict, aiPredictWeatherVo);
            aiPredictWeatherVo.setDifference(calcAbsDouble(aiPredictWeatherVo.getPredictValue(), aiPredictWeatherVo.getFactValue()));
        }
        return timeValues;

    }

    private void handleAIPredictWeatherVo(Boolean isEnd, List<ColdActual> actual, List<ColdPredict> predict, AIPredictWeatherVo aiPredictWeatherVo) {
        if (Boolean.TRUE.equals(isEnd)) {
            if (CollectionUtils.isNotEmpty(actual)) {
                aiPredictWeatherVo.setFactValue(handleDoubleData(actual.get(0).getValue()));
            }
            if (CollectionUtils.isNotEmpty(predict)) {

                aiPredictWeatherVo.setPredictValue((predict.get(0).getValue()));
            }
        } else {
            if (CollectionUtils.isNotEmpty(actual)) {
                aiPredictWeatherVo.setFactValue(handleLossData(actual.get(0).getValue()));
            }
            if (CollectionUtils.isNotEmpty(predict)) {

                aiPredictWeatherVo.setPredictValue((predict.get(0).getValue()));
            }
        }
    }

    /**
     * 处理冷量数据
     *
     * @param value
     * @return
     */
    @Override
    public Double handleDoubleData(Double value) {
        if (Objects.nonNull(value) && value <= 0.0) {
            value = Math.abs(value);
        } else {
            value = null;
        }
        return value;
    }

    /**
     * 处理管损数据
     *
     * @param value
     * @return
     */
    @Override
    public Double handleLossData(Double value) {
        if (Objects.nonNull(value) && value <= 0.0) {
            value = 0.0;
        }
        return value;
    }

    private List<AIPredictWeatherVo> assembleTimeValueWithLoss(List<ColdActual> endActuals, List<ColdPredict> coldPredicts, List<Long> timeRangeForFifteen) {
        List<AIPredictWeatherVo> timeValues = new ArrayList<>();
        for (Long time : timeRangeForFifteen) {
            AIPredictWeatherVo aiPredictWeatherVo = new AIPredictWeatherVo();
            aiPredictWeatherVo.setLogtime(time);
            timeValues.add(aiPredictWeatherVo);
            if (CollectionUtils.isEmpty(endActuals) && CollectionUtils.isEmpty(coldPredicts)) {
                continue;
            }

            List<ColdActual> endActual = endActuals.stream().filter(it -> Objects.equals(TimeUtil.localDateTime2timestamp(it.getLogTime()), time)).collect(Collectors.toList());

            List<ColdPredict> predict = coldPredicts.stream().filter(it -> Objects.equals(TimeUtil.localDateTime2timestamp(it.getLogTime()), time)).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(endActual)) {
                Double data = handleDoubleData(endActual.get(0).getValue());
                if (Objects.nonNull(data)){
                    aiPredictWeatherVo.setFactValue(data * 1.01);
                }
            }
            if (CollectionUtils.isNotEmpty(predict)) {
                aiPredictWeatherVo.setPredictValue((predict.get(0).getValue()));
            }
            aiPredictWeatherVo.setDifference(calcAbsDouble(aiPredictWeatherVo.getPredictValue(), aiPredictWeatherVo.getFactValue()));
        }
        return timeValues;

    }

    private Double addTwoValue(List<ColdActual> endActual, List<ColdActual> totalActual) {
        if (CollectionUtils.isEmpty(endActual) && CollectionUtils.isEmpty(totalActual)) {
            return null;
        }
        if (CollectionUtils.isEmpty(endActual)) {
            return handleLossData(totalActual.get(0).getValue());
        }
        if (CollectionUtils.isEmpty(totalActual)) {
            return handleDoubleData(endActual.get(0).getValue());
        }
        return addTwoData(handleLossData(totalActual.get(0).getValue()), handleDoubleData(endActual.get(0).getValue()));
    }

    @Override
    public Double addTwoValue(ColdPredict endActual, ColdPredict totalActual) {
        if (Objects.isNull(endActual.getValue())) {
            return totalActual.getValue();
        }
        if (Objects.isNull(totalActual.getValue())) {
            return endActual.getValue();
        }

        return totalActual.getValue() + endActual.getValue();
    }

    private Double addTwoData(Double value, Double value1) {
        if (Objects.isNull(value)) {
            return value1;
        }
        if (Objects.isNull(value1)) {
            return value;
        }

        return value + value1;
    }

    private List<AiPredictWithTime> assembleStartAndEndTimeValue(List<RefrigeratingRunningStrategy> refrigeratingRunningStrategies, QueryParam queryParam) {
        List<LocalDateTime> timeRange = TimeUtil.getTimeRange(queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle());
        List<AiPredictWithTime> result = new ArrayList<>();
        for (LocalDateTime time : timeRange) {
            AiPredictWithTime aiPredictWithTime = new AiPredictWithTime();
            aiPredictWithTime.setLogtime(TimeUtil.localDateTime2timestamp(time));
            if (CollectionUtils.isEmpty(refrigeratingRunningStrategies)) {
                result.add(aiPredictWithTime);
                continue;
            }

            List<RefrigeratingRunningStrategy> collect = refrigeratingRunningStrategies.stream().filter(it -> it.getLogTime().equals(time)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                result.add(aiPredictWithTime);
            } else {
                List<RefrigeratingRunningStrategy> start = collect.stream().filter(refrigeratingRunningStrategy -> Objects.equals(DeviceOperationType.START, refrigeratingRunningStrategy.getDeviceOperationType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(start)) {
                    aiPredictWithTime.setPredictStartTime(start.get(0).getPredictAdvanceTime() / (TimeUtil.MINUTE / TimeUtil.SECOND));
                    aiPredictWithTime.setFactStartTime(start.get(0).getActualAdvanceTime() / (TimeUtil.MINUTE / TimeUtil.SECOND));
                }
                List<RefrigeratingRunningStrategy> stop = collect.stream().filter(refrigeratingRunningStrategy -> Objects.equals(DeviceOperationType.STOP, refrigeratingRunningStrategy.getDeviceOperationType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(stop)) {
                    aiPredictWithTime.setPredictStopTime(stop.get(0).getPredictAdvanceTime() / (TimeUtil.MINUTE / TimeUtil.SECOND));
                    aiPredictWithTime.setFactStopTime(stop.get(0).getActualAdvanceTime() / (TimeUtil.MINUTE / TimeUtil.SECOND));
                }
                result.add(aiPredictWithTime);
            }
        }

        return result;
    }

    private void calcuLateDoubleValue(AIPredictWeatherVo item, Double value1, Double value2, Double value3, Double value4) {
        if (Objects.nonNull(value1)) {
            item.setFactValue(value1);
        }
        if (Objects.nonNull(value2) && Objects.nonNull(item.getFactValue())) {
            item.setFactValue(CommonUtils.calcDouble(item.getFactValue(), value2, EnumOperationType.ADD.getId()));
        } else if (Objects.isNull(item.getFactValue())) {
            item.setFactValue(value2);
        }
        if (Objects.nonNull(value3) && Objects.nonNull(item.getFactValue())) {
            item.setFactValue(CommonUtils.calcDouble(item.getFactValue(), value3, EnumOperationType.ADD.getId()));
        } else if (Objects.isNull(item.getFactValue())) {
            item.setFactValue(value3);
        }
        if (Objects.nonNull(value4) && Objects.nonNull(item.getFactValue())) {
            item.setFactValue(CommonUtils.calcDouble(item.getFactValue(), value4, EnumOperationType.ADD.getId()));
        } else if (Objects.isNull(item.getFactValue())) {
            item.setFactValue(value4);
        }
    }

    private List<AIPredictWeatherVo> assembleSystemPowerTrendData(List<OperationTrendVo> operationTrendVoList, List<ColdPredict> coldPredicts) {
        List<AIPredictWeatherVo> result = new ArrayList<>();
        for (int i = 0; i < operationTrendVoList.size(); i++) {
            AIPredictWeatherVo item = new AIPredictWeatherVo();
            item.setFactValue(operationTrendVoList.get(i).getValue());
            OperationTrendVo operationTrendVo = operationTrendVoList.get(i);
            item.setLogtime(operationTrendVoList.get(i).getTime());
            List<ColdPredict> collect = coldPredicts.stream().filter(coldPredict -> Objects.equals(operationTrendVo.getTime(), TimeUtil.localDateTime2timestamp(coldPredict.getLogTime()))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                item.setPredictValue(collect.get(0).getValue());
            }
            item.setDifference(calcAbsDouble(item.getPredictValue(), item.getFactValue()));
            result.add(item);
        }
        return result;
    }

    private List<AIPredictWeatherWithEnergyVo> assembleAIPredictWeatherWithEnergyVo(List<OperationTrendVo> operationTrendVoList, List<Weather> weathers
            , List<ColdActual> endActuals, List<ColdPredict> coldPredicts, QueryParam queryParam, Boolean flag) {

        List<Long> timeRangeForFifteen = getTimeRangeForFifteen(queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle());
        List<AIPredictWeatherWithEnergyVo> timeValues = new ArrayList<>();
        for (Long time : timeRangeForFifteen) {
            AIPredictWeatherWithEnergyVo aiPredictWeatherWithEnergyVo = new AIPredictWeatherWithEnergyVo(time);
            if (CollectionUtils.isEmpty(operationTrendVoList) && CollectionUtils.isEmpty(weathers)
                    && CollectionUtils.isEmpty(endActuals) && CollectionUtils.isEmpty(coldPredicts)) {
                timeValues.add(aiPredictWeatherWithEnergyVo);
                continue;
            }
            List<OperationTrendVo> collect = operationTrendVoList.stream().filter(it -> Objects.equals(time, it.getTime())).collect(Collectors.toList());
            List<Weather> predictForWeather = weathers.stream().filter(it -> Objects.equals((it.getLogTime()), time)).collect(Collectors.toList());
            List<ColdActual> endForEnergy = endActuals.stream().filter(it -> Objects.equals(time, TimeUtil.localDateTime2timestamp(it.getLogTime()))).collect(Collectors.toList());
            List<ColdPredict> predictForEnergy = coldPredicts.stream().filter(it -> Objects.equals(TimeUtil.localDateTime2timestamp(it.getLogTime()), time)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect) && CollectionUtils.isEmpty(predictForWeather)
                    && CollectionUtils.isEmpty(endForEnergy) && CollectionUtils.isEmpty(predictForEnergy)) {
                timeValues.add(aiPredictWeatherWithEnergyVo);
            } else {
                assembleDataOfTemp(collect,
                        predictForWeather,
                        endForEnergy,
                        predictForEnergy, aiPredictWeatherWithEnergyVo, flag);
                timeValues.add(aiPredictWeatherWithEnergyVo);
            }
        }
        return timeValues;
    }

    private void assembleDataOfTemp(List<OperationTrendVo> collect,
                                    List<Weather> predictForWeather,
                                    List<ColdActual> endForEnergy,

                                    List<ColdPredict> predictForEnergy, AIPredictWeatherWithEnergyVo aiPredictWeatherWithEnergyVo, Boolean flag) {
        if (CollectionUtils.isNotEmpty(collect)) {
            aiPredictWeatherWithEnergyVo.setWeatherFactValue(collect.get(0).getValue());
        }
        if (CollectionUtils.isNotEmpty(predictForWeather) && Boolean.TRUE.equals(flag)) {
            aiPredictWeatherWithEnergyVo.setWeatherPredictValue(predictForWeather.get(0).getTempAvg());
        } else if (CollectionUtils.isNotEmpty(predictForWeather) && Boolean.FALSE.equals(flag)) {
            aiPredictWeatherWithEnergyVo.setWeatherPredictValue(predictForWeather.get(0).getHumidity());

        }
        if (CollectionUtils.isNotEmpty(endForEnergy)) {
            Double data = handleDoubleData((endForEnergy.get(0).getValue()));
            if (Objects.nonNull(data)){
                aiPredictWeatherWithEnergyVo.setCoolingLoadFactValue(data * 1.01);
            }
        }
        if (CollectionUtils.isNotEmpty(predictForEnergy)) {
            aiPredictWeatherWithEnergyVo.setCoolingLoadPredictValue(predictForEnergy.get(0).getValue());
        }
    }


    private List<AIPredictWeatherVo> transToWeatherVo(List<OperationTrendVo> operationTrendVoList, List<Weather> weathers) {
        List<AIPredictWeatherVo> result = new ArrayList<>();

        for (OperationTrendVo operationTrendVo : operationTrendVoList) {
            AIPredictWeatherVo aiPredictWeatherVo = new AIPredictWeatherVo();
            aiPredictWeatherVo.setFactValue(operationTrendVo.getValue());
            aiPredictWeatherVo.setLogtime(operationTrendVo.getTime());
            List<Weather> collect = weathers.stream().filter(weather -> Objects.equals((weather.getLogTime()), operationTrendVo.getTime())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                aiPredictWeatherVo.setPredictValue(collect.get(0).getTempAvg());
            }
            aiPredictWeatherVo.setDifference(calcAbsDouble(aiPredictWeatherVo.getPredictValue(), aiPredictWeatherVo.getFactValue()));
            result.add(aiPredictWeatherVo);
        }
        return result;
    }

    /**
     * value是预测，value2是实际
     *
     * @param value
     * @param value2
     * @return
     */
    private Double calcAbsDouble(Double value, Double value2) {
        if (Objects.isNull(value) || Objects.isNull(value2)) {
            return null;
        }
        Double aDouble = CommonUtils.calcDouble(value, value2, EnumOperationType.RATE.getId());
        if (value < value2) {
            return CommonUtils.calcDouble(0.0, aDouble, EnumOperationType.SUBTRACT.getId());
        }
        return aDouble;
    }

    private List<AIPredictWeatherVo> transToWeatherVoForHum(List<OperationTrendVo> operationTrendVoList, List<Weather> weathers) {
        List<AIPredictWeatherVo> result = new ArrayList<>();

        for (OperationTrendVo operationTrendVo : operationTrendVoList) {
            AIPredictWeatherVo aiPredictWeatherVo = new AIPredictWeatherVo();
            aiPredictWeatherVo.setFactValue(operationTrendVo.getValue());
            aiPredictWeatherVo.setLogtime(operationTrendVo.getTime());
            List<Weather> collect = weathers.stream().filter(weather -> Objects.equals((weather.getLogTime()), operationTrendVo.getTime())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                aiPredictWeatherVo.setPredictValue(collect.get(0).getHumidity());
            }
            aiPredictWeatherVo.setDifference(calcAbsDouble(aiPredictWeatherVo.getPredictValue(), aiPredictWeatherVo.getFactValue()));
            result.add(aiPredictWeatherVo);
        }
        return result;
    }

    private void calculatePercent(EquipmentGroupConsumption equipmentGroupConsumption) {

        List<Double> doubles = new ArrayList<>();
        if (Objects.nonNull(equipmentGroupConsumption.getColdWaterMainEngineConsumption())) {
            doubles.add(equipmentGroupConsumption.getColdWaterMainEngineConsumption());
        }
        if (Objects.nonNull(equipmentGroupConsumption.getCoolingTowerConsumption())) {
            doubles.add(equipmentGroupConsumption.getCoolingTowerConsumption());
        }
        if (Objects.nonNull(equipmentGroupConsumption.getCoolingPump())) {
            doubles.add(equipmentGroupConsumption.getCoolingPump());
        }
        if (Objects.nonNull(equipmentGroupConsumption.getRefrigeratingPump())) {
            doubles.add(equipmentGroupConsumption.getRefrigeratingPump());
        }

        if (CollectionUtils.isEmpty(doubles)) {
            return;
        }
        double sum = doubles.stream().mapToDouble(Double::doubleValue).sum();
        equipmentGroupConsumption.setPercentOfMainEngine(CommonUtils.calcDouble(equipmentGroupConsumption.getColdWaterMainEngineConsumption(), sum, EnumOperationType.DIVISION.getId()));
        equipmentGroupConsumption.setPercentOfTower(CommonUtils.calcDouble(equipmentGroupConsumption.getCoolingTowerConsumption(), sum, EnumOperationType.DIVISION.getId()));
        equipmentGroupConsumption.setPercentOfCoolingPump(CommonUtils.calcDouble(equipmentGroupConsumption.getPercentOfCoolingPump(), sum, EnumOperationType.DIVISION.getId()));
        equipmentGroupConsumption.setPercentOfRefrigeratingPump(CommonUtils.calcDouble(equipmentGroupConsumption.getPercentOfRefrigeratingPump(), sum, EnumOperationType.DIVISION.getId()));

    }

    private void calculatePump(List<BaseVo> baseVos1, List<EnergyConsumption> value, EquipmentGroupConsumption equipmentGroupConsumption) {
        List<Double> valueOfPump = new ArrayList<>();
        List<Double> valueOfPumpOther = new ArrayList<>();
        for (EnergyConsumption energyConsumption : value) {
            List<BaseVo> collect = baseVos1.stream().filter(baseVo -> Objects.equals(baseVo.getId(), energyConsumption.getObjectid())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                valueOfPump.add(energyConsumption.getUsage());
            } else {
                valueOfPumpOther.add(energyConsumption.getUsage());
            }
        }
        if (CollectionUtils.isNotEmpty(valueOfPump)) {
            double sum = valueOfPump.stream().mapToDouble(Double::doubleValue).sum();
            equipmentGroupConsumption.setCoolingPump(sum);
        }
        if (CollectionUtils.isNotEmpty(valueOfPumpOther)) {
            double sumOther = valueOfPumpOther.stream().mapToDouble(Double::doubleValue).sum();
            equipmentGroupConsumption.setRefrigeratingPump(sumOther);
        }
    }

    private List<BaseVo> queryOtherPump(List<BaseVo> baseVos) {
        List<Long> collect = baseVos.stream().filter(baseVo -> Objects.equals(NodeLabelDef.PUMP, baseVo.getModelLabel())).map(BaseVo::getId).collect(Collectors.toList());
        QueryCondition condition = new QueryConditionBuilder<>(NodeLabelDef.PUMP).in(ColumnDef.ID, collect).eq(ColdOptimizationLabelDef.FUNCTION_TYPE, PumpFunctionType.REFRIGERATING_PUMP).queryAsTree()
                .build();
        return modelServiceUtils.query(condition, BaseVo.class);
    }

    public List<BaseVo> queryEquipmentGroupDevice(QueryParam queryParam) {

        SingleModelConditionDTO singleModelConditionDTO = new SingleModelConditionDTO(NodeLabelDef.PUMP);
        List<ConditionBlock> filters = new ArrayList<>();
        filters.add(new ConditionBlock(ColdOptimizationLabelDef.FUNCTION_TYPE, ConditionBlock.OPERATOR_IN, Arrays.asList(PumpFunctionType.COOLING_PUMP, PumpFunctionType.REFRIGERATING_PUMP)));
        singleModelConditionDTO.setFilter(new ConditionBlockCompose(filters));
        SingleModelConditionDTO singleModelConditionDTO1 = new SingleModelConditionDTO(NodeLabelDef.COLD_WATER_MAINENGINE);
        SingleModelConditionDTO singleModelConditionDTO2 = new SingleModelConditionDTO(NodeLabelDef.COOLING_TOWER);
        QueryCondition condition = new QueryConditionBuilder<>(queryParam.getObjectLabel(), queryParam.getObjectId())
                .leftJoinCondition(Arrays.asList(singleModelConditionDTO, singleModelConditionDTO1, singleModelConditionDTO2))
                .queryAsTree()
                .build();
        List<BaseVo> query = modelServiceUtils.query(condition, BaseVo.class);
        List<BaseVo> result = new ArrayList<>();
        for (BaseVo baseVo : query) {
            if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                result.addAll(baseVo.getChildren());
            }
        }
        return result;
    }

    private void addeDoubleValue(OperationTrendVo item, Double value1, Double value2, Double value3, Double value4) {
        if (Objects.nonNull(value1)) {
            item.setTotalSystemPower(value1);
        }
        if (Objects.nonNull(item.getTotalSystemPower()) && Objects.nonNull(value2)) {
            item.setTotalSystemPower(CommonUtils.calcDouble(item.getTotalSystemPower(), value2, EnumOperationType.ADD.getId()));
        } else if (Objects.nonNull(value2)) {
            item.setTotalSystemPower(value2);
        }
        if (Objects.nonNull(item.getTotalSystemPower()) && Objects.nonNull(value3)) {
            item.setTotalSystemPower(CommonUtils.calcDouble(item.getTotalSystemPower(), value3, EnumOperationType.ADD.getId()));
        } else if (Objects.nonNull(value3)) {
            item.setTotalSystemPower(value3);
        }
        if (Objects.nonNull(item.getTotalSystemPower()) && Objects.nonNull(value4)) {
            item.setTotalSystemPower(CommonUtils.calcDouble(item.getTotalSystemPower(), value4, EnumOperationType.ADD.getId()));
        } else if (Objects.nonNull(value4)) {
            item.setTotalSystemPower(value4);
        }
    }

    private List<OperationTrendVo> assembleSystemTrendData(List<OperationTrendVo> operationTrendVoList, List<OperationTrendVo> coolingWaterVoList, List<OperationTrendVo> operationTrendVoListOfPump1,
                                                           List<OperationTrendVo> operationTrendVoListOfPump2,
                                                           List<ColdActual> endAndLossColdActual) {
        List<OperationTrendVo> result = new ArrayList<>();
        for (int i = 0; i < operationTrendVoList.size(); i++) {
            OperationTrendVo item = new OperationTrendVo();
            addeDoubleValue(item, operationTrendVoList.get(i).getValue(), coolingWaterVoList.get(i).getValue(), operationTrendVoListOfPump1.get(i).getValue(), operationTrendVoListOfPump2.get(i).getValue());
            item.setCoolingLoad((endAndLossColdActual.get(i).getValue()));

            item.setTime(operationTrendVoList.get(i).getTime());
            item.setDifference(CommonUtils.calcDouble(item.getCoolingLoad(), item.getTotalSystemPower(), EnumOperationType.DIVISION.getId()));
            result.add(item);
        }
        return result;
    }


    private List<OperationTrendVo> querySingleDataLog(List<BaseVo> baseVos, QueryParam queryParam, QuantitySearchVo quantitySearchVo) {
        Map<Integer, List<TrendDataVo>> dataLogResult = quantityManageService.queryDataLogBatch(
                createQuantityDataBatchSearchVo(baseVos, queryParam, quantitySearchVo));
        if (Objects.isNull(dataLogResult)) {
            return Collections.emptyList();
        }
        // 取出设备数据
        List<TrendDataVo> dataVoList = dataLogResult.get(quantitySearchVo.getId());
        List<OperationTrendVo> operationTrendVoList = transSingleData(dataVoList);
        return assembleOperationTrendVo(operationTrendVoList, queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle());
    }

    /**
     * 取出多设备数据
     *
     * @param energySupplyToPos
     * @param queryParam
     * @param quantitySearchVo
     * @return
     * @throws Exception
     */
    private List<OperationTrendVo> querySingleDataLogWithDevices(List<EnergySupplyToPo> energySupplyToPos, QueryParam queryParam, QuantitySearchVo quantitySearchVo) {
        List<BaseVo> baseVos = energySupplyToPos.stream().map(energySupplyToPo -> new BaseVo(energySupplyToPo.getObjectid(), energySupplyToPo.getObjectlabel())).collect(Collectors.toList());
        Map<Integer, List<TrendDataVo>> dataLogResult = quantityManageService.queryDataLogBatch(
                createQuantityDataBatchSearchVo(baseVos, queryParam, quantitySearchVo));
        if (Objects.isNull(dataLogResult)) {
            return Collections.emptyList();
        }
        Map<BaseVo, List<EnergySupplyToPo>> baseVoListMap = energySupplyToPos.stream()
                .collect(Collectors.groupingBy(
                        energySupplyToPo -> new BaseVo(energySupplyToPo.getSupplytoid(), energySupplyToPo.getSupplytolabel())));
        // 取出设备数据
        List<TrendDataVo> dataVoList = dataLogResult.get(quantitySearchVo.getId());
        List<OperationTrendVo> operationTrendVoList = assembleSingleDeviceData(baseVoListMap, dataVoList);
        return assembleOperationTrendVo(operationTrendVoList, queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle());
    }

    /**
     * 取出多设备数据相加
     *
     * @param queryParam
     * @param quantitySearchVo
     * @param baseVos
     * @return
     */
    private List<OperationTrendVo> queryDataLogAdd(QueryParam queryParam, QuantitySearchVo quantitySearchVo, List<BaseVo> baseVos) {
        Map<Integer, List<TrendDataVo>> dataLogResult = quantityManageService.queryDataLogBatch(
                createQuantityDataBatchSearchVo(baseVos, queryParam, quantitySearchVo));
        if (Objects.isNull(dataLogResult)) {
            return Collections.emptyList();
        }

        // 取出设备数据
        List<TrendDataVo> dataVoList = dataLogResult.get(quantitySearchVo.getId());
        List<OperationTrendVo> operationTrendVoList = assembleSingleAddData(dataVoList);
        return assembleOperationTrendVo(operationTrendVoList, queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle());
    }

    private List<OperationTrendVo> assembleSingleAddData(List<TrendDataVo> dataVoList) {
        if (CollectionUtils.isEmpty(dataVoList)) {
            return Collections.emptyList();
        }
        List<DatalogValue> datalogValue = new ArrayList<>();
        for (TrendDataVo trendDataVo : dataVoList) {
            datalogValue.addAll(trendDataVo.getDataList());
        }
        return transAllData(datalogValue);
    }


    private Double assembleSingleRealTimeDeviceData(Map<BaseVo, List<EnergySupplyToPo>> baseVoListMap, List<RealTimeValue> dataVoList) {
        List<RealTimeValue> DatalogValue = new ArrayList<>();
        for (Map.Entry<BaseVo, List<EnergySupplyToPo>> entryMap : baseVoListMap.entrySet()) {
            List<EnergySupplyToPo> value = entryMap.getValue();
            List<BaseVo> monitor = value.stream().map(energySupplyToPo -> new BaseVo(energySupplyToPo.getObjectid(), energySupplyToPo.getObjectlabel()))
                    .collect(Collectors.toList());
            //过滤出属于某个设备的采集设备的数据，再取第一个有值的，然后把这个类型的值全部加起来
            List<RealTimeValue> trendDataVos = dataVoList.stream()
                    .filter(trendDataVo -> monitor.contains(new BaseVo(trendDataVo.getMonitoredId(), trendDataVo.getMonitoredLabel()))).collect(Collectors.toList());
            RealTimeValue data = trendDataVos.stream().filter(realTimeValue -> Objects.nonNull(realTimeValue.getValue())).findAny().orElse(new RealTimeValue());
            if (Objects.nonNull(data.getValue())) {
                DatalogValue.add(data);
            }
        }
        if (CollectionUtils.isEmpty(DatalogValue)) {
            return null;
        }
        return DatalogValue.stream().mapToDouble(RealTimeValue::getValue).sum();
    }

    private List<OperationTrendVo> assembleSingleDeviceData(Map<BaseVo, List<EnergySupplyToPo>> baseVoListMap, List<TrendDataVo> dataVoList) {
        List<DatalogValue> DatalogValue = new ArrayList<>();
        for (Map.Entry<BaseVo, List<EnergySupplyToPo>> entryMap : baseVoListMap.entrySet()) {
            List<EnergySupplyToPo> value = entryMap.getValue();
            List<BaseVo> monitor = value.stream().map(energySupplyToPo -> new BaseVo(energySupplyToPo.getObjectid(), energySupplyToPo.getObjectlabel())).collect(Collectors.toList());
            //过滤出属于某个设备的采集设备的数据，再取第一个有值的，然后把这个类型的值全部加起来
            List<TrendDataVo> trendDataVos = dataVoList.stream().filter(trendDataVo -> monitor.contains(new BaseVo(trendDataVo.getMonitoredid(), trendDataVo.getMonitoredlabel()))).collect(Collectors.toList());
            List<DatalogValue> dataList = getNonValueDataLog(trendDataVos);
            DatalogValue.addAll(dataList);
        }
        return transAllData(DatalogValue);
    }

    private List<OperationTrendVo> transAllData(List<DatalogValue> datalogValue) {
        if (CollectionUtils.isEmpty(datalogValue)) {
            return Collections.emptyList();
        }
        List<OperationTrendVo> operationTrendVoList = new ArrayList<>();
        Map<Long, List<DatalogValue>> longListMap = datalogValue.stream().collect(Collectors.groupingBy(DatalogValue::getTime));
        for (Map.Entry<Long, List<DatalogValue>> entry : longListMap.entrySet()) {
            List<DatalogValue> collect = entry.getValue().stream().filter(DatalogValue1 -> Objects.nonNull(DatalogValue1.getValue())).collect(Collectors.toList());
            OperationTrendVo operationTrendVo = new OperationTrendVo();
            if (CollectionUtils.isNotEmpty(collect)) {
                double sum = collect.stream().mapToDouble(DatalogValue::getValue).sum();
                operationTrendVo.setValue(sum);
            }
            operationTrendVo.setTime(entry.getKey());
            operationTrendVoList.add(operationTrendVo);
        }
        return operationTrendVoList;
    }

    private List<OperationTrendVo> querySingleDataLogWithOut(List<BaseVo> baseVos, QueryParam queryParam, QuantitySearchVo quantitySearchVo) {
        Map<Integer, List<TrendDataVo>> dataLogResult = quantityManageService.queryDataLogBatch(
                createQuantityDataBatchSearchVo(baseVos, queryParam, quantitySearchVo));


        if (Objects.isNull(dataLogResult)) {
            return Collections.emptyList();
        }
        // 取出设备数据
        List<TrendDataVo> dataVoList = dataLogResult.get(quantitySearchVo.getId());
        return transSingleData(dataVoList);

    }


    private List<OperationTrendVo> transSingleData(List<TrendDataVo> dataVoList) {
        if (CollectionUtils.isEmpty(dataVoList)) {
            return Collections.emptyList();
        }
        List<OperationTrendVo> operationTrendVoList = new ArrayList<>();
        List<DatalogValue> dataList = getNonValueDataLog(dataVoList);
        for (DatalogValue dataVo : dataList) {
            OperationTrendVo operationTrendVo = new OperationTrendVo();
            operationTrendVo.setValue(dataVo.getValue());
            operationTrendVo.setTime(dataVo.getTime());
            operationTrendVoList.add(operationTrendVo);
        }
        return operationTrendVoList;
    }

    /**
     * 获得第一个不为null值的定时任务的数据
     *
     * @return
     */
    @Override
    public List<DatalogValue> getNonValueDataLog(List<TrendDataVo> dataVoList) {
        if (CollectionUtils.isEmpty(dataVoList)) {
            return Collections.emptyList();
        }
        for (TrendDataVo trendDataVo : dataVoList) {
            if (CollectionUtils.isNotEmpty(trendDataVo.getDataList()) && Objects.nonNull(trendDataVo.getDataList().get(0).getValue())) {
                return trendDataVo.getDataList();
            }
        }
        return dataVoList.get(0).getDataList();
    }

    private List<OperationTrendVo> transData(List<TrendDataVo> supplyTempList, List<TrendDataVo> returnTempList,
                                             List<TrendDataVo> supplyTempSettingList, List<TrendDataVo> returnTempSettingList, Boolean isCold) {
        if (CollectionUtils.isEmpty(supplyTempList) && CollectionUtils.isEmpty(returnTempList)) {
            return Collections.emptyList();
        }
        List<OperationTrendVo> operationTrendVoList = new ArrayList<>();
        List<DatalogValue> supplyList = getNonValueDataLog(supplyTempList);
        List<DatalogValue> returnList = getNonValueDataLog(returnTempList);
        List<DatalogValue> supplySettingList = getNonValueDataLog(supplyTempSettingList);
        List<DatalogValue> returnSettingList = getNonValueDataLog(returnTempSettingList);
        for (DatalogValue dataVo : supplyList) {
            {
                List<DatalogValue> returnTemp = returnList.stream().filter(datalogValue -> Objects.equals(datalogValue.getTime(), dataVo.getTime())).collect(Collectors.toList());
                OperationTrendVo operationTrendVo = new OperationTrendVo();
                if (CollectionUtils.isNotEmpty(returnTemp)) {
                    operationTrendVo.setReturnTemp(returnTemp.get(0).getValue());
                }
                operationTrendVo.setSupplyTemp(dataVo.getValue());
                operationTrendVo.setTime(dataVo.getTime());
                if (Boolean.FALSE.equals(isCold)) {
                    operationTrendVo.setDifference(CommonUtils.calcDouble(dataVo.getValue(), operationTrendVo.getReturnTemp(), EnumOperationType.SUBTRACT.getId()));
                } else {
                    operationTrendVo.setDifference(CommonUtils.calcDouble(operationTrendVo.getReturnTemp(), dataVo.getValue(), EnumOperationType.SUBTRACT.getId()));
                }
                List<DatalogValue> sameSupply = supplySettingList.stream().filter(datalogValue -> Objects.equals(datalogValue.getTime(), dataVo.getTime())).collect(Collectors.toList());
                List<DatalogValue> sameReturn = returnSettingList.stream().filter(datalogValue -> Objects.equals(datalogValue.getTime(), dataVo.getTime())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(sameSupply)) {
                    operationTrendVo.setSupplyTempSetting(sameSupply.get(0).getValue());
                }
                if (CollectionUtils.isNotEmpty(sameReturn)) {
                    operationTrendVo.setReturnTempSetting(sameReturn.get(0).getValue());
                }
                operationTrendVoList.add(operationTrendVo);


            }

        }
        return operationTrendVoList;
    }

    private List<Long> getTimeRangeForFifteen(LocalDateTime st, LocalDateTime et) {
        List<Long> timeRange = new ArrayList<>();
        LocalDateTime j;
        for (j = st; j.isBefore(et); j = j.plusMinutes(15L)) {
            timeRange.add(TimeUtil.localDateTime2timestamp(j));
        }
        return timeRange;
    }

    private List<Long> getTimeRangeForFifteen(LocalDateTime st, LocalDateTime et, Integer cycle) {
        List<Long> timeRange;
        if (Objects.isNull(cycle)) {
            return Collections.emptyList();
        }
        if (Objects.equals(cycle, FIFTEENMINUTE)) {
            timeRange = getTimeRangeForFifteen(st, et);
        } else {
            timeRange = TimeUtil.getTimeRange(TimeUtil.localDateTime2timestamp(st), TimeUtil.localDateTime2timestamp(et), cycle);
        }
        return timeRange;
    }

    private List<OperationTrendVo> assembleOperationTrendVo(List<OperationTrendVo> operationTrendVoList, LocalDateTime st, LocalDateTime et, Integer cycle) {
        List<Long> timeRange = getTimeRangeForFifteen(st, et, cycle);
        List<OperationTrendVo> timeValues = new ArrayList<>();
        for (Long time : timeRange) {
            if (CollectionUtils.isEmpty(operationTrendVoList)) {
                timeValues.add(new OperationTrendVo(time));
                continue;
            }

            List<OperationTrendVo> collect = operationTrendVoList.stream().filter(it -> Objects.equals(time, it.getTime())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                timeValues.add(new OperationTrendVo(time));
            } else {
                timeValues.add(collect.get(0));
            }
        }
        return timeValues;
    }

    /**
     * 冷冻水供水物理量
     *
     * @return
     */
    private QuantitySearchVo getFreezingWaterForSupplyQuantitySetting() {
        return new QuantitySearchVo(6007206,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.SUPPLY,
                47);
    }

    /**
     * 冷冻水回水物理量
     *
     * @return
     */
    private QuantitySearchVo getFreezingWaterForReturnQuantitySetting() {
        return new QuantitySearchVo(6007207,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.RETURN,
                47);
    }

    /**
     * 冷冻水供水标准物理量
     *
     * @return
     */
    private QuantitySearchVo getFreezingWaterForSupplyStandardQuantitySetting() {
        return new QuantitySearchVo(6000438,
                QuantityCategoryDef.TEMP,
                191,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                47);
    }

    /**
     * 冷冻水回水物理量
     *
     * @return
     */
    private QuantitySearchVo getFreezingWaterForReturnStandardQuantitySetting() {

        return new QuantitySearchVo(6000439,
                QuantityCategoryDef.TEMP,
                192,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                47);
    }

    /**
     * 冷却水供水物理量
     *
     * @return
     */
    private QuantitySearchVo getCoolingWaterForSupplyQuantitySetting() {
        return new QuantitySearchVo(6007203,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.SUPPLY,
                48);
    }

    /**
     * 冷却水回水物理量
     *
     * @return
     */
    private QuantitySearchVo getCoolingWaterForReturnQuantitySetting() {
        return new QuantitySearchVo(6007204,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.RETURN,
                48);
    }

    /**
     * 冷却水供水标准物理量
     *
     * @return
     */
    private QuantitySearchVo getCoolingWaterForSupplyStandaQuantitySetting() {
        return new QuantitySearchVo(6000440,
                QuantityCategoryDef.TEMP,
                191,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                48);
    }

    /**
     * 冷却水回水标准物理量
     *
     * @return
     */
    private QuantitySearchVo getCoolingWaterForReturnStandaQuantitySetting() {
        return new QuantitySearchVo(6000441,
                QuantityCategoryDef.TEMP,
                192,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                48);
    }


    /**
     * 冷水主机功率
     *
     * @return
     */
    private QuantitySearchVo getMainPowerQuantitySetting() {
        return new QuantitySearchVo(2000004,
                QuantityCategoryDef.POWER,
                QuantityTypeDef.POWER,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.ELECTRIC);
    }

    /**
     * 冷却塔功率
     *
     * @return
     */
    private QuantitySearchVo getTowerPowerQuantitySetting() {

        return new QuantitySearchVo(2000004,
                QuantityCategoryDef.POWER,
                QuantityTypeDef.POWER,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.ELECTRIC);
    }

    /**
     * 冷冻水泵功率
     *
     * @return
     */
    private QuantitySearchVo getFreezingPumpPowerQuantitySetting() {
        return new QuantitySearchVo(2000004,
                QuantityCategoryDef.POWER,
                QuantityTypeDef.POWER,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.ELECTRIC);
    }

    /**
     * 冷却水泵功率
     *
     * @return
     */
    private QuantitySearchVo getCoolingPumpPowerQuantitySetting() {
        return new QuantitySearchVo(2000004,
                QuantityCategoryDef.POWER,
                QuantityTypeDef.POWER,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.ELECTRIC);
    }


    /**
     * 冷水主机冷机负荷物理量
     *
     * @return
     */
    private QuantitySearchVo getMainCoolingLoadQuantitySetting() {
        return new QuantitySearchVo(6008010,
                QuantityCategoryDef.POWER,
                QuantityTypeDef.POWER,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.COLD);
    }

    public QuantityDataBatchSearchVo createQuantityDataBatchSearchVo(List<BaseVo> deviceNodes, QueryParam
            query, QuantitySearchVo quantitySearchVo) {
        QuantityDataBatchSearchVo aggregationDataBatch = new QuantityDataBatchSearchVo();
        aggregationDataBatch.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        aggregationDataBatch.setStartTime(TimeUtil.localDateTime2timestamp(query.getStartTime()));
        aggregationDataBatch.setEndTime(TimeUtil.localDateTime2timestamp(query.getEndTime()));
        aggregationDataBatch.setQuantitySettings(Collections.singletonList(quantitySearchVo));
        aggregationDataBatch.setNodes(deviceNodes);
        aggregationDataBatch.setAggregationCycle(query.getCycle());
        return aggregationDataBatch;
    }

    private QuantityDataBatchSearchVo quantityDataBatchSearchVo(List<BaseVo> deviceNodes, QueryParam
            query, List<QuantitySearchVo> quantitySearchVo) {
        QuantityDataBatchSearchVo aggregationDataBatch = new QuantityDataBatchSearchVo();
        aggregationDataBatch.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        aggregationDataBatch.setStartTime(TimeUtil.localDateTime2timestamp(query.getStartTime()));
        aggregationDataBatch.setEndTime(TimeUtil.localDateTime2timestamp(query.getEndTime()));
        aggregationDataBatch.setQuantitySettings((quantitySearchVo));
        aggregationDataBatch.setNodes(deviceNodes);
        aggregationDataBatch.setAggregationCycle(query.getCycle());
        return aggregationDataBatch;
    }

    @Override
    public QuantityDataBatchSearchVo createQuantityDataBatchSearchVo(List<BaseVo> deviceNodes, LocalDateTime st,
                                                                     LocalDateTime et, Integer cycle, QuantitySearchVo quantitySearchVo) {
        QuantityDataBatchSearchVo aggregationDataBatch = new QuantityDataBatchSearchVo();
        aggregationDataBatch.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        aggregationDataBatch.setStartTime(TimeUtil.localDateTime2timestamp(st));
        aggregationDataBatch.setEndTime(TimeUtil.localDateTime2timestamp(et));
        aggregationDataBatch.setQuantitySettings(Collections.singletonList(quantitySearchVo));
        aggregationDataBatch.setNodes(deviceNodes);
        aggregationDataBatch.setAggregationCycle(cycle);
        return aggregationDataBatch;
    }

    @Override
    public QuantityDataBatchSearchVo createQuantityDataSearchVo(List<BaseVo> deviceNodes, QueryParam
            query, List<QuantitySearchVo> quantitySearchVo) {
        QuantityDataBatchSearchVo aggregationDataBatch = new QuantityDataBatchSearchVo();
        aggregationDataBatch.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        aggregationDataBatch.setStartTime(TimeUtil.localDateTime2timestamp(query.getStartTime()));
        aggregationDataBatch.setEndTime(TimeUtil.localDateTime2timestamp(query.getEndTime()));
        aggregationDataBatch.setQuantitySettings(quantitySearchVo);
        aggregationDataBatch.setNodes(deviceNodes);
        aggregationDataBatch.setAggregationCycle(query.getCycle());
        return aggregationDataBatch;
    }

    @Override
    public List<BaseVo> queryColdWaterMainEngine(QueryParam queryParam, String label) {
        QueryCondition condition = new QueryConditionBuilder<>(queryParam.getObjectLabel(), queryParam.getObjectId())
                .leftJoin(label)
                .queryAsTree()
                .build();
        List<Map<String, Object>> query = modelServiceUtils.query(condition);
        List<BaseVo> baseVos = JsonTransferUtils.transferList(query, BaseVo.class);
        List<BaseVo> result = new ArrayList<>();
        for (BaseVo baseVo : baseVos) {
            if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                result.addAll(baseVo.getChildren());
            }
        }
        return result;
    }


    /**
     * 获得温度实际值
     *
     * @return
     */
    private QuantitySearchVo getAiPredictForTempQuantitySetting() {
        return new QuantitySearchVo(6002006,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
    }

    /**
     * 获得湿度实际值
     *
     * @return
     */
    private QuantitySearchVo getAiPredictForHumQuantitySetting() {
        return new QuantitySearchVo(6005000,
                QuantityCategoryDef.HUMIDITY,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
    }

}