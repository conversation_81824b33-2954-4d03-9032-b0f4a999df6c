package com.cet.eem.fusion.demo.core;

import org.apache.maven.repository.internal.MavenRepositorySystemUtils;
import org.eclipse.aether.*;
import org.eclipse.aether.artifact.DefaultArtifact;
import org.eclipse.aether.collection.CollectRequest;
import org.eclipse.aether.graph.Dependency;
import org.eclipse.aether.graph.DependencyNode;
import org.eclipse.aether.repository.LocalRepository;
import org.eclipse.aether.resolution.DependencyRequest;
import org.eclipse.aether.resolution.DependencyResult;
import org.eclipse.aether.impl.DefaultServiceLocator;
import org.eclipse.aether.connector.basic.BasicRepositoryConnectorFactory;
import org.eclipse.aether.spi.connector.RepositoryConnectorFactory;
import org.eclipse.aether.transport.file.FileTransporterFactory;
import org.eclipse.aether.transport.http.HttpTransporterFactory;
import org.eclipse.aether.spi.connector.transport.TransporterFactory;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.jar.*;

/**
 * 纯本地仓库解析器
 * 1. 不进任何私服/central
 * 2. 完全离线
 * 3. 只需本地仓库完整
 */
public class MavenDependencyResolver {

    private static final String GROUP_ID    = "com.cet.electric";
    private static final String ARTIFACT_ID = "eem-solution-common";
    private static final String VERSION     = "4.0.0-SNAPSHOT";
    private static final String LOCAL_REPO  = "E:/work/project/maven_repo";

    public static void main(String[] args) throws Exception {
        RepositorySystem system   = newRepositorySystem();
        RepositorySystemSession session = newSession(system);

        DependencyResult result = resolve(system, session, GROUP_ID, ARTIFACT_ID, VERSION);

        System.out.println("=== 依赖分析结果（纯本地仓库）===");
        print(result.getRoot());
    }

    /* ---------- 工具方法 ---------- */

    private static RepositorySystem newRepositorySystem() {
        DefaultServiceLocator locator = MavenRepositorySystemUtils.newServiceLocator();
        locator.addService(RepositoryConnectorFactory.class, BasicRepositoryConnectorFactory.class);
        locator.addService(TransporterFactory.class, FileTransporterFactory.class);
        locator.addService(TransporterFactory.class, HttpTransporterFactory.class);
        return locator.getService(RepositorySystem.class);
    }

    private static RepositorySystemSession newSession(RepositorySystem system) {
        DefaultRepositorySystemSession session = MavenRepositorySystemUtils.newSession();
        session.setOffline(true);                     // 完全离线
        session.setLocalRepositoryManager(
                system.newLocalRepositoryManager(session, new LocalRepository(LOCAL_REPO)));
        return session;
    }

    private static DependencyResult resolve(RepositorySystem system,
                                            RepositorySystemSession session,
                                            String g, String a, String v) throws Exception {
        DefaultArtifact artifact = new DefaultArtifact(g, a, "jar", v);
        CollectRequest collect = new CollectRequest()
                .setRoot(new Dependency(artifact, "compile"))
                .setRepositories(Collections.emptyList()); // 零远程仓库
        return system.resolveDependencies(session, new DependencyRequest(collect, null));
    }

    private static void print(DependencyNode node) { print(node, 0); }

    private static void print(DependencyNode node, int depth) {
        String indent = String.join("", Collections.nCopies(depth, "  "));
        if (node.getArtifact() != null) {
            System.out.println(indent + node.getArtifact());
            File jar = node.getArtifact().getFile();
            if (jar != null && jar.getName().endsWith(".jar")) {
                listClasses(jar, indent + "  ");
            }
        }
        for (DependencyNode child : node.getChildren()) print(child, depth + 1);
    }

    private static void listClasses(File jar, String indent) {
        try (JarFile jf = new JarFile(jar)) {
            int cnt = 0;
            for (JarEntry e : Collections.list(jf.entries())) {
                String n = e.getName();
                if (n.endsWith(".class") && !n.contains("$") && !n.contains("test")) {
                    System.out.println(indent + n.replace("/", ".").replace(".class", ""));
                    if (++cnt >= 10) {
                        System.out.println(indent + "... (更多类已省略)");
                        break;
                    }
                }
            }
        } catch (IOException ex) {
            System.err.println(indent + "读取 JAR 失败: " + ex.getMessage());
        }
    }
}