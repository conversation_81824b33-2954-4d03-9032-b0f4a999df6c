package com.cet.eem.fusion.transformer.core.service.aioptimization.impl;

import com.cet.eem.auth.service.NodeAuthCheckService;
import com.cet.eem.bll.common.def.EnergyDataType;
import com.cet.eem.bll.common.model.CompareResult;
import com.cet.eem.bll.common.model.domain.object.physicalquantity.ProjectUnitClassify;
import com.cet.eem.bll.common.model.domain.object.physicalquantity.UserDefineUnit;
import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumption;
import com.cet.eem.bll.common.model.domain.subject.generalrules.UnnaturalSetVo;
import com.cet.eem.bll.common.model.energy.EnergyResult;
import com.cet.eem.bll.common.model.ext.objective.physicalquantity.UserDefineUnitSearchDto;
import com.cet.eem.bll.common.service.EnergyUnitService;
import com.cet.eem.bll.common.service.UnnaturalTimeService;
import com.cet.eem.bll.common.util.*;
import com.cet.eem.bll.energy.dao.consumption.EnergyConsumptionDao;
import com.cet.eem.bll.energy.model.consumption.vo.ConsumptionSearchVo;
import com.cet.eem.bll.energy.model.consumption.vo.EnergyWithTbHbVo;
import com.cet.eem.bll.energy.service.consumption.EnergyConsumptionBffService;
import com.cet.eem.bll.energy.service.consumption.OilFieldService;
import com.cet.eem.bll.energysaving.dao.weather.RefrigeratingSystemDao;
import com.cet.eem.bll.energysaving.model.aiconsumption.*;
import com.cet.eem.bll.energysaving.model.config.system.RefrigeratingSystem;
import com.cet.eem.bll.energysaving.service.aioptimization.AIEnergyConsumptionAndCopService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.ParamUtils;
import com.cet.eem.common.constant.*;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.exception.ValidationException;
import com.cet.eem.common.file.FileUtils;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.Result;
import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.eem.common.model.node.NodeVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.time.DateUtils;
import com.cet.eem.common.time.TimeCoordinate;
import com.cet.eem.common.time.TimeUtils;
import com.cet.eem.common.utils.PoiExcelUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.toolkit.CollectionUtils;
import com.cet.eem.fusion.energy.sdk.util.DataCompareUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName : AIEnergyConsumptionAndCopServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-06-02 14:09
 */
@Service
@Slf4j
public class AIEnergyConsumptionAndCopServiceImpl implements AIEnergyConsumptionAndCopService {
    @Autowired
    RefrigeratingSystemDao refrigeratingSystemDao;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    OilFieldService oilFieldService;
    @Autowired
    EnergyConsumptionBffService energyConsumptionBffService;
    @Autowired
    UnnaturalTimeService unnaturalTimeUtils;
    @Autowired
    EnergyUnitService energyUnitService;
    @Autowired
    UnitUtils unitUtils;
    @Autowired
    EnergyConsumptionDao energyConsumptionDao;
    @Autowired
    NodeAuthCheckService nodeAuthBffService;
    private final static String DOWN = "_";
    private final static Integer NEED_AI = 4;
    private final static Integer CURRENT = 0;
    //gj转kw
    public static final Double UNIT = 0.0036D;

    @Override
    public List<BaseVo> treeQuery() {
        //查询制冷系统节点，再根据制冷系统节点关联的房间查询制冷设备
        List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.selectAll();
        List<Long> roomIds = refrigeratingSystems.stream().filter(it -> Boolean.TRUE.equals(it.getUseAi()))
                .map(RefrigeratingSystem::getRoomId).collect(Collectors.toList());
        List<Map<String, Object>> maps = modelServiceUtils.queryWithChildren(NodeLabelDef.ROOM, roomIds, Arrays.asList(NodeLabelDef.COLD_WATER_MAINENGINE, NodeLabelDef.COOLING_TOWER,
                NodeLabelDef.PUMP));
        List<BaseVo> baseVos = JsonTransferUtils.transferList(maps, BaseVo.class);
        assembleTreeId(baseVos);
        return baseVos;
    }

    @Override
    public AiEnergyResult tbhbEnergyData(AiConsumptionParam energyParam, Long userId) {
        AiEnergyResult result = new AiEnergyResult();
        //先判断是能耗还是cop，再判断要不要ai
        if (Objects.equals(energyParam.getAnalysisIndicatorType(), AnalysisTypeDef.energy)) {
            //能耗
            if (!Objects.equals(NEED_AI, energyParam.getQueryType())) {
                //不需要ai，还是使用原来的逻辑
                Result<EnergyResult> tbhbEnergyData = oilFieldService.getTbhbEnergyData(energyParam, userId);
                BeanUtils.copyProperties(tbhbEnergyData.getData(), result);
            }
        } else {
            //cop--能耗/冷量
            if (!Objects.equals(NEED_AI, energyParam.getQueryType())) {
                //不需要ai
                return getTbHbEnergyData(energyParam);
            }
        }
        return result;
    }


    @Override
    public List<AiTbHbEnergyVo> getTbHbEnergy(AiConsumptionSearchVo searchVo) {
        List<AiTbHbEnergyVo> result = new ArrayList<>();
        //先判断是能耗还是cop，再判断要不要ai
        if (Objects.equals(searchVo.getAnalysisIndicatorType(), AnalysisTypeDef.energy)) {
            //能耗
            if (!Objects.equals(NEED_AI, searchVo.getQueryType())) {
                //不需要ai，还是使用原来的逻辑
                List<EnergyWithTbHbVo> energy = energyConsumptionBffService.queryEnergyWithTbHb(searchVo);
                return JsonTransferUtils.transferList(energy, AiTbHbEnergyVo.class);

            }
        } else {
            //cop--能耗/冷量
            if (!Objects.equals(NEED_AI, searchVo.getQueryType())) {
                //不需要ai
                return queryAiTbHbEnergyVo(searchVo);
            }
        }
        return result;
    }

    @Override
    public void exportEnergyData(Integer analysisType, AiConsumptionParam energyParam, HttpServletResponse response, Long userId) throws IOException {
        //先判断是能耗还是cop，再判断要不要ai
        if (Objects.equals(energyParam.getAnalysisIndicatorType(), AnalysisTypeDef.energy)) {
            //能耗
            if (!Objects.equals(NEED_AI, energyParam.getQueryType())) {
                //不需要ai，还是使用原来的逻辑
                oilFieldService.exportEnergyData(analysisType, energyParam, response, GlobalInfoUtils.getUserId());

            }
        } else {
            //cop--能耗/冷量
            if (!Objects.equals(NEED_AI, energyParam.getQueryType())) {
                //不需要ai
                exportEnergyWithCopData(analysisType, energyParam, response, GlobalInfoUtils.getUserId());
            }
        }

    }

    @Override
    public List<CompareResult> compareEnergyWithCopData(AiConsumptionParam energyParam, Long userId) {
        //先判断是能耗还是cop，再判断要不要ai
        if (Objects.equals(energyParam.getAnalysisIndicatorType(), AnalysisTypeDef.energy)) {
            //能耗
            return oilFieldService.compareEnergyData(energyParam, userId).getData();


        } else {
            //cop--能耗/冷量
            return compareCop(energyParam, userId);
        }

    }

    private void handleUnnaturalTimeAndCycle(AiConsumptionParam energyParam) {
        Integer cycle = energyParam.getCycle();
        UnnaturalSetVo unnaturalSet = unnaturalTimeUtils.getUnnaturalSetVo(energyParam.getProjectId(), cycle);
        energyParam.setStartTime(unnaturalTimeUtils.convertUnnaturalTime(cycle, energyParam.getStartTime(), unnaturalSet));
        energyParam.setEndTime(unnaturalTimeUtils.convertUnnaturalTime(cycle, energyParam.getEndTime(), unnaturalSet));
        energyParam.setCycle(ParamUtils.getNextCycle(cycle));
    }

    private List<CompareResult> compareCop(AiConsumptionParam energyParam, Long userId) {
        List<NodeVo> nodes = energyParam.getNode();
        nodes = nodeAuthBffService.filterNodes(nodes, userId);
        if (CollectionUtils.isEmpty(nodes)) {
            return Collections.emptyList();
        }


        List<CompareResult> result = new ArrayList<>();
        // 处理能源类型查询
        List<Integer> energyTypes = Arrays.asList(EnergyTypeDef.ELECTRIC, EnergyTypeDef.COLD);
        handleUnnaturalTimeAndCycle(energyParam);
        //自定义时间类型需要根据查询起止时间指定查询间隔
        int cycle = energyParam.getCycle();

        long st = energyParam.getStartTime();
        long et = energyParam.getEndTime();

        assembleCompareResult(nodes, cycle, st, et, energyTypes, result);
        List<Date> axisTimes = TimeCoordinate.generateTimeValueOfPeriod(new Date(st), new Date(et), cycle);
        for (CompareResult compareResult : result) {
            compareResult.setData(DataCompareUtils.generateCompareDataList(cycle, axisTimes, compareResult.getData(), QueryType.CURRENT));
        }

        return result;
    }

    private void assembleCompareResult(List<NodeVo> nodes, int cycle, long st, long et, List<Integer> energyTypes, List<CompareResult> result) {

        for (NodeVo node : nodes) {
            List<Long> ids = node.getNodes().stream().map(BaseVo::getId).collect(Collectors.toList());
            List<EnergyConsumption> consumption = energyConsumptionDao.queryEnergyConsumption(ids, node.getModelLabel(),
                    st, et, cycle, energyTypes);
            Map<Long, List<EnergyConsumption>> tmpMap = consumption.stream().collect(Collectors.groupingBy(EnergyConsumption::getObjectid));

            node.getNodes().forEach(it -> {
                List<EnergyConsumption> tmpList = tmpMap.get(it.getId());
                List<EnergyConsumption> consumptions = calculateCop(tmpList , st, et, cycle);

                CompareResult tmpResult = new CompareResult(node.getModelLabel(), it.getId(), it.getName());
                result.add(tmpResult);

                if (CollectionUtils.isEmpty(consumptions)) {
                    return;
                }

                for (EnergyConsumption c : consumptions) {
                    tmpResult.getData().add(new DataLogData(c.getLogtime(), c.getUsage()));
                }

            });
        }
    }

    /**
     * 根据时间戳获取文件的名称
     *
     * @param st 开始时间
     * @param et 结束时间
     * @return
     */
    private String getFileNameFromDate(long st, long et, Integer cycle) {
        if (Objects.equals(cycle, AggregationCycle.ONE_YEAR)) {
            return "(" + DateUtils.formatDate(st, DateUtils.YYYY_MM)
                    + "_" + DateUtils.formatDate(et, DateUtils.YYYY_MM) + ")";
        }
        return "(" + DateUtils.formatDate(st, DateUtils.DATE_TO_STRING_SHORT_PATTERN)
                + "_" + DateUtils.formatDate(et, DateUtils.DATE_TO_STRING_SHORT_PATTERN) + ")";
    }

    private void exportEnergyWithCopData(Integer analysisType, AiConsumptionParam energyParam, HttpServletResponse response, Long userId) throws IOException {
        //获取查询开始时间和结束的时间
        UnnaturalSetVo unnaturalSet = unnaturalTimeUtils.getUnnaturalSetVo(energyParam.getProjectId(), energyParam.getCycle());
        long start = unnaturalTimeUtils.convertUnnaturalTime(energyParam.getCycle(), energyParam.getStartTime(), unnaturalSet);
        long end = unnaturalTimeUtils.convertUnnaturalTime(energyParam.getCycle(), energyParam.getEndTime(), unnaturalSet);

        //获取基础模型的名字
        String excelName = energyParam.getNode().get(0).getNodes().get(0).getName();

        switch (analysisType) {
            case QueryType.THB:
                if (StringUtils.isEmpty(excelName)) {
                    excelName = "能耗数据";
                }
                excelName = excelName + getFileNameFromDate(start, end, energyParam.getCycle());
                AiEnergyResult result = tbhbEnergyData(energyParam, userId);
                assembleEnergyWithCopData(energyParam, response,
                        excelName, result);
                break;

            case QueryType.NODES_COMPARE:
                excelName = "节点对比能耗";
                excelName = excelName + getFileNameFromDate(start, end, energyParam.getCycle());
                List<CompareResult> compareResults = compareCop(energyParam, userId);
                EnergyExportUtils.exportCompareData(compareResults, energyParam.getCycle(), response, excelName);
                break;
            default:
                break;
        }
    }

    private void assembleEnergyWithCopData(AiConsumptionParam energyParam, HttpServletResponse response,
                                           String excelName, AiEnergyResult result) throws IOException {
        try(Workbook workBook = PoiExcelUtils.createWorkBook(ExcelType.BIG_DATA);) {
            List<Integer> colWidth = Arrays.asList(18, 18, 18, 18, 18, 18, 18);
            PoiExcelUtils.createSheet(workBook, excelName, (sheet, baseCellStyle, rowIndex) -> {
                int rowNum = 0;

                writeHeaderEnergyTbHb(sheet, baseCellStyle, rowNum++, energyParam.getQueryType(), result.getSymbol());
                writeDataEnergyTbHb(sheet, baseCellStyle, rowNum, result, energyParam.getQueryType());


            }, colWidth);
            FileUtils.downloadExcel(response, workBook, excelName, CommonUtils.APPLICATION_MSEXCEL);
        } catch (Exception e) {
            log.info("导出异常", e);
            throw new ValidationException(e.getMessage());
        }
    }

    private void writeDataEnergyTbHb(Sheet sheet, CellStyle baseCellStyle, int rowNum, AiEnergyResult result, int queryType) {
        int col;
        List<DataLogData> currentData = result.getCurrentdata();
        List<DataLogData> aiData = result.getAidata();
        List<DataLogData> hbData = result.getHbdata();
        List<DataLogData> tbData = result.getTbdata();
        int i = 0;
        for (DataLogData item : currentData) {
            col = 0;
            Row row = PoiExcelUtils.createRow(sheet, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, TimeUtil.format(item.getTime(), TimeUtil.LONG_TIME_FORMAT));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, EemCommonUtils.formatDouble(item.getValue(), EemCommonUtils.PRECISION_2, EemCommonUtils.BLANK_STR));
            if ((queryType == QueryType.YEAR_ON_YEAR || queryType == QueryType.YEAR_AND_MONTH) && tbData.size() > i) {
                PoiExcelUtils.createCell(row, col++, baseCellStyle,
                        EemCommonUtils.formatDouble(tbData.get(i).getValue(), EemCommonUtils.PRECISION_2, EemCommonUtils.BLANK_STR));

            }
            if ((queryType == QueryType.MONTH_ON_MONTH || queryType == QueryType.YEAR_AND_MONTH) && hbData.size() > i) {
                PoiExcelUtils.createCell(row, col++, baseCellStyle,
                        EemCommonUtils.formatDouble(hbData.get(i).getValue(), EemCommonUtils.PRECISION_2, EemCommonUtils.BLANK_STR));
            }
            if (Objects.equals(queryType, NEED_AI) && aiData.size() > 1) {
                PoiExcelUtils.createCell(row, col, baseCellStyle,
                        EemCommonUtils.formatDouble(aiData.get(i).getValue(), EemCommonUtils.PRECISION_2, EemCommonUtils.BLANK_STR));
            }
            rowNum++;
            i++;
        }
    }

    private void writeHeaderEnergyTbHb(Sheet sheet, CellStyle baseCellStyle, int startRow, int queryType, String unit) {
        if (Objects.isNull(unit)) {
            unit = "";
        } else {
            unit = String.format("(%s)", unit);
        }
        LinkedHashMap<String, CellStyle> headerMap = new LinkedHashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        headerMap.put("序号", baseCellStyle);
        headerMap.put("时间", baseCellStyle);
        headerMap.put("当前数据" + unit, baseCellStyle);
        if (queryType == QueryType.YEAR_ON_YEAR || queryType == QueryType.YEAR_AND_MONTH) {
            headerMap.put("同比数据" + unit, baseCellStyle);
        }

        if (queryType == QueryType.MONTH_ON_MONTH || queryType == QueryType.YEAR_AND_MONTH) {

            headerMap.put("环比数据" + unit, baseCellStyle);
        }
        if (Objects.equals(queryType, NEED_AI)) {
            headerMap.put("AI运行时间占比", baseCellStyle);
        }
        PoiExcelUtils.createHeaderName(sheet, startRow, headerMap);
    }

    private void handleTime(ConsumptionSearchVo searchVo) {
        int originCycle = searchVo.getCycle();
        UnnaturalSetVo unnaturalSet = unnaturalTimeUtils.getUnnaturalSetVo(searchVo.getProjectId(), originCycle);
        long st = unnaturalTimeUtils.convertUnnaturalTime(originCycle, TimeUtil.localDateTime2timestamp(searchVo.getStartTime()), unnaturalSet);
        long et = unnaturalTimeUtils.convertUnnaturalTime(originCycle, TimeUtil.localDateTime2timestamp(searchVo.getEndTime()), unnaturalSet);

        long nowTime = System.currentTimeMillis();
        if (nowTime >= st && nowTime < et) {
            if (originCycle == AggregationCycle.ONE_YEAR || originCycle == AggregationCycle.ONE_MONTH) {
                et = TimeUtil.getFirstTimeOfDay(nowTime);
            } else {
                et = TimeUtil.localDateTime2timestamp(TimeUtil.getFirstTimeOfHour(LocalDateTime.now()));
            }
        }
        int cycle = 0;
        if (originCycle == AggregationCycle.ONE_YEAR || originCycle == AggregationCycle.ONE_MONTH) {
            cycle = AggregationCycle.ONE_DAY;
        } else {
            cycle = AggregationCycle.ONE_HOUR;
        }
        searchVo.setStartTime(TimeUtil.timestamp2LocalDateTime(st));
        searchVo.setEndTime(TimeUtil.timestamp2LocalDateTime(et));
        searchVo.setCycle(cycle);
    }

    public List<AiTbHbEnergyVo> queryAiTbHbEnergyVo(AiConsumptionSearchVo searchVo) {

        int originCycle = searchVo.getCycle();
        handleTime(searchVo);
        long st = TimeUtil.localDateTime2timestamp(searchVo.getStartTime());
        long et = TimeUtil.localDateTime2timestamp(searchVo.getEndTime());
        List<BaseVo> nodes = JsonTransferUtils.transferList(searchVo.getNodes(), BaseVo.class);
        List<Integer> energyTypes = Collections.singletonList(searchVo.getEnergyType());
        if (Objects.equals(searchVo.getAnalysisIndicatorType(), AnalysisTypeDef.cop)) {
            energyTypes = Arrays.asList(EnergyTypeDef.ELECTRIC, EnergyTypeDef.COLD);
        }
        int cycle = searchVo.getCycle();
        // 查询当周期能耗
        Map<BaseVo, AiTbHbEnergyVo> tmpResult = queryCurrentConsumption(nodes, cycle, st, et,
                energyTypes, searchVo.getAnalysisIndicatorType().equals(AnalysisTypeDef.cop));

        switch (searchVo.getQueryType()) {
            case QueryType.YEAR_ON_YEAR:
                // 同比
                queryTbHbConsumption(nodes, originCycle, cycle, st, et, energyTypes, tmpResult, true, searchVo.getAnalysisIndicatorType().equals(AnalysisTypeDef.cop));

                break;
            case QueryType.MONTH_ON_MONTH:
                // 环比
                queryTbHbConsumption(nodes, originCycle, cycle, st, et, energyTypes, tmpResult, false, searchVo.getAnalysisIndicatorType().equals(AnalysisTypeDef.cop));
                break;
            case QueryType.YEAR_AND_MONTH:
                // 同比
                queryTbHbConsumption(nodes, originCycle, cycle, st, et, energyTypes, tmpResult, true, searchVo.getAnalysisIndicatorType().equals(AnalysisTypeDef.cop));
                // 环比
                queryTbHbConsumption(nodes, originCycle, cycle, st, et, energyTypes, tmpResult, false, searchVo.getAnalysisIndicatorType().equals(AnalysisTypeDef.cop));
                break;
            default:
                break;
        }

        List<AiTbHbEnergyVo> result = new ArrayList<>(tmpResult.values());
        assembleAiTbHbEnergyVo(searchVo, result);
        return result;
    }

    private void assembleAiTbHbEnergyVo(AiConsumptionSearchVo searchVo, List<AiTbHbEnergyVo> result) {

        for (EnergyWithTbHbVo data : result) {
            if (Objects.equals(searchVo.getAnalysisIndicatorType(), AnalysisTypeDef.cop)) {
                return;
            }
            Double max = CommonUtils.getMax(data.getValue(), data.getHbValue(), data.getTbValue());
            UserDefineUnit unit = energyUnitService.queryUnitCoef(new UserDefineUnitSearchDto(searchVo.getProjectId(), searchVo.getEnergyType(), ProjectUnitClassify.ENERGY), max, GlobalInfoUtils.getProjectId());
            data.setEnergyTypeName(unit.getTypeName());
            data.setSymbol(unit.getUnitEn());

            data.setValue(CommonUtils.calcDouble(data.getValue(), unit.getCoef(), EnumOperationType.DIVISION.getId()));
            data.setHbValue(CommonUtils.calcDouble(data.getHbValue(), unit.getCoef(), EnumOperationType.DIVISION.getId()));
            data.setTbValue(CommonUtils.calcDouble(data.getTbValue(), unit.getCoef(), EnumOperationType.DIVISION.getId()));
        }
    }

    private void assembleData(List<BaseVo> nodes, Map<BaseVo, AiTbHbEnergyVo> tmpResult, long tbSt, long tbEt, Boolean ifTb, Map<BaseVo, Double> tbConsumptionMap) {
        for (BaseVo node : nodes) {
            EnergyWithTbHbVo obj = tmpResult.get(node);
            Double val = tbConsumptionMap.get(node);
            if (Boolean.TRUE.equals(ifTb)) {
                obj.setTbValue(val);
                obj.setTbStartTime(TimeUtil.timestamp2LocalDateTime(tbSt));
                obj.setTbEndTime(TimeUtil.timestamp2LocalDateTime(tbEt));
            } else {
                obj.setHbValue(val);
                obj.setHbStartTime(TimeUtil.timestamp2LocalDateTime(tbSt));
                obj.setHbEndTime(TimeUtil.timestamp2LocalDateTime(tbEt));
            }

        }
    }

    private void queryTbHbConsumption(List<BaseVo> nodes, int originCycle, int cycle, long st, long et,
                                      List<Integer> energyTypes, Map<BaseVo, AiTbHbEnergyVo> tmpResult, Boolean ifTb, Boolean isCop) {
        long startTime;
        long endTime;
        if (Boolean.TRUE.equals(ifTb)) {
            startTime = TimeUtils.convertTime(st, originCycle, QueryType.YEAR_ON_YEAR);
            endTime = TimeUtils.convertTime(et, originCycle, QueryType.YEAR_ON_YEAR);
        } else {
            startTime = TimeUtils.convertTime(st, originCycle, QueryType.MONTH_ON_MONTH);
            endTime = TimeUtils.convertTime(et, originCycle, QueryType.MONTH_ON_MONTH);
        }

        List<EnergyConsumption> tbConsumptions = energyConsumptionDao.queryEnergyConsumption(nodes, startTime, endTime, cycle, energyTypes);
        Map<BaseVo, Double> tbConsumptionMap = countConsumptionByNode(tbConsumptions, isCop);
        assembleData(nodes, tmpResult, startTime, endTime, ifTb, tbConsumptionMap);
    }

    private Map<BaseVo, AiTbHbEnergyVo> queryCurrentConsumption(List<BaseVo> nodes, int cycle, long st, long et, List<Integer> energyTypes, Boolean isCop) {
        Map<BaseVo, AiTbHbEnergyVo> tmpResult = new HashMap<>(nodes.size());
        List<EnergyConsumption> currentConsumptions = energyConsumptionDao.queryEnergyConsumption(nodes, st, et, cycle, energyTypes);
        Map<BaseVo, Double> currentConsumptionMap = countConsumptionByNode(currentConsumptions, isCop);
        for (BaseVo node : nodes) {
            AiTbHbEnergyVo obj = new AiTbHbEnergyVo();
            tmpResult.put(node, obj);
            obj.setMonitoredId(node.getId());

            obj.setMonitoredLabel(node.getModelLabel());
            Double val = currentConsumptionMap.get(node);
            obj.setValue(val);
            obj.setStartTime(TimeUtil.timestamp2LocalDateTime(st));
            obj.setEndTime(TimeUtil.timestamp2LocalDateTime(et));
        }

        return tmpResult;
    }

    private Map<BaseVo, Double> countConsumptionByNode(List<EnergyConsumption> dataList, Boolean isCop) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyMap();
        }
        Map<BaseVo, List<EnergyConsumption>> map = dataList.stream().collect(Collectors.groupingBy(it -> new BaseVo(it.getObjectid(), it.getObjectlabel())));
        Map<BaseVo, Double> result = new HashMap<>(map.size());
        if (Boolean.FALSE.equals(isCop)) {
            for (BaseVo baseVo : map.keySet()) {
                result.put(baseVo, map.get(baseVo).stream().filter(it -> it.getUsage() != null).mapToDouble(EnergyConsumption::getUsage).sum());
            }
            return result;
        }

        for (Map.Entry<BaseVo, List<EnergyConsumption>> entry : map.entrySet()) {
            List<EnergyConsumption> value = entry.getValue();
            if (CollectionUtils.isEmpty(value)) {
                continue;
            }
            Map<Integer, List<EnergyConsumption>> energyConsumptionMap = value.stream().collect(Collectors.groupingBy(EnergyConsumption::getEnergytype));
            List<EnergyConsumption> electric = energyConsumptionMap.get(EnergyTypeDef.ELECTRIC);
            List<EnergyConsumption> cold = energyConsumptionMap.get(EnergyTypeDef.COLD);
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(electric) || org.apache.commons.collections4.CollectionUtils.isEmpty(cold)) {
                continue;
            }

            double coldData = cold.stream().filter(it -> it.getUsage() != null).mapToDouble(EnergyConsumption::getUsage).sum();
            Double aDouble = CommonUtils.calcDouble(CommonUtils.calcDouble(coldData,UNIT,EnumOperationType.DIVISION.getId()),
                    electric.stream().filter(it -> it.getUsage() != null).mapToDouble(EnergyConsumption::getUsage).sum(), EnumOperationType.DIVISION.getId());
            result.put(entry.getKey(), aDouble);
        }

        return result;
    }

    private void assembleTreeId(List<BaseVo> baseVos) {
        if (CollectionUtils.isEmpty(baseVos)) {
            return;
        }
        for (BaseVo baseVo : baseVos) {
            baseVo.setTreeId(baseVo.getModelLabel() + DOWN + baseVo.getId());
            assembleTreeId(baseVo.getChildren());
        }
    }

    private AiEnergyResult getTbHbEnergyData(AiConsumptionParam energyParam) {
        long id = energyParam.getNode().get(0).getNodes().get(0).getId();
        String modelLabel = energyParam.getNode().get(0).getModelLabel();
        List<Long> ids = Stream.of(id).collect(Collectors.toList());
        List<Integer> energyTypes = Stream.of(energyParam.getEnergyType()).collect(Collectors.toList());
        if (Objects.equals(energyParam.getAnalysisIndicatorType(), AnalysisTypeDef.cop)) {
            energyTypes = Arrays.asList(EnergyTypeDef.ELECTRIC, EnergyTypeDef.COLD);
        }
        int cycle = energyParam.getCycle();
        UnnaturalSetVo unnaturalSet = unnaturalTimeUtils.getUnnaturalSetVo(energyParam.getProjectId(), cycle);
        long st = unnaturalTimeUtils.convertUnnaturalTime(cycle, energyParam.getStartTime(), unnaturalSet);
        long et = unnaturalTimeUtils.convertUnnaturalTime(cycle, energyParam.getEndTime(), unnaturalSet);
        Integer analysisIndicatorType = energyParam.getAnalysisIndicatorType();

        AiEnergyResult energyResult = new AiEnergyResult();
        //插入能源类型
        energyResult.setEnergytype(energyParam.getEnergyType());
        //查询时间段数据
        List<DataLogData> currentdata = queryPeriodData(ids, modelLabel, st, et,
                cycle, energyTypes, QueryType.CURRENT, energyParam.getCycle() == QueryType.CUSTOM_QUERY, analysisIndicatorType);
        DataCompareUtils.generateEnergyData(currentdata, energyResult, QueryType.CURRENT);
        switch (energyParam.getQueryType()) {
            case QueryType.YEAR_ON_YEAR:
                //同比
                handleYearOnYearData(st, et, cycle, ids, modelLabel, energyTypes, energyResult,
                        energyParam.getQueryType(), analysisIndicatorType);
                break;
            case QueryType.MONTH_ON_MONTH:
                //环比
                handleChainData(st, et, cycle, ids, modelLabel, energyTypes,
                        energyResult, energyParam.getQueryType(), analysisIndicatorType);
                break;
            case QueryType.YEAR_AND_MONTH:
                //同比
                handleYearOnYearData(st, et, cycle, ids, modelLabel, energyTypes, energyResult,
                        QueryType.YEAR_ON_YEAR, analysisIndicatorType);
                //环比
                handleChainData(st, et, cycle, ids, modelLabel, energyTypes,
                        energyResult, QueryType.MONTH_ON_MONTH, analysisIndicatorType);
                break;
            default:
                break;
        }
        if (Objects.equals(analysisIndicatorType, AnalysisTypeDef.energy)) {
            unitUtils.addTbhbUnit(energyParam.getProjectId(), energyParam.getEnergyType(), energyResult);
        }
        return energyResult;
    }

    /**
     * 处理环比数据
     *
     * @param ids
     * @param modelLabel
     * @param energyTypes
     * @param energyResult
     */
    private void handleChainData(long st, long et, int cycle, List<Long> ids, String modelLabel, List<Integer> energyTypes,
                                 EnergyResult energyResult, int queryType, Integer analysisIndicatorType) {
        List<DataLogData> hbdata = queryPeriodData(ids, modelLabel, st, et,
                cycle, energyTypes, queryType, false, analysisIndicatorType);
        DataCompareUtils.generateEnergyData(hbdata, energyResult, queryType);
    }

    /**
     * 处理同比数据
     *
     * @param ids
     * @param modelLabel
     * @param energyTypes
     * @param energyResult
     */
    private void handleYearOnYearData(long st, long et, int cycle, List<Long> ids, String modelLabel, List<Integer> energyTypes,
                                      EnergyResult energyResult, int queryType, Integer analysisIndicatorType) {
        List<DataLogData> tbdata = queryPeriodData(ids, modelLabel, st, et,
                cycle, energyTypes, queryType, false, analysisIndicatorType);
        DataCompareUtils.generateEnergyData(tbdata, energyResult, queryType);
    }

    public List<DataLogData> queryPeriodData(List<Long> ids, String modelLabel, long st, long et, Integer cycle, List<Integer> energyTypes,
                                             int queryType, boolean isDefined, Integer analysisIndicatorType) {
        long startTime = TimeUtil.convertTime(st, cycle, queryType);
        long endTime = TimeUtil.convertTime(et, cycle, queryType);
        if (cycle == AggregationCycle.ONE_DAY) {
            if (startTime == endTime) {
                endTime = TimeUtil.addDateTimeByCycle(startTime, cycle, 1);
            }
        }
        int nextCycle = cycle;
        // 判断是否为自定义的
        if (!isDefined) {
            // 分析周期 -> 要查询单条记录的aggregationCycle字段
            nextCycle = ParamUtils.getNextCycle(cycle);
        }

        List<EnergyConsumption> consumption = energyConsumptionDao.queryEnergyConsumption(ids, modelLabel,
                startTime, endTime, nextCycle, energyTypes);
        if (Objects.equals(analysisIndicatorType, AnalysisTypeDef.cop)) {
            consumption = calculateCop(consumption, startTime, endTime, nextCycle);
        }
        //能耗数据坐标统一
        List<Date> compareTimes = TimeCoordinate.generateTimeValueOfPeriod(new Date(st), new Date(et), nextCycle);
        return DataCompareUtils.generateNodesCompareDataList(nextCycle, compareTimes, consumption, queryType, EnergyDataType.USAGE);
    }

    private List<EnergyConsumption> calculateCop(List<EnergyConsumption> consumption, long st, long et, Integer cycle) {
        if (CollectionUtils.isEmpty(consumption)){
            return Collections.emptyList();
        }
        List<EnergyConsumption> result = new ArrayList<>();
        List<Long> timeRange = TimeUtil.getTimeRange(st, et, cycle);
        Map<Long, List<EnergyConsumption>> consumptionMap = consumption.stream().collect(Collectors.groupingBy(EnergyConsumption::getLogtime));
        for (Long time : timeRange) {
            List<EnergyConsumption> consumptions = consumptionMap.get(time);
            if (CollectionUtils.isEmpty(consumptions)) {
                continue;
            }
            Map<Integer, EnergyConsumption> energyConsumptionMap = consumptions.stream()
                    .collect(Collectors.toMap(EnergyConsumption::getEnergytype, energyConsumption -> energyConsumption, (v1, v2) -> v1));
            EnergyConsumption electric = energyConsumptionMap.get(EnergyTypeDef.ELECTRIC);
            EnergyConsumption cold = energyConsumptionMap.get(EnergyTypeDef.COLD);
            if (Objects.isNull(electric) || Objects.isNull(cold)) {
                continue;
            }
            EnergyConsumption energyConsumption = new EnergyConsumption();
            BeanUtils.copyProperties(electric, energyConsumption);
            energyConsumption.setTotal(CommonUtils.calcDouble(CommonUtils.calcDouble(cold.getUsage(), UNIT, EnumOperationType.DIVISION.getId()),
                    electric.getUsage(), EnumOperationType.DIVISION.getId()));
            result.add(energyConsumption);
        }
        return result;
    }


}