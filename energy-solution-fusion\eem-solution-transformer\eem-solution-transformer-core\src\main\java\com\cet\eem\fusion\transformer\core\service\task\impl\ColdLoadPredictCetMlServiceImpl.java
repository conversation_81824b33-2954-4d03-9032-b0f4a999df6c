package com.cet.eem.fusion.transformer.core.service.task.impl;

import com.cet.eem.bll.common.dao.project.ProjectDao;
import com.cet.eem.bll.common.model.domain.object.organization.Project;
import com.cet.eem.bll.common.model.domain.subject.energysaving.ColdPredict;
import com.cet.eem.bll.energysaving.dao.aioptimization.AiStartStopStrategyDao;
import com.cet.eem.bll.energysaving.dao.aioptimization.StrategyObjectMapDao;
import com.cet.eem.bll.energysaving.dao.weather.ColdPredictDao;
import com.cet.eem.bll.energysaving.dao.weather.RefrigeratingSystemDao;
import com.cet.eem.bll.energysaving.feign.CetmlNewFeignService;
import com.cet.eem.bll.energysaving.handle.optimizationstrategy.FaultDiagnosis;
import com.cet.eem.bll.energysaving.model.aioptimization.AiStartStopStrategy;
import com.cet.eem.bll.energysaving.model.aioptimization.StrategyObjectMap;
import com.cet.eem.bll.energysaving.model.aioptimization.StrategyTypeDef;
import com.cet.eem.bll.energysaving.model.cetml.*;
import com.cet.eem.bll.energysaving.model.config.system.RefrigeratingSystem;
import com.cet.eem.bll.energysaving.model.weather.ColdLoadType;
import com.cet.eem.bll.energysaving.model.weather.PredictDataType;
import com.cet.eem.bll.energysaving.service.predict.CetMlPredictService;
import com.cet.eem.bll.energysaving.service.predict.LgbModelPredictService;
import com.cet.eem.bll.energysaving.service.task.ColdLoadPredictCetMlService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.Result;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.tool.ModelServiceUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName : ColdPredictDataServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-06-08 14:12
 */
@Slf4j
@Service
public class ColdLoadPredictCetMlServiceImpl implements ColdLoadPredictCetMlService {
    @Value("${cet.eem.task.energy-saving.endCold.startTime}")
    private String startTime;
    @Value("${cet.eem.task.energy-saving.mainControl.startTime}")
    private String startTimeOfMainControl;
    @Value("${cet.eem.task.energy-saving.copFit.startTime}")
    private String startTimeOfCop;
    @Value("${cet.eem.task.energy-saving.pumpFit.startTime}")
    private String startTimeOfPump;
    @Autowired
    RefrigeratingSystemDao refrigeratingSystemDao;
    @Autowired
    LgbModelPredictService lgbModelPredictService;
    public static final Integer COLD_LOAD_POI_TYPE = 21;
    public static final Integer MAIN_CONTROL_POI_TYPE = 22;
    public static final Integer COP_FIT_POI_TYPE = 23;
    public static final Integer PUMP_FIT_POI_TYPE = 24;
    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    ProjectDao projectDao;
    @Autowired
    ColdPredictDao coldPredictDao;
    @Autowired
    CetMlPredictService cetMlPredictService;
    @Autowired
    CetmlNewFeignService cetmlNewFeignService;
    @Autowired
    AiStartStopStrategyDao aiStartStopStrategyDao;
    @Autowired
    StrategyObjectMapDao strategyObjectMapDao;
    @Autowired
    FaultDiagnosis faultDiagnosis;
    private static final String COLD_LOG_KEY = "[冷负荷预测数据转存]";
    private static final String MAIN_CONTROL_LOG_KEY = "[冷机控制数据转存]";
    private static final String COP_FIT_LOG_KEY = "[cop拟合]";
    private static final String PUMP_FIT_LOG_KEY = "[冷冻泵功率和总管流量拟合]";

    @Override
    public void saveColdPredictData() {
        log.info(COLD_LOG_KEY + "开始转存冷负荷预测结果数据。");
        long beginTime = System.currentTimeMillis();


        long count = 0;

        try {
            count = queryEndColdPredictData(COLD_LOAD_POI_TYPE);
        } catch (Exception e) {
            log.error(COLD_LOG_KEY + "[转存冷负荷预测结果数据]转存冷负荷预测结果异常", e);
        }

        long overTime = System.currentTimeMillis();
        log.info(COLD_LOG_KEY + "本次转存冷负荷预测结果数据：{}，耗时(s)：{}", count, (overTime - beginTime) * CommonUtils.DOUBLE_CONVERSION_COEFFICIENT / 1000);
        log.info(COLD_LOG_KEY + "结束转存冷负荷预测结果数据。");
    }

    private Map<Long, Project> assembleRoomIdWithProject() {
        List<Project> projects = projectDao.selectAll();
        if (CollectionUtils.isEmpty(projects)) {
            return Collections.emptyMap();
        }
        Map<Long, Project> map = new HashMap<>();
        List<Long> ids = projects.stream().map(Project::getId).collect(Collectors.toList());
        List<Map<String, Object>> maps = modelServiceUtils.queryWithChildren(NodeLabelDef.PROJECT, ids, Collections.singletonList(NodeLabelDef.ROOM));
        List<BaseVo> baseVos = JsonTransferUtils.transferList(maps, BaseVo.class);
        for (BaseVo baseVo : baseVos) {
            if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                for (BaseVo baseVo1 : baseVo.getChildren()) {
                    Project project1 = projects.stream().filter(project -> Objects.equals(project.getId(), baseVo.getId())).findAny().orElse(new Project());
                    map.put(baseVo1.getId(), project1);
                }
            }
        }
        return map;
    }

    @Override
    public void saveColdMainControlPredictData() {
        log.info(MAIN_CONTROL_LOG_KEY + "开始转存冷机控制数据结果数据。");
        long beginTime = System.currentTimeMillis();
        long count = 0;

        try {
            count = queryEndColdPredictData(MAIN_CONTROL_POI_TYPE);
        } catch (Exception e) {
            log.error(MAIN_CONTROL_LOG_KEY + "[转存冷机控制数据结果数据]转存转存管道预测结果异常", e);
        }

        long overTime = System.currentTimeMillis();
        log.info(MAIN_CONTROL_LOG_KEY + "本次转存冷机控制数据结果数据：{}，耗时(s)：{}", count, (overTime - beginTime) * CommonUtils.DOUBLE_CONVERSION_COEFFICIENT / 1000);
        log.info(MAIN_CONTROL_LOG_KEY + "结束转存冷机控制数据结果数据。");
    }

    @Override
    public void saveCopFittingPredictData() {
        //先不写转存，只是执行拟合算法的调用
        log.info(COP_FIT_LOG_KEY + "开始执行cop拟合模型更新。");
        long beginTime = System.currentTimeMillis();
        long count = 0;

        try {
            count = queryEndColdPredictData(COP_FIT_POI_TYPE);
        } catch (Exception e) {
            log.error(COP_FIT_LOG_KEY + "异常", e);
        }

        long overTime = System.currentTimeMillis();
        log.info(COP_FIT_LOG_KEY + "本次执行cop拟合模型更新：{}，耗时(s)：{}", count, (overTime - beginTime) * CommonUtils.DOUBLE_CONVERSION_COEFFICIENT / 1000);
        log.info(COP_FIT_LOG_KEY + "结束执行cop拟合模型更新。");
    }

    @Override
    public void savePumpFitPredictData() {
        //先不写转存，只是执行拟合算法的调用
        log.info(PUMP_FIT_LOG_KEY + "开始执行冷冻泵功率和总管流量拟合模型更新。");
        long beginTime = System.currentTimeMillis();
        long count = 0;

        try {
            count = queryEndColdPredictData(PUMP_FIT_POI_TYPE);
        } catch (Exception e) {
            log.error(PUMP_FIT_LOG_KEY + "异常", e);
        }

        long overTime = System.currentTimeMillis();
        log.info(PUMP_FIT_LOG_KEY + "本次执行冷冻泵功率和总管流量拟合模型更新：{}，耗时(s)：{}", count, (overTime - beginTime) * CommonUtils.DOUBLE_CONVERSION_COEFFICIENT / 1000);
        log.info(PUMP_FIT_LOG_KEY + "结束执行冷冻泵功率和总管流量拟合模型更新。");
    }

    /**
     * 解析全局开始时间
     *
     * @return 全局开始时间
     */
    public LocalDateTime parsePercentStartTime(String startTime) {
        if (StringUtils.isBlank(startTime)) {
            return getLastFifTime(LocalDateTime.now());
        }
        return TimeUtil.parse(startTime, TimeUtil.LONG_TIME_FORMAT);

    }

    private LocalDateTime getStartTimeByType(Integer poiType) {
        if (Objects.equals(poiType, COLD_LOAD_POI_TYPE)) {
            return parsePercentStartTime(startTime);
        } else if (Objects.equals(poiType, MAIN_CONTROL_POI_TYPE)) {
            return parsePercentStartTime(startTimeOfMainControl);
        }else if (Objects.equals(PUMP_FIT_POI_TYPE,poiType)){
            return parsePercentStartTime(startTimeOfPump);
        }
        return parsePercentStartTime(startTimeOfCop);

    }


    /**
     * @return
     */
    private long queryEndColdPredictData(Integer poiType) throws Exception {
        LocalDateTime time = getStartTimeByType(poiType);
        List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.selectAll();
        if (CollectionUtils.isEmpty(refrigeratingSystems)) {
            return 0;
        }
        refrigeratingSystems = refrigeratingSystems.stream().filter(it ->
                Boolean.TRUE.equals(it.getUseAi())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(refrigeratingSystems)) {
            return 0;
        }
        Map<Long, Project> roomIdWithProject = assembleRoomIdWithProject();
        long count = 0L;
        for (RefrigeratingSystem refrigeratingSystem : refrigeratingSystems) {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();

            long l = queryEndColdPredictDataSystemBatch(time, refrigeratingSystem.getRoomId(), roomIdWithProject.get(refrigeratingSystem.getRoomId()), poiType,
                    refrigeratingSystem.getId());

            stopWatch.stop();
            count += l;
            log.info("每个系统制冷数据转存执行时间:{}", stopWatch.getLastTaskTimeMillis());
        }

        return count;
    }


    /**
     * 找靠近当前时间点的15分钟的数据(小于等于）
     *
     * @param time
     * @return
     */
    private LocalDateTime getLastFifTime(LocalDateTime time) {
        //找靠近当前时间点的15分钟的数据(小于等于）
        int minute = time.getMinute();
        LocalDateTime firstTimeOfHour = TimeUtil.getFirstTimeOfHour(time);
        return TimeUtil.addDateTimeByCycle(firstTimeOfHour, AggregationCycle.FIFTEEN_MINUTES, minute / 15);
    }

    /**
     * @return
     */
    private long queryEndColdPredictDataSystemBatch(LocalDateTime time, Long roomId, Project project, Integer poiType, Long systemId) throws Exception {
        long count = 0;
        if (Objects.equals(poiType, COP_FIT_POI_TYPE)) {
            List<LocalDateTime> monthTime = TimeUtil.getTimeRange(time, LocalDateTime.now(), AggregationCycle.ONE_MONTH);
            for (LocalDateTime dateTime : monthTime) {
                copFitting(dateTime, roomId, project);
            }
        } else if (Objects.equals(poiType, PUMP_FIT_POI_TYPE)) {
            List<LocalDateTime> monthTime = TimeUtil.getTimeRange(time, LocalDateTime.now(), AggregationCycle.ONE_MONTH);
            for (LocalDateTime dateTime : monthTime) {
                pumpFitting(dateTime, roomId, project);
            }
        } else {
            List<LocalDateTime> timeRange = TimeUtil.getTimeRange(time, LocalDateTime.now(), AggregationCycle.FIFTEEN_MINUTES);
            for (LocalDateTime time1 : timeRange) {
                if (Objects.equals(poiType, COLD_LOAD_POI_TYPE)) {
                    count += queryEndColdPredictDataSystem(time1, roomId, project);
                } else if (Objects.equals(poiType, MAIN_CONTROL_POI_TYPE)) {
                    count += queryMainControlPredictDataSystem(time1, roomId, project, systemId);
                }
            }
        }
        return count;
    }

    /**
     * @return
     */
    private long queryEndColdPredictDataSystem(LocalDateTime time, Long roomId, Project project) throws Exception {


        List<ColdLoadQueryParam> coldPredictQueryParam = cetMlPredictService.getColdPredictQueryParam(time, roomId, project.getId());
        log.info(COLD_LOG_KEY + "本次转存查询入参是{}", JsonTransferUtils.toJSONString(coldPredictQueryParam));
        String s = cetmlNewFeignService.clodLoadPredict(coldPredictQueryParam);
        Result result = JsonTransferUtils.parseString(s, Result.class);
        List<ColdLoadPredictReturn> coldLoadPredictReturns = JsonTransferUtils.transferList((List) result.getData(), ColdLoadPredictReturn.class);
        log.info(COLD_LOG_KEY + "本次转存调用算法返回结果是{}", JsonTransferUtils.toJSONString(coldLoadPredictReturns));
        LocalDateTime predictTime = TimeUtil.addDateTimeByCycle(time, AggregationCycle.FIFTEEN_MINUTES, 1);
        List<ColdPredict> old = coldPredictDao.queryColdPredictData(Arrays.asList(ColdLoadType.END, ColdLoadType.TOTAL),
                Collections.singletonList(PredictDataType.COLD_LOAD), predictTime, TimeUtil.addDateTimeByCycle(predictTime, AggregationCycle.FIFTEEN_MINUTES, 1), roomId);
        List<ColdPredict> predicts = transDataType(coldLoadPredictReturns, project.getId(), roomId);
        log.info("本次转存调用算法返回值是{}", JsonTransferUtils.toJSONString(predicts));
        log.info("本次转存查询到的历史数据是{}", JsonTransferUtils.toJSONString(old));
        return checkOldData(predicts, old);
    }

    private void filterNormalMains(Map<Long, Boolean> statusMap, List<ColdMachineControlParam> coldMainControlQueryParams) {
        if (CollectionUtils.isEmpty(coldMainControlQueryParams)) {
            return;
        }
        ColdMachineControlParam param = coldMainControlQueryParams.get(0);
        ColdLoadParam coldLoadParam = param.getColdLoadParams().get(0);
        List<ColdMachineData> coldMachineData = coldLoadParam.getColdMachineData();
        List<ColdMachineData> result = new ArrayList<>();
        for (ColdMachineData machineData : coldMachineData) {
            Boolean status = statusMap.get(machineData.getObjectId());
            if (Boolean.TRUE.equals(status)) {
                result.add(machineData);
            }
        }
        coldLoadParam.setColdMachineData(result);

    }

    /**
     *
     * @param time
     * @param roomId
     * @param project
     * @param systemId
     * @return
     * @throws Exception
     */
    private long queryMainControlPredictDataSystem(LocalDateTime time, Long roomId, Project project, Long systemId) throws Exception {

        List<ColdMachineControlParam> coldMainControlQueryParams = cetMlPredictService.getColdMainControlQueryParams(time, roomId, project.getId());
        long endTime = TimeUtil.localDateTime2timestamp(time);
        //过滤不正常的冷机
        Map<Long, Boolean> statusMap = faultDiagnosis.deviceFaultDiagnosis(roomId, TimeUtil.addDateTimeByCycle(endTime, AggregationCycle.FIFTEEN_MINUTES, -1), endTime);
        log.info(MAIN_CONTROL_LOG_KEY + "本次转存冷机状态判断的结果是{}", JsonTransferUtils.toJSONString(statusMap));
        filterNormalMains(statusMap, coldMainControlQueryParams);
        log.info(MAIN_CONTROL_LOG_KEY + "本次转存查询入参是{}", JsonTransferUtils.toJSONString(coldMainControlQueryParams));
        String s = cetmlNewFeignService.clodMachineControl(coldMainControlQueryParams);
        Result result = JsonTransferUtils.parseString(s, Result.class);
        List<OptimizeControlResultReturn> controlReturns = JsonTransferUtils.transferList((List) result.getData(), OptimizeControlResultReturn.class);
        log.info(MAIN_CONTROL_LOG_KEY + "本次转存调用算法返回结果是{}", JsonTransferUtils.toJSONString(controlReturns));
        LocalDateTime predictTime = TimeUtil.addDateTimeByCycle(time, AggregationCycle.FIFTEEN_MINUTES, 1);
        //要更新策略表和预测表
        List<AiStartStopStrategy> strategies = aiStartStopStrategyDao.queryAiStartStopStrategy(systemId,
                Arrays.asList(StrategyTypeDef.stop, StrategyTypeDef.start, StrategyTypeDef.optimization), predictTime, TimeUtil.addDateTimeByCycle(predictTime, AggregationCycle.FIFTEEN_MINUTES, 1));
        List<Long> ids = strategies.stream().map(AiStartStopStrategy::getId).distinct().collect(Collectors.toList());
        List<StrategyObjectMap> strategyObjectMaps = strategyObjectMapDao.queryStrategyObjectMap(ids);
        List<ColdPredict> old = coldPredictDao.queryColdPredictData(Collections.singletonList(ColdLoadType.TOTAL),
                Collections.singletonList(PredictDataType.POWER), predictTime, TimeUtil.addDateTimeByCycle(predictTime, AggregationCycle.FIFTEEN_MINUTES, 1), roomId);
        List<ColdPredict> predicts = transDataListType(controlReturns, project.getId(), roomId);
        writeAndCheckOldData(controlReturns, strategies, strategyObjectMaps, systemId, roomId, statusMap);
        log.info("本次转存调用算法返回值是{}", JsonTransferUtils.toJSONString(predicts));
        log.info("本次转存查询到的历史数据是{}", JsonTransferUtils.toJSONString(old));
        return checkOldData(predicts, old);
    }

    /**
     * @return
     */
    private Long copFitting(LocalDateTime time, Long roomId, Project project) throws IllegalAccessException, InstantiationException {
        List<CopQueryParam> copQueryParams = cetMlPredictService.getCopQueryParams(time, roomId, project.getId());
        log.info(COP_FIT_LOG_KEY + "本次算法调用查询入参是{}", JsonTransferUtils.toJSONString(copQueryParams));
        String cop = cetmlNewFeignService.cop(copQueryParams);
        log.info(COP_FIT_LOG_KEY + "本次算法返回值是{}", JsonTransferUtils.toJSONString(cop));
        return null;
    }

    /**
     * @return
     */
    private Long pumpFitting(LocalDateTime time, Long roomId, Project project)  {
        List<PumpFitParam> pumpFitParam = cetMlPredictService.getPumpFitParam(time, roomId, project.getId());
        log.info(PUMP_FIT_LOG_KEY + "本次算法调用查询入参是{}", JsonTransferUtils.toJSONString(pumpFitParam));
        String cop = cetmlNewFeignService.pumpFit(pumpFitParam);
        log.info(PUMP_FIT_LOG_KEY + "本次算法返回值是{}", JsonTransferUtils.toJSONString(cop));
        return null;
    }

    private List<ColdPredict> transDataType(List<ColdLoadPredictReturn> coldLoadPredictReturns, Long projectId, Long roomId) {
        List<ColdPredict> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(coldLoadPredictReturns)) {
            return result;
        }
        ColdLoadPredictReturn coldLoadPredictReturn = coldLoadPredictReturns.get(0);
        List<ColdLoadPredict> predictResults = coldLoadPredictReturn.getPredictResults();
        for (ColdLoadPredict predict : predictResults) {
            if (Objects.nonNull(predict.getPredictEndCoolingLoad())) {
                result.add(assembleColdPredict(predict.getPredictEndCoolingLoad(), predict.getLogTime(), projectId, PredictDataType.COLD_LOAD,
                        ColdLoadType.END, roomId));
            }
            if (Objects.nonNull(predict.getPredictTotalCoolingLoad())) {
                result.add(assembleColdPredict(predict.getPredictTotalCoolingLoad(), predict.getLogTime(), projectId, PredictDataType.COLD_LOAD,
                        ColdLoadType.TOTAL, roomId));
            }
        }

        return result;
    }

    private List<ColdPredict> transDataListType(List<OptimizeControlResultReturn> controlReturns, Long projectId, Long roomId) {
        List<ColdPredict> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(controlReturns)) {
            return result;
        }
        OptimizeControlResultReturn optimizeControlResultReturn = controlReturns.get(0);
        List<OptimizeControlResult> optimizeControlResults = optimizeControlResultReturn.getOptimizeControlResults();
        for (OptimizeControlResult predictResults : optimizeControlResults) {
            CoolingResult coolingResult = predictResults.getCoolingResult();
            if (Objects.nonNull(coolingResult) && Objects.nonNull(coolingResult.getTotalLeastPower())) {
                result.add(assembleColdPredict(coolingResult.getTotalLeastPower(), predictResults.getLogTime(), projectId, PredictDataType.POWER,
                        ColdLoadType.TOTAL, roomId));
            }
        }
        return result;
    }

    private long writeAndCheckOldData(List<OptimizeControlResultReturn> controlReturns, List<AiStartStopStrategy> strategies,
                                      List<StrategyObjectMap> strategyObjectMaps, Long systemId, Long roomId, Map<Long, Boolean> statusMap) {
        if (CollectionUtils.isEmpty(controlReturns)) {
            return 0;
        }
        OptimizeControlResultReturn optimizeControlResultReturn = controlReturns.get(0);
        List<OptimizeControlResult> optimizeControlResults = optimizeControlResultReturn.getOptimizeControlResults();
        List<AiStartStopStrategy> newStrategy = new ArrayList<>();
        for (OptimizeControlResult result : optimizeControlResults) {
            newStrategy.addAll(assembleStrategyAndMap(result, systemId, statusMap));

        }
        List<AiStartStopStrategy> strategies1 = checkStrategyOldData(newStrategy, strategies);
        List<StrategyObjectMap> newMap = new ArrayList<>();
        for (OptimizeControlResult result : optimizeControlResults) {
            //还是需要系统对应房间的子节点
            List<Map<String, Object>> maps = modelServiceUtils.queryWithChildren(NodeLabelDef.ROOM, Collections.singletonList(roomId), Collections.singletonList(NodeLabelDef.COLD_WATER_MAINENGINE));
            List<BaseVo> baseVos = JsonTransferUtils.transferList(maps, BaseVo.class);
            if (CollectionUtils.isEmpty(baseVos)) {
                continue;
            }
            List<BaseVo> children = baseVos.get(0).getChildren();
            newMap.addAll(createAiStartStopStrategyList(result,
                    strategies1, children));

        }
        // 匹配数据库中已经有的数据
        checkOldDataStrategyObjectMap(newMap, strategyObjectMaps);
        return strategies1.size();
    }

    private List<StrategyObjectMap> createAiStartStopStrategyList(OptimizeControlResult controlResult,
                                                                  List<AiStartStopStrategy> strategies1, List<BaseVo> nodes) {
        List<ColdMachineResult> coldMachineResults = new ArrayList<>();
        if (Objects.nonNull(controlResult.getCoolingResult().getColdMachineResults())) {
            coldMachineResults = controlResult.getCoolingResult().getColdMachineResults();
        }
        Map<Long, ColdMachineResult> coldMachineResultMap = coldMachineResults.stream().collect(Collectors.toMap(ColdMachineResult::getObjectId, Function.identity()));
        List<StrategyObjectMap> objectMaps = new ArrayList<>();
        if (Boolean.TRUE.equals(controlResult.getCoolingEnabled())) {
            for (BaseVo baseVo : nodes) {
                ColdMachineResult coldMachineResult = coldMachineResultMap.get(baseVo.getId());
                if (Objects.isNull(coldMachineResult)) {
                    //故障的情况
                    AiStartStopStrategy orElse = strategies1.stream().filter(aiStartStopStrategy ->
                            Objects.equals(aiStartStopStrategy.getOperationTime(), controlResult.getLogTime())
                                    && Objects.equals(aiStartStopStrategy.getStrategyType(), StrategyTypeDef.stop))
                            .findFirst().orElse(new AiStartStopStrategy());
                    objectMaps.add(assembleStrategyObjectMap(orElse, baseVo.getId(), NodeLabelDef.COLD_WATER_MAINENGINE, null));
                } else {
                    //正常的情况
                    addNormalMainsData(controlResult, strategies1, coldMachineResult,
                            objectMaps, baseVo);
                }
            }
        } else {
            AiStartStopStrategy orElse = strategies1.stream().filter(aiStartStopStrategy ->
                    Objects.equals(aiStartStopStrategy.getOperationTime(), controlResult.getLogTime())
                            && Objects.equals(aiStartStopStrategy.getStrategyType(), StrategyTypeDef.start))
                    .findFirst().orElse(new AiStartStopStrategy());
            for (BaseVo baseVo : nodes) {
                objectMaps.add(assembleStrategyObjectMap(orElse, baseVo.getId(), NodeLabelDef.COLD_WATER_MAINENGINE, null));
            }

        }
        return objectMaps;
    }

    private void addNormalMainsData(OptimizeControlResult controlResult, List<AiStartStopStrategy> strategies1, ColdMachineResult coldMachineResult,
                                    List<StrategyObjectMap> objectMaps, BaseVo baseVo) {
        if (Objects.equals(coldMachineResult.getCoolingLoad(), 0.0) || Objects.equals(coldMachineResult.getCoolingLoadRate(), 0.0)) {
            //关机 加调优
            AiStartStopStrategy orElse = strategies1.stream().filter(aiStartStopStrategy ->
                    Objects.equals(aiStartStopStrategy.getOperationTime(), controlResult.getLogTime())
                            && Objects.equals(aiStartStopStrategy.getStrategyType(), StrategyTypeDef.stop))
                    .findFirst().orElse(new AiStartStopStrategy());
            objectMaps.add(assembleStrategyObjectMap(orElse, baseVo.getId(), NodeLabelDef.COLD_WATER_MAINENGINE, null));
        } else {
            AiStartStopStrategy orElse = strategies1.stream().filter(aiStartStopStrategy ->
                    Objects.equals(aiStartStopStrategy.getOperationTime(), controlResult.getLogTime())
                            && Objects.equals(aiStartStopStrategy.getStrategyType(), StrategyTypeDef.start))
                    .findFirst().orElse(new AiStartStopStrategy());
            objectMaps.add(assembleStrategyObjectMap(orElse, coldMachineResult.getObjectId(), NodeLabelDef.COLD_WATER_MAINENGINE, null));

        }
        AiStartStopStrategy strategy = strategies1.stream().filter(aiStartStopStrategy ->
                Objects.equals(aiStartStopStrategy.getOperationTime(), controlResult.getLogTime())
                        && Objects.equals(aiStartStopStrategy.getStrategyType(), StrategyTypeDef.optimization))
                .findFirst().orElse(new AiStartStopStrategy());
        objectMaps.add(assembleStrategyObjectMap(strategy, coldMachineResult.getObjectId(), NodeLabelDef.COLD_WATER_MAINENGINE, coldMachineResult.getWaterTemp()));

    }

    private StrategyObjectMap assembleStrategyObjectMap(AiStartStopStrategy orElse, Long objectId, String objectLabel, Double temp) {
        StrategyObjectMap strategy = new StrategyObjectMap();
        strategy.setStrategyId(orElse.getId());
        strategy.setTemp(temp);
        strategy.setObjectId(objectId);
        strategy.setObjectLabel(objectLabel);
        return strategy;
    }

    private List<AiStartStopStrategy> assembleStrategyAndMap(OptimizeControlResult result, Long systemId, Map<Long, Boolean> statusMap) {
        Boolean coolingEnabled = result.getCoolingEnabled();
        Long logTime = result.getLogTime();
        CoolingResult coolingResult = result.getCoolingResult();

        List<ColdMachineResult> coldMachineResults = new ArrayList<>();
        if (Objects.nonNull(coolingResult)) {
            coldMachineResults = coolingResult.getColdMachineResults();
        }
        Map<Integer, AiStartStopStrategy> map = new HashMap<>();
        if (Boolean.TRUE.equals(coolingEnabled)) {
            for (ColdMachineResult coldMachineResult : coldMachineResults) {
                if (Objects.equals(coldMachineResult.getCoolingLoad(), 0.0) || Objects.equals(coldMachineResult.getCoolingLoadRate(), 0.0)) {
                    //关机 加调优
                    map.put(StrategyTypeDef.stop, createAiStartStopStrategySingle(StrategyTypeDef.stop, systemId, logTime));
                } else {
                    map.put(StrategyTypeDef.start, createAiStartStopStrategySingle(StrategyTypeDef.start, systemId, logTime));
                }

            }
            map.put(StrategyTypeDef.optimization, createAiStartStopStrategySingle(StrategyTypeDef.optimization, systemId, logTime));
        } else {
            map.put(StrategyTypeDef.stop, createAiStartStopStrategySingle(StrategyTypeDef.stop, systemId, logTime));

        }
        boolean present = statusMap.values().stream().anyMatch(Boolean.FALSE::equals);
        if (Boolean.TRUE.equals(present)) {
            map.put(StrategyTypeDef.stop, createAiStartStopStrategySingle(StrategyTypeDef.stop, systemId, logTime));
        }
        return new ArrayList<>(map.values());
    }

    private AiStartStopStrategy createAiStartStopStrategySingle(Integer type, Long systemId
            , Long logTime) {
        AiStartStopStrategy strategy = new AiStartStopStrategy();
        strategy.setStrategyType(type);
        strategy.setOperationTime(logTime);
        strategy.setUpdateTime(logTime);
        strategy.setRefrigeratingSystemId(systemId);
        return strategy;
    }


    private List<AiStartStopStrategy> checkStrategyOldData(List<AiStartStopStrategy> predicts, List<AiStartStopStrategy> old) {

        // 匹配数据库中已经有的数据
        for (AiStartStopStrategy weather : predicts) {
            Optional<AiStartStopStrategy> any = old.stream().filter(item ->
                    Objects.equals(item.getOperationTime(), weather.getOperationTime()) &&
                            Objects.equals(weather.getRefrigeratingSystemId(), item.getRefrigeratingSystemId())
                            && Objects.equals(weather.getStrategyType(), item.getStrategyType())).findAny();
            any.ifPresent(item -> weather.setId(item.getId()));
        }
        return modelServiceUtils.writeData(predicts, AiStartStopStrategy.class);
    }

    private void checkOldDataStrategyObjectMap(List<StrategyObjectMap> predicts, List<StrategyObjectMap> old) {

        // 匹配数据库中已经有的数据
        for (StrategyObjectMap weather : predicts) {
            Optional<StrategyObjectMap> any = old.stream().filter(item ->
                    Objects.equals(item.getStrategyId(), weather.getStrategyId()) &&
                            Objects.equals(weather.getObjectId(), item.getObjectId())
                            && Objects.equals(weather.getObjectLabel(), item.getObjectLabel())).findAny();
            any.ifPresent(item -> weather.setId(item.getId()));
        }
        modelServiceUtils.writeData(predicts, StrategyObjectMap.class);
    }

    private ColdPredict assembleColdPredict(Double value, Long time, Long projectId, Integer dataType, Integer coldType, Long roomId) {
        ColdPredict predict = new ColdPredict();
        predict.setValue(value);
        predict.setLogTime(TimeUtil.timestamp2LocalDateTime(time));
        predict.setProjectId(projectId);
        predict.setPredictDataType(dataType);
        predict.setColdLoadType(coldType);
        predict.setRoomId(roomId);
        return predict;
    }


    private long checkOldData(List<ColdPredict> weatherPredicts, List<ColdPredict> old) {

        // 匹配数据库中已经有的数据
        for (ColdPredict weather : weatherPredicts) {
            Optional<ColdPredict> any = old.stream().filter(item ->
                    Objects.equals(item.getLogTime(), weather.getLogTime()) &&
                            Objects.equals(weather.getRoomId(), item.getRoomId())
                            && Objects.equals(item.getPredictDataType(), weather.getPredictDataType())
                            && Objects.equals(item.getColdLoadType(), weather.getColdLoadType())).findAny();
            any.ifPresent(item -> weather.setId(item.getId()));
        }
        List<ColdPredict> write = coldPredictDao.write(weatherPredicts);
        log.info("本次转存最后入库结果是{}", JsonTransferUtils.toJSONString(weatherPredicts));
        //再写入
        return write.size();
    }
}