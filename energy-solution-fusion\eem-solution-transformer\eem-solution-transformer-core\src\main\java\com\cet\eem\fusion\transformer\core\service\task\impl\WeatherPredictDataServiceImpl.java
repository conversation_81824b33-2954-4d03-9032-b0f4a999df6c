package com.cet.eem.fusion.transformer.core.service.task.impl;

import com.cet.eem.bll.common.dao.project.ProjectDao;
import com.cet.eem.bll.common.model.domain.object.organization.Project;
import com.cet.eem.bll.common.model.domain.subject.energysaving.WeatherPredict;
import com.cet.eem.bll.energysaving.dao.weather.RefrigeratingSystemDao;
import com.cet.eem.bll.energysaving.dao.weather.WeatherPredictDao;
import com.cet.eem.bll.energysaving.model.config.system.RefrigeratingSystem;
import com.cet.eem.bll.energysaving.model.dataentryquery.DataLogDataWrite;
import com.cet.eem.bll.energysaving.model.weather.ForecastBasicWeatherQueryVo;
import com.cet.eem.bll.energysaving.service.predict.LgbModelPredictService;
import com.cet.eem.bll.energysaving.service.task.WeatherPredictDataService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.tool.ModelServiceUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : WeatherPredictDataServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-07-25 15:13
 */
@Slf4j
@Service
public class WeatherPredictDataServiceImpl implements WeatherPredictDataService {

    @Autowired
    RefrigeratingSystemDao refrigeratingSystemDao;
    @Autowired
    LgbModelPredictService lgbModelPredictService;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    WeatherPredictDao weatherPredictDao;
    @Autowired
    ProjectDao projectDao;
    @Value("${cet.eem.task.energy-saving.weather.startTime}")
    private String startTime;

    @Override
    public void saveWeatherPredictData() {
        log.info("开始转存算法天气预测结果数据。");
        long beginTime = System.currentTimeMillis();


        long count = 0;

        try {
            count = queryWeatherPredictData();
        } catch (Exception e) {
            log.error("[转存算法天气预测结果数据]转存算法天气预测结果异常", e);
        }

        long overTime = System.currentTimeMillis();
        log.info("本次转存算法天气预测结果数据：{}，耗时(s)：{}", count, (overTime - beginTime) * CommonUtils.DOUBLE_CONVERSION_COEFFICIENT / 1000);
        log.info("结束转存算法天气预测结果数据。");
    }

    /**
     * 解析全局开始时间
     *
     * @return 全局开始时间
     */
    public LocalDateTime parsePercentlStartTime() {
        if (StringUtils.isBlank(startTime)) {
            return LocalDateTime.now();
        }
        return TimeUtil.parse(startTime, TimeUtil.LONG_TIME_FORMAT);
    }

    /**
     * @return
     */
    private long queryWeatherPredictData() throws Exception {
        //先查用能异常越限的信息
        LocalDateTime time = parsePercentlStartTime();
        List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.selectAll();
        List<Project> projects = projectDao.selectAll();
        if (CollectionUtils.isEmpty(refrigeratingSystems) || CollectionUtils.isEmpty(projects)) {
            return 0;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        long count = queryWeatherBatch(time, projects);
        stopWatch.stop();
        log.info("天气数据转存执行时间:{}", stopWatch.getLastTaskTimeMillis());
        return count;
    }

    private long queryWeatherBatch(LocalDateTime time, List<Project> projects) throws Exception {
        List<LocalDateTime> timeRange = TimeUtil.getTimeRange(time, LocalDateTime.now(), AggregationCycle.ONE_HOUR);
        long count = 0;
        for (LocalDateTime time1 : timeRange) {
            count += queryWeatherPredictDataSystem(time1, projects);
        }
        return count;
    }

    private List<WeatherPredict> transDataTypeSingle(DataLogDataWrite weatherPredictData, DataLogDataWrite tempPredictData, Project project
    ) {
        List<WeatherPredict> result = new ArrayList<>();
        List<DataLogData> hum = weatherPredictData.getDataLogData();
        List<DataLogData> temp = tempPredictData.getDataLogData();
        Map<Long, List<DataLogData>> mapHum = hum.stream().collect(Collectors.groupingBy(DataLogData::getTime));
        Map<Long, List<DataLogData>> mapTemp = temp.stream().collect(Collectors.groupingBy(DataLogData::getTime));
        for (Map.Entry<Long, List<DataLogData>> entry : mapHum.entrySet()) {
            List<DataLogData> value = entry.getValue();
            List<DataLogData> dataLogData = mapTemp.get(entry.getKey());
            if (CollectionUtils.isNotEmpty(value) && CollectionUtils.isNotEmpty(dataLogData)) {
                WeatherPredict predict = new WeatherPredict();
                predict.setHumidity(value.get(0).getValue());
                predict.setTemp(dataLogData.get(0).getValue());
                predict.setAggregationCycle(AggregationCycle.FIFTEEN_MINUTES);
                predict.setLat(project.getLatitude());
                predict.setLon(project.getLongitude());
                predict.setProjectId(project.getId());
                predict.setLogTime(TimeUtil.timestamp2LocalDateTime(value.get(0).getTime()));
                result.add(predict);
            }
        }
        return result;
    }


    /**
     * @return
     */
    private long queryWeatherPredictDataSystem(LocalDateTime time, List<Project> projects) throws Exception {
        //构建查询历史和未来的开始和截止时间
        if (CollectionUtils.isEmpty(projects)) {
            return 0;
        }
        Project project = projects.get(0);
        ForecastBasicWeatherQueryVo query = assembleTime(time);
        query.setCycle(AggregationCycle.FIFTEEN_MINUTES);
        DataLogDataWrite weatherPredictData = lgbModelPredictService.getWeatherPredictData(query, project.getId());
        DataLogDataWrite weatherPredictDataOfTemperature = lgbModelPredictService.getWeatherPredictDataOfTemperature(query, project.getId());
        List<WeatherPredict> weatherPredicts = weatherPredictDao.queryWeather(query.getStartTimePredict(), query.getEndTimePredict());
        List<WeatherPredict> newData = transDataTypeSingle(weatherPredictData, weatherPredictDataOfTemperature, project);
        return checkOldData(newData, weatherPredicts);
    }

    private ForecastBasicWeatherQueryVo assembleTime(LocalDateTime now) {
        ForecastBasicWeatherQueryVo query = new ForecastBasicWeatherQueryVo();
        //历史时间
        LocalDateTime firstTimeOfHour = TimeUtil.getFirstTimeOfHour(now);
        List<LocalDateTime> timeRangeForFifteen = TimeUtil.getTimeRange(firstTimeOfHour, TimeUtil.addDateTimeByCycle(firstTimeOfHour, AggregationCycle.ONE_HOUR, 3), AggregationCycle.FIFTEEN_MINUTES);
        int i = 0;
        for (LocalDateTime time : timeRangeForFifteen) {
            if (Boolean.TRUE.equals(time.isAfter(now))) {
                if (Objects.equals(i, 0)) {
                    query.setStartTimePredict(time);
                } else if (Objects.equals(i, 4)) {
                    query.setEndTimePredict(time);
                }
                i++;
            }
        }
        //考虑now是整点的情况，不能赋值now
        query.setEndTime(query.getStartTimePredict());
        query.setStartTime(TimeUtil.addDateTimeByCycle(query.getEndTime(), AggregationCycle.ONE_DAY, -1));
        return query;
    }


    private long checkOldData(List<WeatherPredict> weatherPredicts, List<WeatherPredict> old) {

        // 匹配数据库中已经有的数据
        for (WeatherPredict weather : weatherPredicts) {
            Optional<WeatherPredict> any = old.stream().filter(item ->
                    Objects.equals(item.getLogTime(), weather.getLogTime()) &&
                            Objects.equals(weather.getProjectId(), item.getProjectId())).findAny();
            any.ifPresent(item -> weather.setId(item.getId()));
        }
        List<WeatherPredict> weatherPredicts1 = weatherPredictDao.writeOrUpdateData(weatherPredicts);
        //再写入
        return weatherPredicts1.size();
    }
}