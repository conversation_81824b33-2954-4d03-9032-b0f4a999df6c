package com.cet.eem.fusion.transformer.core.service.predict;

import com.cet.eem.bll.energysaving.model.dataentryquery.*;
import com.cet.eem.bll.energysaving.model.predict.WeatherEntry;
import com.cet.eem.bll.energysaving.model.predict.WeatherReturn;
import com.cet.eem.bll.energysaving.model.weather.ForecastBasicWeatherDataVo;
import com.cet.eem.bll.energysaving.model.weather.ForecastBasicWeatherQueryVo;
import com.cet.eem.common.model.Result;

import java.util.List;

/**
 * @ClassName : LgbModelPredictService
 * @Description : 算法预测服务
 * <AUTHOR> jiangzixuan
 * @Date: 2022-07-12 10:01
 */
public interface LgbModelPredictService {
    /**
     * 湿度
     *
     * @param entry
     * @return
     */
    WeatherReturn getWeatherPredict(WeatherEntry entry);

    /**
     * 温度
     *
     * @param entry
     * @return
     */
    WeatherReturn getWeatherPredictOfTemperature(WeatherEntry entry);

    /**
     * 湿度
     *
     * @param query
     * @return
     * @throws Exception
     */
    DataLogDataWrite getWeatherPredictData(ForecastBasicWeatherQueryVo query, Long projectId) throws Exception;
    DataLogDataWrite getWeatherPredictData(Result<List<ForecastBasicWeatherDataVo>> ok);
    /**
     * 温度
     *
     * @param query
     * @return
     * @throws Exception
     */
    DataLogDataWrite getWeatherPredictDataOfTemperature(ForecastBasicWeatherQueryVo query, Long projectId) throws Exception;

    /**
     * 末端
     *
     * @param queryParam
     * @return
     */
    DataLogDataWrite getEndColdPredictData(DataQueryParam queryParam, Long projectId) throws InstantiationException, IllegalAccessException;
    DataLogDataWrite getEndColdPredictData(Result<List<EndColdDataEntry>> entries);
    /**
     * 管损
     *
     * @param queryParam
     * @return
     */
    DataLogDataWrite getPipelineLossColdPredictData(DataQueryParam queryParam, Long projectId);

    /**
     * 冷机出水温度
     *
     * @param queryParam
     * @return
     */
    List<DataLogDataWrite> getOptimizationOfRefrigeratorWaterPredict(DataQueryParam queryParam, Long projectId) throws InstantiationException, IllegalAccessException;

    /**
     * 系统总功率需求预测查询
     * @param queryParam
     * @return
     */
    DataLogDataWrite getTotalSystemPowerPredict(DataQueryParam queryParam, Long projectId) throws InstantiationException, IllegalAccessException;
    DataLogDataWrite getTotalSystemPowerPredict(Result<List<TotalSystemPowerEntry>> entries);
}
