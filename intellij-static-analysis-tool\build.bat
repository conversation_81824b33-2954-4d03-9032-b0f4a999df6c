@echo off
chcp 65001 >nul
echo ========================================
echo IntelliJ Static Analysis Tool Build Script
echo ========================================

echo.
echo Checking if <PERSON><PERSON> is available...
mvn --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: <PERSON>ven not found, please ensure <PERSON><PERSON> is installed and added to PATH
    pause
    exit /b 1
)

echo Maven version info:
mvn --version

echo.
echo Starting project cleanup...
call mvn clean
if %errorlevel% neq 0 (
    echo Error: Project cleanup failed
    pause
    exit /b 1
)

echo.
echo Starting project compilation...
call mvn compile
if %errorlevel% neq 0 (
    echo Error: Project compilation failed
    pause
    exit /b 1
)

echo.
echo Starting project packaging...
call mvn package
if %errorlevel% neq 0 (
    echo Error: Project packaging failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed!
echo ========================================
echo.
echo Generated files:
echo - target\intellij-static-analysis-tool-1.0.0.jar
echo - target\intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar
echo.
echo Usage:
echo java -jar target\intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar ^<file-path^> [project-root-path]
echo.
echo Example:
echo java -jar target\intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar examples\AiOptimizationServiceImpl.java .
echo.
pause
