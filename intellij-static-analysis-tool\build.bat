@echo off
echo ========================================
echo IntelliJ Static Analysis Tool 构建脚本
echo ========================================

echo.
echo 检查Maven是否可用...
mvn --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Maven，请确保Maven已安装并添加到PATH环境变量中
    pause
    exit /b 1
)

echo Maven版本信息:
mvn --version

echo.
echo 开始清理项目...
call mvn clean
if %errorlevel% neq 0 (
    echo 错误: 清理项目失败
    pause
    exit /b 1
)

echo.
echo 开始编译项目...
call mvn compile
if %errorlevel% neq 0 (
    echo 错误: 编译项目失败
    pause
    exit /b 1
)

echo.
echo 开始打包项目...
call mvn package
if %errorlevel% neq 0 (
    echo 错误: 打包项目失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 构建完成！
echo ========================================
echo.
echo 生成的文件:
echo - target\intellij-static-analysis-tool-1.0.0.jar
echo - target\intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar
echo.
echo 使用方法:
echo java -jar target\intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar ^<文件路径^> [项目根路径]
echo.
echo 示例:
echo java -jar target\intellij-static-analysis-tool-1.0.0-jar-with-dependencies.jar examples\AiOptimizationServiceImpl.java .
echo.
pause
