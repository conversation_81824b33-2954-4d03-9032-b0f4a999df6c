package com.cet.eem.fusion.transformer.core.service.trend;

import com.cet.eem.bll.energysaving.model.config.*;
import com.cet.eem.bll.energysaving.model.weather.PumpVo;

import java.util.List;
import java.util.Map;

/**
 * @ClassName : AIPredictService
 * @Description : ai预测
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-16 14:49
 */
public interface ModelConfigurationService {
    /**
     * 查询所有启用预测的空调机房
     * @return
     */
    List<RefrigeratingSystemVo> queryAiUseSystem(Long projectId, Long uesrId);

    /**
     * 根据id查询空调机房（启用预测）
     * @param id
     * @return
     */
    List<RefrigeratingSystemVo> queryAiUseSystemByRoomId(Long id);
    /**
     * 写入需要ui预测的数据
     * @param ids
     * @return
     */
    void writeOrUpdateAiUseSystem(List<Long> ids);

    /**
     * 更新板式xxx温度
     * @param id
     * @param temp
     */
    void setOrUpdateTemp(Long id, Double temp, Double outsideTempLimit, Double coolingLoadDemandLimit);

    /**
     * 修改启停顺序
     * @param modelConfigurationParam
     */
    void writeOrUpdate(ModelConfigurationParam modelConfigurationParam);

    /**
     * 写入连锁顺序
     * @param deviceChainParam
     * @return
     */
    Map<String, Object> insertDeviceChain(DeviceChainParam deviceChainParam);

    /**
     * 查询所有连锁
     * @param roomId
     * @return
     */
    List<DeviceChainParam> queryAllDeviceChain(Long roomId, Long projectId);
    /**
     *  查询空调机房下的需要连锁的设备
     * @param rooId
     * @return
     */
    List<PumpVo> queryChainDevice(Long rooId);

    /**
     * 查询启停顺序
     * @param roomId
     * @return
     */
    List<ModelConfigVo> queryDeviceStartAndStop(Long roomId);

    /**
     * 删除连锁
     * @param chainIds
     */
    void deleteChain(List<Long> chainIds);

    /**
     * 根据连锁id查询连锁详情
     * @param chainId
     * @return
     */
    List<DeviceChainParam> queryDeviceChainWithDetail(Long chainId, Long projectID);

    /**
     * 写入启停设备操作规则
     * @param startAndStopParam
     */
    void writeStartAndStopOperationRule(StartAndStopParam startAndStopParam);

    /**
     * 查询启停顺序操作规则
     * @param roomId
     * @return
     */
    StartAndStopVo queryStartAndStopOperationRule(Long roomId);
}