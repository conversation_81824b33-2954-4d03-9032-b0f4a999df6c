package com.example.intellij.analysis;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.PathManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManager;
import com.intellij.openapi.startup.StartupManager;
import com.intellij.openapi.util.Disposer;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.intellij.openapi.vfs.VirtualFile;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.List;

/**
 * 静态分析工具主入口类
 * 基于IntelliJ Platform SDK实现Java文件静态扫描
 */
public class StaticAnalysisMain {

    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(StaticAnalysisMain.class);
    private static final Logger intellijLogger = Logger.getInstance(StaticAnalysisMain.class);
    
    public static void main(String[] args) {
        if (args.length < 1) {
            System.err.println("使用方法: java -jar intellij-static-analysis-tool.jar <文件路径> [项目根路径]");
            System.err.println("示例: java -jar intellij-static-analysis-tool.jar /path/to/AiOptimizationServiceImpl.java /path/to/project");
            System.exit(1);
        }
        
        String filePath = args[0];
        String projectPath = args.length > 1 ? args[1] : new File(filePath).getParent();
        
        logger.info("开始分析文件: {}", filePath);
        logger.info("项目路径: {}", projectPath);
        
        try {
            // 初始化IntelliJ Platform环境
            initializeIntellijPlatform();

            // 执行静态分析
            StaticAnalysisMain analyzer = new StaticAnalysisMain();
            AnalysisResult result = analyzer.analyzeFile(filePath, projectPath);

            // 输出结果
            printAnalysisResult(result);

        } catch (Exception e) {
            logger.error("分析过程中发生错误", e);
            intellijLogger.error("分析过程中发生错误", e);
            System.exit(1);
        }
    }

    /**
     * 初始化IntelliJ Platform环境
     */
    private static void initializeIntellijPlatform() {
        logger.info("初始化IntelliJ Platform环境...");

        // 设置系统属性
        System.setProperty("idea.config.path", PathManager.getConfigPath());
        System.setProperty("idea.system.path", PathManager.getSystemPath());
        System.setProperty("idea.plugins.path", PathManager.getPluginsPath());
        System.setProperty("idea.log.path", PathManager.getLogPath());

        // 初始化应用程序
        ApplicationManager.getApplication();

        logger.info("IntelliJ Platform环境初始化完成");
    }



    /**
     * 分析指定的Java文件
     * 
     * @param filePath 要分析的文件路径
     * @param projectPath 项目根路径
     * @return 分析结果
     */
    public AnalysisResult analyzeFile(String filePath, String projectPath) {
        logger.info("开始分析文件: {}", filePath);
        
        File file = new File(filePath);
        if (!file.exists()) {
            throw new IllegalArgumentException("文件不存在: " + filePath);
        }
        
        File projectDir = new File(projectPath);
        if (!projectDir.exists() || !projectDir.isDirectory()) {
            throw new IllegalArgumentException("项目路径不存在或不是目录: " + projectPath);
        }
        
        try {
            // 获取虚拟文件系统中的文件
            VirtualFile virtualFile = LocalFileSystem.getInstance().findFileByPath(file.getAbsolutePath());
            if (virtualFile == null) {
                throw new IllegalArgumentException("无法在虚拟文件系统中找到文件: " + filePath);
            }
            
            // 打开或创建项目
            Project project = openProject(projectDir);
            
            try {
                // 等待项目初始化完成
                waitForProjectInitialization(project);
                
                // 创建分析器并执行分析
                JavaFileAnalyzer analyzer = new JavaFileAnalyzer(project);
                AnalysisResult result = analyzer.analyze(virtualFile);
                
                logger.info("文件分析完成，发现 {} 个问题", result.getProblems().size());
                return result;
                
            } finally {
                // 清理项目资源
                if (project != null && !project.isDisposed()) {
                    Disposer.dispose(project);
                }
            }
            
        } catch (Exception e) {
            logger.error("分析文件时发生错误: " + filePath, e);
            throw new RuntimeException("分析文件失败", e);
        }
    }
    
    /**
     * 打开或创建项目
     */
    private Project openProject(File projectDir) {
        logger.info("打开项目: {}", projectDir.getAbsolutePath());
        
        ProjectManager projectManager = ProjectManager.getInstance();
        
        // 尝试打开现有项目
        Project project = projectManager.loadAndOpenProject(projectDir.getAbsolutePath());
        
        if (project == null) {
            throw new RuntimeException("无法打开项目: " + projectDir.getAbsolutePath());
        }
        
        return project;
    }
    
    /**
     * 等待项目初始化完成
     */
    private void waitForProjectInitialization(Project project) {
        logger.info("等待项目初始化完成...");
        
        StartupManager startupManager = StartupManager.getInstance(project);
        
        // 等待启动活动完成
        final Object lock = new Object();
        final boolean[] completed = {false};
        
        startupManager.runAfterOpened(() -> {
            synchronized (lock) {
                completed[0] = true;
                lock.notify();
            }
        });
        
        synchronized (lock) {
            while (!completed[0]) {
                try {
                    lock.wait(1000); // 最多等待1秒
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        logger.info("项目初始化完成");
    }
    
    /**
     * 打印分析结果
     */
    private static void printAnalysisResult(AnalysisResult result) {
        System.out.println("\n=== 静态分析结果 ===");
        System.out.println("文件: " + result.getFilePath());
        System.out.println("分析时间: " + result.getAnalysisTime() + "ms");
        System.out.println("发现问题数量: " + result.getProblems().size());
        
        if (!result.getProblems().isEmpty()) {
            System.out.println("\n=== 问题详情 ===");
            List<InspectionProblem> problems = result.getProblems();
            for (int i = 0; i < problems.size(); i++) {
                InspectionProblem problem = problems.get(i);
                System.out.printf("%d. [%s] %s%n", 
                    i + 1, 
                    problem.getSeverity(), 
                    problem.getDescription());
                System.out.printf("   位置: 第%d行，第%d列%n", 
                    problem.getLine(), 
                    problem.getColumn());
                if (problem.getQuickFix() != null && !problem.getQuickFix().isEmpty()) {
                    System.out.printf("   建议修复: %s%n", problem.getQuickFix());
                }
                System.out.println();
            }
        } else {
            System.out.println("\n未发现任何问题！");
        }
    }
}
