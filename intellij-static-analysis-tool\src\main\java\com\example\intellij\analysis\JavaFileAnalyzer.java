package com.example.intellij.analysis;

import com.intellij.codeInspection.*;
import com.intellij.codeInspection.ex.InspectionManagerEx;
import com.intellij.codeInspection.ex.InspectionProfileImpl;
import com.intellij.codeInspection.ex.InspectionToolWrapper;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Computable;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.profile.codeInspection.InspectionProjectProfileManager;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiManager;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Java文件分析器
 * 使用IntelliJ Platform SDK的检查API来分析Java文件
 */
public class JavaFileAnalyzer {
    
    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(JavaFileAnalyzer.class);
    private static final Logger intellijLogger = Logger.getInstance(JavaFileAnalyzer.class);
    
    private final Project project;
    private final InspectionManager inspectionManager;
    
    public JavaFileAnalyzer(Project project) {
        this.project = project;
        this.inspectionManager = InspectionManager.getInstance(project);
    }
    
    /**
     * 分析指定的虚拟文件
     * 
     * @param virtualFile 要分析的虚拟文件
     * @return 分析结果
     */
    public AnalysisResult analyze(VirtualFile virtualFile) {
        logger.info("开始分析文件: {}", virtualFile.getPath());
        long startTime = System.currentTimeMillis();
        
        try {
            // 在读取操作中获取PSI文件
            PsiFile psiFile = ReadAction.compute(() -> {
                PsiManager psiManager = PsiManager.getInstance(project);
                return psiManager.findFile(virtualFile);
            });
            
            if (psiFile == null) {
                throw new IllegalArgumentException("无法获取PSI文件: " + virtualFile.getPath());
            }
            
            logger.info("成功获取PSI文件: {}", psiFile.getName());
            
            // 执行检查
            List<InspectionProblem> problems = performInspections(psiFile);
            
            long analysisTime = System.currentTimeMillis() - startTime;
            
            return new AnalysisResult(
                virtualFile.getPath(),
                problems,
                analysisTime
            );
            
        } catch (Exception e) {
            logger.error("分析文件时发生错误: " + virtualFile.getPath(), e);
            throw new RuntimeException("分析失败", e);
        }
    }
    
    /**
     * 执行所有相关的检查
     */
    private List<InspectionProblem> performInspections(PsiFile psiFile) {
        logger.info("开始执行检查...");
        
        List<InspectionProblem> allProblems = new ArrayList<>();
        
        try {
            // 获取检查配置文件
            InspectionProjectProfileManager profileManager = InspectionProjectProfileManager.getInstance(project);
            InspectionProfileImpl profile = (InspectionProfileImpl) profileManager.getCurrentProfile();
            
            // 获取所有启用的检查工具
            List<InspectionToolWrapper<?, ?>> inspectionTools = profile.getInspectionTools(psiFile);
            
            logger.info("找到 {} 个检查工具", inspectionTools.size());
            
            // 执行每个检查工具
            for (InspectionToolWrapper<?, ?> toolWrapper : inspectionTools) {
                if (!toolWrapper.isEnabledByDefault()) {
                    continue;
                }
                
                try {
                    List<InspectionProblem> problems = runInspectionTool(toolWrapper, psiFile);
                    allProblems.addAll(problems);
                    
                    if (!problems.isEmpty()) {
                        logger.debug("检查工具 {} 发现 {} 个问题", 
                            toolWrapper.getDisplayName(), problems.size());
                    }
                    
                } catch (Exception e) {
                    logger.warn("执行检查工具 {} 时发生错误: {}", 
                        toolWrapper.getDisplayName(), e.getMessage());
                    intellijLogger.warn("执行检查工具时发生错误", e);
                }
            }
            
            logger.info("检查完成，总共发现 {} 个问题", allProblems.size());
            
        } catch (Exception e) {
            logger.error("执行检查时发生错误", e);
            throw new RuntimeException("检查执行失败", e);
        }
        
        return allProblems;
    }
    
    /**
     * 运行单个检查工具
     */
    private List<InspectionProblem> runInspectionTool(InspectionToolWrapper<?, ?> toolWrapper, PsiFile psiFile) {
        List<InspectionProblem> problems = new ArrayList<>();
        
        try {
            // 在读取操作中执行检查
            CompletableFuture<List<ProblemDescriptor>> future = CompletableFuture.supplyAsync(() -> 
                ReadAction.compute(() -> {
                    try {
                        // 创建检查上下文
                        GlobalInspectionContext globalContext = inspectionManager.createNewGlobalContext();
                        
                        // 执行检查
                        InspectionTool tool = toolWrapper.getTool();
                        if (tool instanceof LocalInspectionTool) {
                            LocalInspectionTool localTool = (LocalInspectionTool) tool;
                            ProblemsHolder holder = new ProblemsHolder(inspectionManager, psiFile, false);
                            
                            // 创建访问器并访问文件
                            PsiElementVisitor visitor = localTool.buildVisitor(holder, false);
                            if (visitor != null) {
                                psiFile.accept(visitor);
                            }
                            
                            return holder.getResults();
                        }
                        
                        return new ArrayList<>();
                        
                    } catch (Exception e) {
                        logger.warn("执行检查工具内部逻辑时发生错误: {}", e.getMessage());
                        return new ArrayList<>();
                    }
                })
            );
            
            // 等待检查完成，设置超时
            List<ProblemDescriptor> descriptors = future.get(30, TimeUnit.SECONDS);
            
            // 转换问题描述符为我们的格式
            for (ProblemDescriptor descriptor : descriptors) {
                InspectionProblem problem = convertProblemDescriptor(descriptor, toolWrapper.getDisplayName());
                problems.add(problem);
            }
            
        } catch (Exception e) {
            logger.debug("运行检查工具 {} 时发生异常: {}", toolWrapper.getDisplayName(), e.getMessage());
        }
        
        return problems;
    }
    
    /**
     * 转换问题描述符为我们的问题格式
     */
    private InspectionProblem convertProblemDescriptor(ProblemDescriptor descriptor, String inspectionName) {
        try {
            // 获取问题的基本信息
            String description = descriptor.getDescriptionTemplate();
            
            // 获取位置信息
            int line = 1;
            int column = 1;
            
            if (descriptor.getPsiElement() != null) {
                // 在读取操作中获取位置信息
                int[] position = ReadAction.compute(() -> {
                    try {
                        com.intellij.openapi.editor.Document document = 
                            com.intellij.psi.PsiDocumentManager.getInstance(project)
                                .getDocument(descriptor.getPsiElement().getContainingFile());
                        
                        if (document != null) {
                            int offset = descriptor.getPsiElement().getTextOffset();
                            int lineNum = document.getLineNumber(offset) + 1;
                            int colNum = offset - document.getLineStartOffset(lineNum - 1) + 1;
                            return new int[]{lineNum, colNum};
                        }
                    } catch (Exception e) {
                        logger.debug("获取位置信息时发生错误: {}", e.getMessage());
                    }
                    return new int[]{1, 1};
                });
                
                line = position[0];
                column = position[1];
            }
            
            // 获取严重程度
            String severity = "INFO";
            if (descriptor instanceof ProblemDescriptorBase) {
                ProblemHighlightType highlightType = ((ProblemDescriptorBase) descriptor).getHighlightType();
                severity = mapHighlightTypeToSeverity(highlightType);
            }
            
            // 获取快速修复建议
            String quickFix = "";
            QuickFix<?>[] fixes = descriptor.getFixes();
            if (fixes != null && fixes.length > 0) {
                quickFix = fixes[0].getName();
            }
            
            return new InspectionProblem(
                inspectionName,
                description,
                severity,
                line,
                column,
                quickFix
            );
            
        } catch (Exception e) {
            logger.debug("转换问题描述符时发生错误: {}", e.getMessage());
            return new InspectionProblem(
                inspectionName,
                "无法获取问题描述",
                "INFO",
                1,
                1,
                ""
            );
        }
    }
    
    /**
     * 将高亮类型映射为严重程度
     */
    private String mapHighlightTypeToSeverity(ProblemHighlightType highlightType) {
        if (highlightType == null) {
            return "INFO";
        }
        
        switch (highlightType) {
            case ERROR:
                return "ERROR";
            case WARNING:
                return "WARNING";
            case WEAK_WARNING:
                return "WEAK_WARNING";
            case INFORMATION:
                return "INFO";
            case GENERIC_ERROR_OR_WARNING:
                return "WARNING";
            default:
                return "INFO";
        }
    }
}
